<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class AddRoles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:updateBaseRoles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $users = User::whereDoesntHave('roles')->get();

        foreach ($users as $user) {
            $user->assignRole('store assistant');
        }

        return Command::SUCCESS;
    }
}
