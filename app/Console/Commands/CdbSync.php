<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class CdbSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:cdbSync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $databasesNames = ['countries', 'currencies', 'diagrams', 'order-groups', 'centres', 'seasons'];

        foreach ($databasesNames as $databaseName) {
            Artisan::call("cdb:sync $databaseName");
        }


        return Command::SUCCESS;
    }
}
