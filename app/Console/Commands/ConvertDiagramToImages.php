<?php

namespace App\Console\Commands;

use App\Models\Claim;
use App\Services\FileService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Storage;

class ConvertDiagramToImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:convertImages';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $fileService = new FileService();
        $claims = Claim::where('created_at', '>=', Carbon::createFromFormat('Y-m-d', '2023-11-27')->startOfDay())
            ->whereHas('claimArticle', function (Builder $query) {
                $query->whereNotNull(['diagram_id', 'diagram_coordinates']);
            })
            ->with('claimArticle')->get();
        

        foreach ($claims as $claim) {
            $img = $fileService->createImageFromCoordinates(
                diagramId: $claim->claimArticle->diagram_id,
                diagramCoordinates: $claim->claimArticle->diagram_coordinates
            );

            Storage::disk($fileService->getPhotosDisk())->put($claim->claim_code . '/diagram.png', $img);
        }
        return Command::SUCCESS;
    }
}
