<?php

namespace App\Console\Commands;

use App\Models\ApiUser;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class CreateApiUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:api_user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $token = Str::random(60);

        $user = ApiUser::create([
            'name' => 'retail',
        ]);

        $user->forceFill([
            'api_token' => hash('sha256', $token),
        ])->save();

        echo 'User was created.';
        echo $user;
    }
}
