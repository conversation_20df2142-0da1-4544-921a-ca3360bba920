<?php

namespace App\Console\Commands;

use App\Jobs\Deactivate3R01ClaimsJob;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\Status;
use Carbon\Carbon;
use Illuminate\Console\Command;

class Deactivate3R01ClaimsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:deactivate-3R01-claims-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        Deactivate3R01ClaimsJob::dispatch();
    }
}
