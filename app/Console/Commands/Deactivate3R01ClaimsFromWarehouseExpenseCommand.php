<?php

namespace App\Console\Commands;

use App\Imports\BaseExcelFileImport;
use App\Jobs\Deactivate3R01ClaimsJob;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\ClaimHistoryLog;
use App\Models\Status;
use App\Models\User;
use App\Services\EsoApi\EsoService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;

class Deactivate3R01ClaimsFromWarehouseExpenseCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:deactivate-3R01-claims-from-warehouse-expense';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $esoService = new EsoService();
        $res =$esoService->getWarehouseExpense("2361250547");

        if ($res->hasErrors()){
            dd('ESO connection ERROR');
        }

        $articles = $res->getResponse()->data->vydejka[0]->artikle;

        $formatedArray = [];

        foreach ($articles as $row) {
            $formatedArray[$row->goods_id] = [
                'goodsid' => $row->goods_id,
                'count' => $row->pocet,
            ];
        }

        $uuid = Str::uuid()->toString();
        $actionUserId = User::where('login', 'u3985')->first()->id;


        $claimsForUpdate = [];

        $claims = Claim::whereHas('claimArticle.article', function ($query) use ($formatedArray) {
            $query->whereIn('goodsid', array_keys($formatedArray));
        })
            ->where('warehouse_centre_id', Centre::Warehouse_3R01)
            ->where('warehouse_status_id', Status::WAREHOUSE_STATUS_SEND_TO_3R01)
            ->where('claim_status_id', '!=', Status::CLAIM_STATUS_CANCELED)
            ->where('is_active', 1)
            ->with('claimArticle.article')
            ->oldest('id')
            ->get();

        foreach ($claims as $claim) {
            $goodsid = $claim->claimArticle->article->goodsid;

            dump("------------------------------------------");
            dump("GOODS ID: $goodsid");
            dump("Before:", $formatedArray[$goodsid] ?? 'N/A');

            $match = $formatedArray[$goodsid] ?? null;

            if ($match) {
                dump("MATCH FOUND");
                $claimsForUpdate[] = $claim;


                if ($claim->claimArticle->quantity >= $match['count']) {
                    unset($formatedArray[$goodsid]);
                } else{
                    $formatedArray[$goodsid]['count'] =  $match['count'] - $claim->claimArticle->quantity;
                }

                dump("After:", $formatedArray[$goodsid] ?? 'UNSET');
                dump("------------------------------------------");
            }
        }
        dump("------------------------------------------");
        dump("NOT FOUND FORMATTED ARTICLES");
        dump($formatedArray);
        dump("------------------------------------------");
        $logs = [];

        foreach ($claimsForUpdate as $claim) {
            \Log::channel('closed_claims_3R01')->info("$claim->claim_code($claim->id) closed by admin");
            $timestamp = now();
            $logs[] = [
                'claim_id' => $claim->id,
                'claim_responsible_person_id' => $claim->claim_responsible_person_id,
                'action_user_id' => $actionUserId,
                'status_id' => Status::WAREHOUSE_STATUS_SOLD_ED,
                'action_type' => 'updated',
                'action' => 'claimWasDeactivated',
                'note' => "Claim was deactivated by admin. Ticket: https://tickets.vermont.eu/tickets/86264  UUID: $uuid ",
                'decision' => null,
                'created_at' => $timestamp,
                'updated_at' => $timestamp
            ];
        }

        ClaimHistoryLog::insert($logs);

        Claim::whereIn('id', array_column($claimsForUpdate,'id'))->update([
            'warehouse_status_id' => Status::WAREHOUSE_STATUS_SOLD_ED,
            'is_active' => 0
        ]);
    }
}
