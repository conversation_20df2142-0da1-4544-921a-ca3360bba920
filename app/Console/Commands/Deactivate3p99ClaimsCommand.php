<?php

namespace App\Console\Commands;

use App\Jobs\Deactivate3R01ClaimsJob;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\ClaimHistoryLog;
use App\Models\Status;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class Deactivate3p99ClaimsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:deactivate-3P99-claims-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {


        $allClaims = Claim::where('warehouse_centre_id', Centre::Warehouse_3P99_CRAZY_DAYS)
            ->where('warehouse_status_id', Status::WAREHOUSE_STATUS_SEND_TO_3P99)
            ->where('claim_status_id', '!=', Status::CLAIM_STATUS_CANCELED)
            ->where('is_active', 1)
            ->get();


        $actionUserId = User::where('login', 'u3985')->first()->id;
        $uuid = Str::uuid()->toString();

        $allClaims->chunk(1000)->each(function ($claims) use ($actionUserId, $uuid) {

            $logs = [];

            foreach ($claims as $claim) {

                dump("Claim $claim->claim_code on stock 3P99 was deactivated");

                $timestamp = now();
                $logs[] = [
                    'claim_id' => $claim->id,
                    'claim_responsible_person_id' => $claim->claim_responsible_person_id,
                    'action_user_id' => $actionUserId,
                    'status_id' => $claim->claim_status_id,
                    'action_type' => 'updated',
                    'action' => 'claimWasDeactivated',
                    'note' => "Claim was deactivated by admin. Note: https://tickets.vermont.eu/tickets/86294 UUID: $uuid ",
                    'decision' => null,
                    'created_at' => $timestamp,
                    'updated_at' => $timestamp
                ];
            }

            ClaimHistoryLog::insert($logs);

            Claim::whereIn('id', $claims->pluck('id'))->update([
                'is_active' => 0
            ]);

        });

    }
}
