<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class DeleteChunks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:chunksDelete';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Remove chunks from storage";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if (file_exists(storage_path('app/chunks'))) {
            $files = Storage::allFiles('chunks');
            Storage::delete($files);
        }
    }
}
