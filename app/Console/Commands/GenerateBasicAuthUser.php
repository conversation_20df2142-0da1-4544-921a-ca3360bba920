<?php

namespace App\Console\Commands;

use App\Models\BasicAuthUser;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class GenerateBasicAuthUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-basic-auth-user {name} {--desc=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $username = \Str::uuid()->toString();
        $password = \Str::uuid()->toString();

        BasicAuthUser::insert([
           'name' => $this->arguments()['name'],
           'description' => $this->option('desc') ?? null,
           'username'=>$username,
           'password'=>Hash::make($password),
           'created_at' => \Carbon\Carbon::now(),
           'updated_at' => \Carbon\Carbon::now(),
        ]);

        dump($username, $password);
    }
}
