<?php

namespace App\Console\Commands;

use App\Jobs\MarkNegativeReceiptsAsClaimsJob;
use App\Models\Claim;
use App\Services\EsoApi\WarehouseDocumentsService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class MarkNegativeReceiptsAsClaims extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:markNegativeReceiptsAsClaims';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        MarkNegativeReceiptsAsClaimsJob::dispatch(year: '2023');
    }
}
