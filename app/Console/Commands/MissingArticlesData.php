<?php

namespace App\Console\Commands;

use App\Jobs\ArticleSync;
use Illuminate\Console\Command;

class MissingArticlesData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:articlesSync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ArticleSync::dispatch();
    }
}
