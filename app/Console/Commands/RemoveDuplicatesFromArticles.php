<?php

namespace App\Console\Commands;

use App\Models\Article;
use App\Models\ClaimArticle;
use Illuminate\Console\Command;

class RemoveDuplicatesFromArticles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:removeDuplicatesFromArticles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $duplicatesGoodsIds = Article::select('goodsid')
            ->groupBy('goodsid')
            ->whereNotNull('goodsid')
            ->havingRaw('count(goodsid) > 1')
            ->get()
            ->pluck('goodsid')->toArray();


        $articlesForDelete = [];

        foreach ($duplicatesGoodsIds as $duplicatesGoodsId) {
            $duplicatesArticles = Article::where('goodsid', $duplicatesGoodsId)->get();
            $selectedArticle = $duplicatesArticles[0];
            $claimArticles = ClaimArticle::whereIn('article_id', $duplicatesArticles->pluck('id')->toArray())->get();

            if ($claimArticles->count() == 0) {
                Article::where('goodsid', $duplicatesGoodsId)->delete();
            } elseif ($claimArticles->count() == 1) {
                $usedArticle = $claimArticles->first();
                $unusedArticles = $duplicatesArticles->where('id', '!=', $usedArticle->article_id)->pluck(
                    'id'
                )->toArray();
                $articlesForDelete = array_merge($articlesForDelete, $unusedArticles);
            } elseif ($claimArticles->count() > 1) {
                $selectedArticle = $duplicatesArticles->first();

                foreach ($claimArticles as $claimArticle) {
                    $claimArticle->article_id = $selectedArticle->id;

                    $claimArticle->update([
                        'article_id' => $selectedArticle->id
                    ]);
                }

                $unusedArticles = $duplicatesArticles->where('id', '!=', $selectedArticle->id)->pluck('id')->toArray();
                $articlesForDelete = array_merge($articlesForDelete, $unusedArticles);
            }
        }
        Article::whereIn('id', $articlesForDelete)->delete();

        echo 'duplicates removed';
        return Command::SUCCESS;
    }
}
