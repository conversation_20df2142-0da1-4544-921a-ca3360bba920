<?php

namespace App\Console\Commands;

use App\Jobs\SuspicionOfSupplierClaimsInTenDaysJob;
use App\Jobs\SuspicionOfSupplierClaimsInThirtyDaysJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;


class SupplierClaimsCheck extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:supplierClaimsCheck';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Supplier claims check';


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Bus::chain([
            //new SuspicionOfSupplierClaimsInTenDaysJob(),
            new SuspicionOfSupplierClaimsInThirtyDaysJob(),
        ])->dispatch();

        $this->info('The command was successful!');
    }
}
