<?php

namespace App\Console\Commands;

use App\Mail\Survey\SurveyDeadlineMail;
use App\Mail\Survey\UnfinishedSurveysMail;
use App\Models\Status;
use App\Models\Survey;
use App\Notifications\InternalNotifications\SurveyNotifications\UnfinishedSurveysNotifications;
use App\Services\CentresService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class SurveysReminder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:surveysReminder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $centreService = new CentresService();
        $allWarehouseIds = $centreService->allWarehouses()->pluck('id')->toArray();

        $surveysInProgress = Survey::where('status_id', Status::SURVEY_IN_PROGRESS)
            ->with([
                'surveyCentres.centre',
                'surveyCentres' => function ($q) {
                    $q->where('status_id', Status::SURVEY_ARTICLE_UNCHECKED);
                }
            ])
            ->get();

        $today = Carbon::now()->startOfDay();
        foreach ($surveysInProgress as $survey) {
            $dueBy = Carbon::parse($survey->due_by)->endOfDay();
            //BT notifications
            if ($dueBy->diffInDays($today) == 0) {
                Mail::to(config('emailAddresses.bt.renata_kontarova'))
                    ->locale('sk')
                    ->send(new SurveyDeadlineMail($survey));
            } elseif ($today > $dueBy) {
                Mail::to(config('emailAddresses.bt.renata_kontarova'))
                    ->locale('sk')
                    ->send(new SurveyDeadlineMail($survey, true));
            }

            //Centres notifications
            if ($today > $dueBy || $dueBy->diffInDays($today) <= 1 || $dueBy->diffInDays($today) == 3) {
                $warehouses = $survey->surveyCentres->whereIn('centre_id', $allWarehouseIds);

                //TODO:: fixnut rozdelit na skladovy mail a notifikaciu centier
                if ($warehouses->isNotEmpty()) {
                    Mail::to(config('emailAddresses.claims_logistics'))
                        ->locale('sk')
                        ->send(new UnfinishedSurveysMail($survey, true));
                }

                foreach ($survey->surveyCentres->whereNotIn('centre_id', $allWarehouseIds) as $surveyCentre) {
                    $surveyCentre->centre->notify(
                        new UnfinishedSurveysNotifications($survey)
                    );
                }
            }
        }

        return Command::SUCCESS;
    }
}
