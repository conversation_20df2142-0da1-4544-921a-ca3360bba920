<?php

namespace App\Console\Commands;

use App\Jobs\TranslateManyDamageDescriptionsJob;
use App\Models\Claim;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class TranslateDamageDescription extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:translateDamageDescription';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Claim::whereHas('claimArticle', function (Builder $query) {
            $query->whereNotNull('damage_description');
            $query->whereNull('damage_description_eng');
        })->with('claimArticle', 'centre')->latest('id')->chunk(
            500,
            function ($claims) {
                TranslateManyDamageDescriptionsJob::dispatch($claims);
            }
        );


        echo 'Translate damage description job was dispatched';
        return Command::SUCCESS;
    }
}
