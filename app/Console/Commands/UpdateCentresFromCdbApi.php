<?php

namespace App\Console\Commands;

use App\Models\Centre;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class UpdateCentresFromCdbApi extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:updateCentres';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        function updateCentres($page)
        {
            $url = "https://cdb.vermont.eu/api/universal/centres/v1?page=$page&per_page=40&columns=id,code,hash,name,active,address_id&relations=itdata:centre_id,publicip;address.city;contact:centre_id,email";

            $response = Http::withBasicAuth(config('vermont.cdb_key'), config('vermont.cdb_secret'))
                ->get($url)
                ->object();


            $data = $response?->data;

            if (isset($data)) {
                foreach ($data as $centre) {
                    Centre::updateOrCreate([
                        'cdb_id' => $centre->id
                    ], [
                        'name' => $centre->name,
                        'code' => $centre->code,
                        'hash' => $centre->hash,
                        'street' => $centre->address->street,
                        'number' => $centre->address->number,
                        'zip' => $centre->address->zip,
                        'city' => $centre->address->city->name,
                        'country_id' => $centre->address->country_id,
                        'email' => $centre->contact->email ?? '',
                        'public_ip' => ($centre->itdata->publicip ? implode(', ', $centre->itdata->publicip) : null),
                        'is_active' => $centre->active,
                    ]);
                }
            }

            if ($response?->links?->next != null) {
                $page++;
                updateCentres($page);
            }
        }

        updateCentres(1, []);

        Cache::forget('centres');

        echo 'Centres updated';

        return Command::SUCCESS;
    }
}
