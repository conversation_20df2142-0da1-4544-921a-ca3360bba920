<?php

namespace App\Console\Commands;


use App\Enums\ClaimsTransports\CarrierStatusesEnum;
use App\Enums\ClaimsTransports\ClaimsTransportsLogActionsEnum;
use App\Enums\ClaimsTransports\ClaimsTransportsStatusesEnum;
use App\Enums\Decisions\SupplierClaimDecisionsEnum;
use App\Enums\Logs\LogActionTypes;
use App\Jobs\GetB2BWarehouseDocumentsJob;
use App\Jobs\GetB2CWarehouseDocumentsForEshopClaimsJob;
use App\Mail\Reports\NewReportForCheckMail;
use App\Mail\Reports\ReportWasCreditedMail;
use App\Models\Article;
use App\Models\BasicAuthUser;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\ClaimArticle;
use App\Models\ClaimHistoryLog;
use App\Models\ClaimsTransport;
use App\Models\ClaimsWithoutDamageTypesStatistic;
use App\Models\ClaimType;
use App\Models\Decision;
use App\Models\NotificationClass;
use App\Models\QueueLog;
use App\Models\Report;
use App\Models\Status;
use App\Models\SupplierClaimArticle;
use App\Models\Survey;
use App\Models\User;
use App\Services\Logs\LogsService;
use App\Services\VermontApiService;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;


class UpdateDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:updateDatabase';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $b2cClaims = Claim::where('claim_type_id', ClaimType::CLAIM_TYPE_B2B_B2C_WAREHOUSE_RETURNS)
            ->whereNotNull('warehouse_status_id')
            ->where('claim_code', 'like', 'cl-vsersk%')
            ->whereHas('b2bB2cWarehouseDocument', function ($query) {
                $query->whereNotNull('eshop_invoice_number');
            })
            ->with(['claimArticle:id,article_id', 'claimArticle.article:id,goodsid', 'b2bB2cWarehouseDocument:id,claim_id,eshop_invoice_number'])
            ->oldest('id')
            ->get();


        $uuid = Str::uuid()->toString();
        $userId = User::where('login', 'u3985')->first()->id;


        foreach ($b2cClaims as $b2cClaim) {

            $claim = Claim::whereIn('claim_status_id', [Status::CLAIM_STATUS_CREATED, Status::CLAIM_STATUS_IN_TRANSPORT])
                ->where('claim_type_id', ClaimType::CLAIM_TYPE_CUSTOMER_ESHOP)
                ->wherehas('claimArticle.article', function ($query) use ($b2cClaim) {
                    $query->where('goodsid', $b2cClaim->claimArticle->article->goodsid);
                })
                ->whereHas('purchaseDocument', function ($query) use ($b2cClaim) {
                    $query->where('invoice_number', $b2cClaim->b2bB2cWarehouseDocument->eshop_invoice_number);
                })
                ->first();

            if ($claim) {

                dump("$b2cClaim->claim_code({$b2cClaim->id})  -->  $claim->claim_code({$claim->id})");

                $claim->fill([
                    'claim_status_id' => Status::CLAIM_STATUS_WAREHOUSE_RECEIVED,
                    'warehouse_received_at' => now(),
                    'is_active' => 0,
                ])->save();

                $claim->createHistoryLog(
                    'updated',
                    'receivedAtWarehouse',
                    "Updated by admin. B2C Return: $b2cClaim->claim_code, UUID: $uuid ",
                );

                $b2cClaim->createHistoryLog(
                    'updated',
                    'originalEshopClaimReceivedAtWarehouse',
                    "Updated by admin. Eshop claim: $claim->claim_code, UUID: $uuid ",
                );
            } else {
                //dump("$b2cClaim->claim_code  -->  N/A");
            }

            //sleep(1);
        }

    }
}
