<?php

namespace App\Console\Commands;

use App\Models\Claim;
use App\Models\Status;
use App\Services\FileService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class deactiveAll3R01 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:deactive-all3r01';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(FileService $fileService)
    {
        DB::statement('
            INSERT INTO `claim_history_logs`(claim_id, claim_responsible_person_id, status_id, action_type, `action`, note, created_at, updated_at)
            SELECT claims.id AS claim_id,
                   claims.claim_responsible_person_id,
                   ' . Status::WAREHOUSE_STATUS_SEND_TO_BANDIT. ' AS status_id,
                   "updated" AS action_type,
                   "claimWasSoldToBandit" AS `action`,
                   "command:deactive-all3r01" as note,
                   NOW() AS created_at,
                   NOW() AS updated_at
            FROM claims
            WHERE warehouse_status_id = ' . Status::WAREHOUSE_STATUS_SEND_TO_3R01. '
        ');

        $updated = Claim::where('warehouse_status_id',  Status::WAREHOUSE_STATUS_SEND_TO_3R01)->update([
            'is_active' => 0,
            'warehouse_status_id' => Status::WAREHOUSE_STATUS_SEND_TO_BANDIT
        ]);

        //TODO fotky po 30 dnoch cron

        echo $updated . " claims with status Sent to 3R01 were deactivated\n";
    }
}
