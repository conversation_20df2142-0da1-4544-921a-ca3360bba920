<?php

namespace App\Console;

use App\Jobs\DeletePhotosAfterNinetyDaysJob;
use App\Jobs\DeletePhotosAfterThirtyDaysJob;
use App\Jobs\GetB2BWarehouseDocumentsJob;
use App\Jobs\GetB2CWarehouseDocumentsForEshopClaimsJob;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('command:chunksDelete')->lastDayOfMonth('23:55');
        $schedule->command('command:articlesSync')->dailyAt('08:00');
        $schedule->command('command:supplierClaimsCheck')->dailyAt('07:00');
        $schedule->command('command:cdbSync')->dailyAt('01:00');
        $schedule->job(new DeletePhotosAfterThirtyDaysJob)->dailyAt('02:00');
        $schedule->job(new DeletePhotosAfterNinetyDaysJob)->dailyAt('02:10');
        //$schedule->job(new GetB2BWarehouseDocumentsJob())->dailyAt('04:00');
        //$schedule->job(new GetB2CWarehouseDocumentsForEshopClaimsJob())->dailyAt('04:00');
        $schedule->command('command:surveysReminder')->dailyAt('07:00');
        $schedule->command('sanctum:prune-expired --hours=24')->daily();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
