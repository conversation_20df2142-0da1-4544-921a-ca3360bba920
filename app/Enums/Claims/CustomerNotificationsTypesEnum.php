<?php

namespace App\Enums\Claims;


use Collator;

enum CustomerNotificationsTypesEnum: string
{
    case CONTACTED_BY_PHONE = 'contactedByPhone';
    case SMS = 'contactedBySms';
    case SENT_EMAIL_FROM_CENTRE = 'sentEmailFromCentre';

    case OTHER = 'other';


    public static function valuesToArray(): array
    {
        $array = [];
        foreach (self::cases() as $case) {
            $array[] = $case->value;
        }
        return $array;
    }

    public static function translatedValues(): array
    {
        return [
            ['value' => self::CONTACTED_BY_PHONE->value, 'name' => __('solution.enums.contactedByPhone'),],
            ['value' => self::SMS->value, 'name' => __('solution.enums.contactedBySms'),],
            ['value' => self::SENT_EMAIL_FROM_CENTRE->value, 'name' => __('solution.enums.sentEmailFromCentre'),],
            ['value' => self::OTHER->value, 'name' => __('solution.enums.other'),],
        ];
    }


    public function translate(): ?string
    {
        return match ($this) {
            self::CONTACTED_BY_PHONE => __('solution.enums.contactedByPhone'),
            self::SMS => __('solution.enums.contactedBySms'),
            self::SENT_EMAIL_FROM_CENTRE => __('solution.enums.sentEmailFromCentre'),
            self::OTHER => __('solution.enums.other'),
            default => '',
        };
    }

    private static function getCollator(): Collator
    {
        $locales = [
            'sk' => 'sk_SK',
            'cz' => 'cs_CZ',
            'hu' => 'hu_HU',
            'en' => 'en_US'
        ];

        return collator_create($locales[app()->getLocale()] ?? 'en_US');
    }
}
