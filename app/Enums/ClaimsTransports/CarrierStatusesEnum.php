<?php

namespace App\Enums\ClaimsTransports;


use App\Models\Status;
use App\Models\User;
use Collator;

enum CarrierStatusesEnum: int
{
    case ORDERED = 1;
    case SENT = 2;
    case RECEIVED_AT_WAREHOUSE = 3;
    case CANCELED = 4;

    public function translate(): ?string
    {
        return match ($this) {
            self::ORDERED => __('claimsTransports.carrierStatuses.ordered'),
            self::SENT => __('claimsTransports.carrierStatuses.sent'),
            self::RECEIVED_AT_WAREHOUSE => __('claimsTransports.carrierStatuses.receivedAtWarehouse'),
            self::CANCELED => __('claimsTransports.carrierStatuses.canceled'),
        };
    }

    public static function valuesToArray(bool $sort = true): array
    {
        $array = array_map(fn($case) => $case->value, self::cases());

        if ($sort) {
            usort($array, fn($a, $b) => collator_compare(self::getCollator(), $a, $b));
        }

        return $array;
    }

    private static function getCollator(): Collator
    {
        $locales = [
            'sk' => 'sk_SK',
            'cz' => 'cs_CZ',
            'hu' => 'hu_HU',
            'en' => 'en_US'
        ];

        return collator_create($locales[app()->getLocale()] ?? 'en_US');
    }
}
