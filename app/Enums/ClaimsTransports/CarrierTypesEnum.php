<?php

namespace App\Enums\ClaimsTransports;


use App\Models\Status;
use App\Models\User;
use Collator;
use Illuminate\Support\Facades\Vite;

enum CarrierTypesEnum: string
{
    case VERMONT_INTERNAL_TRANSPORT = 'VERMONT Internal Transport';
    case DPD_SK = 'DPD.SK';
    case PPL_CZ = 'PPL.CZ';
    case DPD_HU = 'DPD.HU';


    public function translate(): ?string
    {
        return match ($this) {
            self::VERMONT_INTERNAL_TRANSPORT => __('claimsTransports.carrierTypes.internal'),
            self::DPD_SK => __('claimsTransports.carrierTypes.dpdSk'),
            self::PPL_CZ => __('claimsTransports.carrierTypes.pplCz'),
            self::DPD_HU => __('claimsTransports.carrierTypes.dpdHu'),
        };
    }

    public static function valuesToArray(bool $sort = true): array
    {
        $array = array_map(fn($case) => $case->value, self::cases());

        if ($sort) {
            usort($array, fn($a, $b) => collator_compare(self::getCollator(), $a, $b));
        }

        return $array;
    }

    private static function getCollator(): Collator
    {
        $locales = [
            'sk' => 'sk_SK',
            'cz' => 'cs_CZ',
            'hu' => 'hu_HU',
            'en' => 'en_US'
        ];

        return collator_create($locales[app()->getLocale()] ?? 'en_US');
    }

    public static function translatedValues(bool $sortByName = true): array
    {
        $values = [
            ['value' => self::VERMONT_INTERNAL_TRANSPORT->value, 'name' => __('claimsTransports.carrierTypes.internal'),],
            ['value' => self::DPD_SK->value, 'name' => __('claimsTransports.carrierTypes.dpdSk')],
            ['value' => self::PPL_CZ->value, 'name' => __('claimsTransports.carrierTypes.pplCz')],
            ['value' => self::DPD_HU->value, 'name' => __('claimsTransports.carrierTypes.dpdHu')],
        ];

        if ($sortByName) {
            $collator = self::getCollator();

            usort($values, function ($a, $b) use ($collator) {
                return $collator->compare($a['name'], $b['name']);
            });
        }

        return $values;
    }

    public function translatedWithIcon(): ?string
    {
        return match ($this) {
            self::VERMONT_INTERNAL_TRANSPORT => $this->setIcon('resources/images/claimsTransports/parcel_vermont-min.svg') . " " . __('claimsTransports.carrierTypes.internal'),
            self::DPD_SK => $this->setIcon('resources/images/claimsTransports/parcel_dpd-min.svg') . " " . __('claimsTransports.carrierTypes.dpdSk'),
            self::PPL_CZ => $this->setIcon('resources/images/claimsTransports/parcel_ppl-min.svg') . " " . __('claimsTransports.carrierTypes.pplCz'),
            self::DPD_HU => $this->setIcon('resources/images/claimsTransports/parcel_dpd-min.svg') . " " . __('claimsTransports.carrierTypes.dpdHu'),
        };
    }

    private function setIcon(string $path): string
    {
        return '<img style="max-width:20px" src="' . Vite::asset($path) . '">';
    }


}
