<?php

namespace App\Enums\ClaimsTransports;


use App\Models\Status;
use App\Models\User;
use Collator;
use Illuminate\Support\Facades\Vite;

enum ClaimsTransportTypesEnum: string
{
    case ECOM_CUSTOMER = 'ecom_customer';
    case INTERNAL_AND_CUSTOMER = 'customer_and_internal';
    case SUPPLIER = 'supplier';

    public function translate(): ?string
    {
        return match ($this) {
            self::ECOM_CUSTOMER => __('claimsTransports.types.ecom'),
            self::INTERNAL_AND_CUSTOMER => __('claimsTransports.types.internalAndCustomer'),
            self::SUPPLIER => __('claimsTransports.types.supplier'),
        };
    }

    public static function valuesToArray(bool $sort = true): array
    {
        $array = array_map(fn($case) => $case->value, self::cases());

        if ($sort) {
            usort($array, fn($a, $b) => collator_compare(self::getCollator(), $a, $b));
        }

        return $array;
    }

    private static function getCollator(): Collator
    {
        $locales = [
            'sk' => 'sk_SK',
            'cz' => 'cs_CZ',
            'hu' => 'hu_HU',
            'en' => 'en_US'
        ];

        return collator_create($locales[app()->getLocale()] ?? 'en_US');
    }

    public static function translatedValues(bool $sortByName = true): array
    {
        $values = [
            ['value' => self::ECOM_CUSTOMER->value, 'name' => __('claimsTransports.types.ecom')],
            ['value' => self::INTERNAL_AND_CUSTOMER->value, 'name' => __('claimsTransports.types.internalAndCustomer')],
            ['value' => self::SUPPLIER->value, 'name' => __('claimsTransports.types.supplier')],
        ];

        if ($sortByName) {
            $collator = self::getCollator();

            usort($values, function ($a, $b) use ($collator) {
                return $collator->compare($a['name'], $b['name']);
            });
        }

        return $values;
    }

    public function translatedWithIcon(): ?string
    {
        return match ($this) {
            self::ECOM_CUSTOMER => $this->setIcon('claims claims-claims-eshop opacity-50') . " " . __('claimsTransports.types.ecom'),
            self::INTERNAL_AND_CUSTOMER => $this->setIcon('claims claims-claims-zakaznik opacity-50') . " " . __('claimsTransports.types.internalAndCustomer'),
            self::SUPPLIER => $this->setIcon('claims claims-claims-dodavatelska opacity-50') . " " . __('claimsTransports.types.supplier'),
        };
    }

    private function setIcon(string $path): string
    {
        return '<i class="' . $path . '"></i>';
    }
}
