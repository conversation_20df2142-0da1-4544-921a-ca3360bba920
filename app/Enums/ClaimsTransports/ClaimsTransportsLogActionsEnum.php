<?php

namespace App\Enums\ClaimsTransports;


use App\Models\Status;
use App\Models\User;
use Collator;
use Illuminate\Support\Facades\Vite;

enum ClaimsTransportsLogActionsEnum: int
{
    case CREATED = 1;
    case UPDATED = 2;
    case CLAIM_ADDED_TO_TRANSPORT = 3;
    case CLAIM_REMOVED_FROM_TRANSPORT = 4;
    case PHYSICALLY_CHECKED = 5;
    case STATUS_UPDATED = 6;
    case TRANSPORT_ORDERED = 7;
    case TRANSPORT_HUB_CONFIRMED_ORDER = 8;
    case TRANSPORT_HUB_CANCEL_ORDER = 9;
    case SENT_TO_WAREHOUSE = 10;
    case DELETED = 11;
    case REORDERED_TO_EXTERNAL = 12;


    public function translate(): ?string
    {
        return match ($this) {
            self::CREATED => __('claimsTransports.logs.created'),
            self::CLAIM_ADDED_TO_TRANSPORT => __('claimsTransports.logs.claimAddedToTransport'),
            self::CLAIM_REMOVED_FROM_TRANSPORT => __('claimsTransports.logs.claimRemovedFromTransport'),
            self::PHYSICALLY_CHECKED => __('claimsTransports.logs.physicalChecked'),
            self::STATUS_UPDATED => __('claimsTransports.logs.statusUpdated'),
            self::TRANSPORT_HUB_CONFIRMED_ORDER => __('claimsTransports.logs.transportHubConfirmedOrder'),
            self::TRANSPORT_HUB_CANCEL_ORDER => __('claimsTransports.logs.transportHubCancelOrder'),
            self::TRANSPORT_ORDERED => __('claimsTransports.logs.transportOrdered'),
            self::SENT_TO_WAREHOUSE => __('claimsTransports.logs.sentToWarehouse'),
            self::DELETED => __('claimsTransports.logs.deleted'),
            self::REORDERED_TO_EXTERNAL => __('claimsTransports.logs.reorderedToExternal'),
            default => ""
        };
    }

    public static function valuesToArray(bool $sort = true): array
    {
        $array = array_map(fn($case) => $case->value, self::cases());

        if ($sort) {
            usort($array, fn($a, $b) => collator_compare(self::getCollator(), $a, $b));
        }

        return $array;
    }

    private static function getCollator(): Collator
    {
        $locales = [
            'sk' => 'sk_SK',
            'cz' => 'cs_CZ',
            'hu' => 'hu_HU',
            'en' => 'en_US'
        ];

        return collator_create($locales[app()->getLocale()] ?? 'en_US');
    }

    public static function translatedValues(bool $sortByName = true): array
    {
        $values = [
            ['value' => self::CREATED, 'name' => __('claimsTransports.logs.created')],
            ['value' => self::CLAIM_ADDED_TO_TRANSPORT, 'name' => __('claimsTransports.logs.claimAddedToTransport')],
            ['value' => self::CLAIM_REMOVED_FROM_TRANSPORT, 'name' => __('claimsTransports.logs.claimRemovedFromTransport')],
            ['value' => self::PHYSICALLY_CHECKED, 'name' => __('claimsTransports.logs.physicalChecked')],
            ['value' => self::STATUS_UPDATED, 'name' => __('claimsTransports.logs.statusUpdated')],
            ['value' => self::TRANSPORT_HUB_CONFIRMED_ORDER, 'name' => __('claimsTransports.logs.transportHubConfirmedOrder')],
        ];

        if ($sortByName) {
            $collator = self::getCollator();

            usort($values, function ($a, $b) use ($collator) {
                return $collator->compare($a['name'], $b['name']);
            });
        }

        return $values;
    }
}
