<?php

namespace App\Enums\ClaimsTransports;


use App\Models\Status;
use App\Models\User;
use Collator;

enum ClaimsTransportsStatusesEnum: int
{
    case CREATED = 1;
    case WAITING_FOR_PROCESSING = 2;
    case ORDERED = 3;
    //PROCESSED
    case SENT = 4;
    case RECEIVED_AT_WAREHOUSE = 5;
    case CANCELED = 6;


    public function translate(): ?string
    {
        return match ($this) {
            self::CANCELED => __('claimsTransports.statuses.canceled'),
            self::CREATED => __('claimsTransports.statuses.created'),
            self::WAITING_FOR_PROCESSING => __('claimsTransports.statuses.waitingForProcessing'),
            self::ORDERED => __('claimsTransports.carrierStatuses.ordered'),
            self::SENT => __('claimsTransports.statuses.sent'),
            self::RECEIVED_AT_WAREHOUSE => __('claimsTransports.statuses.receivedAtWarehouse'),
            default => null,
        };
    }

    public static function valuesToArray(bool $sort = true): array
    {
        $array = array_map(fn($case) => $case->value, self::cases());

        if ($sort) {
            usort($array, fn($a, $b) => collator_compare($this->getCollator(), $a, $b));
        }

        return $array;
    }

    private static function getCollator(): Collator
    {
        $locales = [
            'sk' => 'sk_SK',
            'cz' => 'cs_CZ',
            'hu' => 'hu_HU',
            'en' => 'en_US'
        ];

        return collator_create($locales[app()->getLocale()] ?? 'en_US');
    }

    public static function translatedValues(bool $sortByName = true): array
    {
        $values = [
            ['value' => self::CANCELED->value, 'name' => __('claimsTransports.statuses.canceled'),],
            ['value' => self::CREATED->value, 'name' => __('claimsTransports.statuses.created'),],
            ['value' => self::WAITING_FOR_PROCESSING->value, 'name' => __('claimsTransports.statuses.waitingForProcessing')],
            ['value' => self::ORDERED->value, 'name' => __('claimsTransports.statuses.ordered')],
            ['value' => self::RECEIVED_AT_WAREHOUSE->value, 'name' => __('claimsTransports.statuses.receivedAtWarehouse')],
        ];

        if ($sortByName) {
            $collator = self::getCollator();

            usort($values, function ($a, $b) use ($collator) {
                return $collator->compare($a['name'], $b['name']);
            });
        }

        return $values;
    }

    public function translatedWithIcon(): ?string
    {
        return match ($this) {
            self::ORDERED => $this->setIcon('claims claims-okay text-success ') . " " . __('claimsTransports.statuses.ordered'),
            self::WAITING_FOR_PROCESSING => $this->setIcon('claims claims-clock-outline text-secondary opacity-50') . " " . __('claimsTransports.statuses.waitingForProcessing'),
            self::RECEIVED_AT_WAREHOUSE => $this->setIcon('claims claims-warehouse text-secondary opacity-50') . " " . __('claimsTransports.statuses.receivedAtWarehouse'),
            self::CREATED => $this->setIcon('claims claims-edit-text-outline text-primary ') . " " . __('claimsTransports.statuses.created'),
            self::CANCELED => $this->setIcon('claims claims-close text-danger ') . " " . __('claimsTransports.statuses.canceled'),
        };
    }

    private function setIcon(string $path): string
    {
        return '<i class="' . $path . '"></i>';
    }
}
