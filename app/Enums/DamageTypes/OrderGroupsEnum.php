<?php

namespace App\Enums\DamageTypes;


enum OrderGroupsEnum: int
{
    case TEXTILE_AND_ACC = 1;
    case L1_SHOES = 2;
    case OTHER = 3;


    public static function valuesToArray(): array
    {
        $array = [];
        foreach (self::cases() as $case) {
            $array[] = $case->value;
        }
        return $array;
    }

    public function translate(): ?string
    {
        return match ($this) {
            self::TEXTILE_AND_ACC => __('administration.damageTypes.textileAndAcc'),
            self::L1_SHOES => __('administration.damageTypes.shoes'),
            self::OTHER => __('administration.damageTypes.other'),
            default => null,
        };
    }

    public static function translatedAvailableValues(): array
    {
        return [
            self::TEXTILE_AND_ACC->value => __('administration.damageTypes.textileAndAcc'),
            self::L1_SHOES->value => __('administration.damageTypes.shoes')
        ];
    }
}
