<?php

namespace App\Enums\Decisions;


use App\Models\Status;
use App\Models\User;

enum ClaimsDecisionsEnum: int
{
    case ACCEPTED = 1;
    case REJECTED = 2;
    case SEND_FOR_REVIEW = 3;
    case REQUEST_FOR_REVIEW = 4;
    case SEND_FOR_FIBER_INSTITUTE = 5;
    case FILLED_INCORRECTLY = 6;
    case SEND_TO_GUARANTOR = 7;


    public static function valuesToArray(): array
    {
        $array = [];
        foreach (self::cases() as $case) {
            $array[] = $case->value;
        }
        return $array;
    }

    public static function getDecisionsByPermissions(User $user, ?int $decisionId): array
    {
        return match (true) {
            $user->hasPermissionTo('claims review') => [
                self::ACCEPTED->value => self::ACCEPTED->translate(),
                self::REJECTED->value => self::REJECTED->translate(),
                self::REQUEST_FOR_REVIEW->value => self::REQUEST_FOR_REVIEW->translate(),
                self::SEND_FOR_FIBER_INSTITUTE->value => self::SEND_FOR_FIBER_INSTITUTE->translate(),
                self::FILLED_INCORRECTLY->value => self::FILLED_INCORRECTLY->translate(),
            ],
            !$user->hasPermissionTo('claims review') && $decisionId == self::REQUEST_FOR_REVIEW->value => [
                self::SEND_TO_GUARANTOR->value => self::SEND_TO_GUARANTOR->translate(),
            ],
            !$user->hasPermissionTo('claims review') => [
                self::ACCEPTED->value => self::ACCEPTED->translate(),
                self::REJECTED->value => self::REJECTED->translate(),
                self::SEND_FOR_REVIEW->value => self::SEND_FOR_REVIEW->translate(),
            ],
            default => null,
        };
    }


    public function translate(): ?string
    {
        return match ($this) {
            self::ACCEPTED => __('decision.accepted'),
            self::REJECTED => __('decision.reject'),
            self::SEND_FOR_REVIEW => __('decision.send-for-review'),
            self::REQUEST_FOR_REVIEW => __('decision.request-for-review'),
            self::SEND_FOR_FIBER_INSTITUTE => __('decision.send-for-fiber-institute'),
            self::FILLED_INCORRECTLY => __('decision.filled-incorrectly'),
            self::SEND_TO_GUARANTOR => __('decision.send-to-guarantor'),
            default => null,
        };
    }

    public static function translatedValues(): array
    {
        return [
            self::ACCEPTED->value => __('decision.accepted'),
            self::REJECTED->value => __('decision.reject'),
            self::SEND_FOR_REVIEW->value => __('decision.send-for-review'),
            self::REQUEST_FOR_REVIEW->value => __('decision.request-for-review'),
            self::SEND_FOR_FIBER_INSTITUTE->value => __('decision.send-for-fiber-institute'),
            self::FILLED_INCORRECTLY->value => __('decision.filled-incorrectly'),
            self::SEND_TO_GUARANTOR->value => __('decision.send-to-guarantor'),
        ];
    }

    public static function getClaimStatusByDecision(int $decision, int $claimStatus): int
    {
        return match (true) {
            self::ACCEPTED->value === $decision => Status::CLAIM_STATUS_ACCEPTED,
            self::REJECTED->value === $decision && $claimStatus == Status::CLAIM_STATUS_GUARANTOR_RECEIVED => Status::CLAIM_STATUS_SENT_TO_CENTRE,
            self::REJECTED->value === $decision && !auth()->user()->hasPermissionTo('claims review') => Status::CLAIM_STATUS_PENDING_ON_REVIEW,
            self::REJECTED->value === $decision => Status::CLAIM_STATUS_REJECTED,
            in_array($decision, [self::SEND_FOR_REVIEW->value, self::SEND_FOR_FIBER_INSTITUTE->value]) => Status::CLAIM_STATUS_PENDING_ON_REVIEW,
            self::REQUEST_FOR_REVIEW->value === $decision => Status::CLAIM_STATUS_REQUESTED_FOR_PHYSICAL_ASSESSMENT,
            self::FILLED_INCORRECTLY->value === $decision => Status::CLAIM_STATUS_SIGNED,
            self::SEND_TO_GUARANTOR->value === $decision => Status::CLAIM_STATUS_SENT_TO_GUARANTOR,
            default => null,
        };
    }
}
