<?php

namespace App\Enums\Decisions;


use App\Models\Status;
use App\Models\User;

enum SupplierClaimDecisionsEnum: int
{
    case CONFIRM_ALL = 1;
    case CONFIRM_DAMAGED = 2;
    case REJECT = 3;


    public static function valuesToArray(): array
    {
        $array = [];
        foreach (self::cases() as $case) {
            $array[] = $case->value;
        }
        return $array;
    }
    public static function withoutRejected(): array
    {
        return [
            self::CONFIRM_ALL,
            self::CONFIRM_DAMAGED
        ];
    }

    public function translate(): ?string
    {
        return match ($this) {
            self::CONFIRM_ALL => __('decision.withdraw-all-articles'),
            self::CONFIRM_DAMAGED => __('decision.withdraw-damaged-articles'),
            self::REJECT => __('decision.reject'),
            default => null,
        };
    }
}
