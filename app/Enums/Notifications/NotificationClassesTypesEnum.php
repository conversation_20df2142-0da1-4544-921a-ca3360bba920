<?php
namespace App\Enums\Notifications;


enum NotificationClassesTypesEnum:string
{
    case MAIL = 'mail';
    case NOTIFICATION_INTERNAL = 'notification_internal';
    case NOTIFICATION_CUSTOMER = 'notification_customer';


    public static function valuesToArray(): array
    {
        $array = [];
        foreach (self::cases() as $case) {
            $array[] = $case->value;
        }
        return $array;
    }


    public function translate(): ?string
    {
        return match($this)
        {
            self::MAIL => __('administration.notifications.mail'),
            self::NOTIFICATION_INTERNAL => __('administration.notifications.notificationInternal'),
            self::NOTIFICATION_CUSTOMER => __('administration.notifications.notificationCustomer'),
            default => null,
        };
    }
}
