<?php

namespace App\Enums\Notifications;


use App\Models\Department;
use App\Models\Recipient;
use App\Models\User;

enum RecipientsTypesEnum: string
{
    case USERS = 'users';
    case DEPARTMENTS = 'departments';


    public static function valuesToArray(): array
    {
        $array = [];
        foreach (self::cases() as $case) {
            $array[] = $case->value;
        }
        return $array;
    }

    public static function classes(): array
    {
        return [
            self::USERS->value => User::class,
            self::DEPARTMENTS->value => Recipient::class,
        ];
    }

    public static function getClass(string $type): string
    {
        return match ($type) {
            self::USERS->value => User::class,
            self::DEPARTMENTS->value => Department::class,
            default => null,
        };
    }

    public static function getNameByClass(string $class): RecipientsTypesEnum
    {
        return match ($class) {
            User::class =>self::USERS,
            Department::class=>self::DEPARTMENTS,
            default => null,
        };
    }

    public static function translatedValues(): array
    {
        return [
            self::USERS->value => __('general.user'),
            self::DEPARTMENTS->value => __('administration.notifications.department'),
        ];
    }


    public function translate(): ?string
    {
        return match ($this) {
            self::USERS => __('administration.notifications.user'),
            self::DEPARTMENTS => __('administration.notifications.department'),
            default => null,
        };
    }
}
