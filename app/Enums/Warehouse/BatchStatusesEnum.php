<?php

namespace App\Enums\Warehouse;


use App\Models\Status;
use App\Models\User;
use Collator;
use Illuminate\Support\Facades\Vite;

enum BatchStatusesEnum: int
{
    case CREATED = 1;
    case SENT_TO_CLEANERS = 2;
    case RECEIVED_FROM_CLEANERS = 3;


    public function translate(): ?string
    {
        return match ($this) {
            self::CREATED => __('batches.enums.created'),
            self::SENT_TO_CLEANERS => __('batches.enums.sentToCleaners'),
            self::RECEIVED_FROM_CLEANERS => __('batches.enums.receivedFromCleaners'),

        };
    }

    public static function valuesToArray(bool $sort = true): array
    {
        $array = array_map(fn($case) => $case->value, self::cases());

        if ($sort) {
            usort($array, fn($a, $b) => collator_compare(self::getCollator(), $a, $b));
        }

        return $array;
    }

    private static function getCollator(): Collator
    {
        $locales = [
            'sk' => 'sk_SK',
            'cz' => 'cs_CZ',
            'hu' => 'hu_HU',
            'en' => 'en_US'
        ];

        return collator_create($locales[app()->getLocale()] ?? 'en_US');
    }

    public static function translatedValues(bool $sortByName = true): array
    {
        $values = [
            ['value' => self::CREATED->value, 'name' => __('batches.enums.created'),],
            ['value' => self::SENT_TO_CLEANERS->value, 'name' => __('batches.enums.sentToCleaners')],
            ['value' => self::RECEIVED_FROM_CLEANERS->value, 'name' => __('batches.enums.receivedFromCleaners')],
        ];

        if ($sortByName) {
            $collator = self::getCollator();

            usort($values, function ($a, $b) use ($collator) {
                return $collator->compare($a['name'], $b['name']);
            });
        }

        return $values;
    }


}
