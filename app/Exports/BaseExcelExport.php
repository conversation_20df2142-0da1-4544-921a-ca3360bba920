<?php

namespace App\Exports;

use App\Models\Claim;
use Maatwebsite\Excel\Concerns\FromCollection;

class BaseExcelExport implements FromCollection
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $from = '2024-07-01 00:00:00';
        $to = '2024-12-31 23:59:59';

        $claims = Claim::select('claim_code')->whereHas('esoHistoryLogs', function ($query) use ($from, $to) {
            $query->whereBetween('created_at', [$from, $to])->where('action', 'sentTo3S15');
        })->latest()->get();

        return $claims;
    }
}
