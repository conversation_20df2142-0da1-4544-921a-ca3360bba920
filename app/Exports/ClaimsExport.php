<?php

namespace App\Exports;

use App\Models\Claim;
use App\Models\ClaimType;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ClaimsExport implements FromCollection, WithHeadings, WithMapping, WithColumnWidths
{

    private object $claims;

    public function __construct($claims)
    {
        $this->claims = $claims;
        $this->claims->load('supplierClaimArticle.damageType','supplierClaimArticle.damageSubType', 'claimArticle.damageType','claimArticle.damageSubType');
    }

    public function columnWidths(): array
    {
        return [
            'A' => 20,
            'B' => 20,
            'C' => 20,
            'D' => 20,
            'E' => 20,
            'F' => 20,
            'G' => 20,
            'H' => 20,
            'I' => 20,
            'J' => 20,
            'k' => 20,
            'L' => 20,
            'M' => 20,
        ];
    }

    public function headings(): array
    {
        return [
            __('claims.index.claimCode'),
            __('claims.index.customer'),
            __('claims.index.article'),
            __('damage.damageType'),
            __('damage.damageDescription'),
            __('claims.index.type'),
            __('claims.index.centre'),
            __('claims.index.creator'),
            __('claims.index.created_at'),
            __('claims.index.status'),
            __('claims.index.solution'),
            __('article.quantity'),
        ];
    }

    /**
     * @return array
     * @var Claim $claim
     */
    public function map($row): array
    {
        if ($row->claim_type_id == ClaimType::CLAIM_TYPE_SUPPLIER) {
            $rows = [];

            foreach ($row->supplierClaimArticles(true)->get() as $article) {
                $rows[] = [
                    $row->claim_code,
                    null, //customer
                    $article->name . '(' . sliceGoodsId(
                        $article->goodsid,
                        0,
                        4
                    ) . ')',
                    $row->supplierClaimArticle->damage_type_with_subtype,
                    $row->supplierClaimArticle->damage_description,
                    $row->type->translated_name,
                    $row->centre->NameWithCode,
                    $row->user->name,
                    Carbon::parse($row->created_at)->format('d.m.Y h:i:s'),
                    $row->status->translated_name,
                    $row->solution?->name ? __('solution.' . $row->solution?->name) : '',
                    $article->pivot->quantity
                ];
            }

            return $rows;
        } else {
            return [
                $row->claim_code,
                $row?->wholesalePartner?->subject_name ?? $row?->customer?->fullName,
                $row->claimArticle?->article?->name ? $row->claimArticle->article->name . '(' . $row->claimArticle->article->goodsid . ')' : null,
                $row->claimArticle?->damage_type_with_subtype,
                $row->claimArticle?->damage_description,
                $row->type->translated_name,
                $row->centre->NameWithCode,
                $row->user->name,
                Carbon::parse($row->created_at)->format('d.m.Y h:i:s'),
                $row->status->translated_name,
                $row->solution?->name ? __('solution.' . $row->solution?->name) : '',
                $row->claimArticle?->quantity
            ];
        }
    }

    /**
     * @return object
     */
    public function collection()
    {
        return $this->claims;
    }
}
