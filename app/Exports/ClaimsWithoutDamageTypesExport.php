<?php

namespace App\Exports;

use App\Models\Claim;
use App\Models\ClaimType;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ClaimsWithoutDamageTypesExport implements FromCollection, WithHeadings, WithMapping, WithColumnWidths
{


    private object $claimsWithoutDamageTypes;

    public function __construct(object $claimsWithoutDamageTypes)
    {
        $this->claimsWithoutDamageTypes = $claimsWithoutDamageTypes;
    }

    public function columnWidths(): array
    {
        return [
            'A' => 20,
            'B' => 20,
            'C' => 20,
            'D' => 20,
            'E' => 20,
            'F' => 20,
            'G' => 20,
        ];
    }

    public function headings(): array
    {
        return [
            __('claims.index.claimCode'),
            __('claims.index.type'),
            __('administration.claimsWithoutDamageTypeStatistics.centre'),
            __('administration.claimsWithoutDamageTypeStatistics.currentDamageType'),
            __('warehouse.warehouse'),
            __('general.state'),
            __('administration.claimsWithoutDamageTypeStatistics.filled'),

        ];
    }

    /**
     * @return array
     * @var Claim $claim
     */
    public function map($row): array
    {
        return [
            $row->claim->claim_code,
            $row->claim->type->translated_name,
            $row->claim->centre->name_with_code,
            $row->claim?->claimArticle?->damage_type_with_subtype,
            $row->claim->warehouse_centre_id ? $row->claim->warehouseCentre->name_with_code : $row->claim->centre->name_with_code,
            $row->claim?->is_active == 1 ? __('warehouse.active') : __('warehouse.inactive'),
            formatDateByLang($row->created_at)
        ];
    }

    /**
     * @return Object
     */
    public function collection(): object
    {
        return $this->claimsWithoutDamageTypes;
    }

}
