<?php

namespace App\Exports;

use App\Enums\ClaimsTransports\ClaimsTransportsStatusesEnum;
use App\Models\Claim;
use App\Models\ClaimType;

use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class OverviewOfClaimsInTransportExport implements FromCollection, WithHeadings, WithMapping, WithColumnWidths
{

    private object $claims;

    public function __construct($claims)
    {
        $this->claims = $claims;
        $this->claims->load('supplierClaimArticle', 'claimArticle', 'dispatchNoteWarehouseExpense.successor');
    }

    public function columnWidths(): array
    {
        return [
            'A' => 20,
            'B' => 20,
            'C' => 20,
            'D' => 20,
            'E' => 20,
            'F' => 20,
            'G' => 20,
            'H' => 20,
            'I' => 20,
            'J' => 20,
            'k' => 20,
            'L' => 20,
        ];
    }

    public function headings(): array
    {
        return [
            __('claims.index.claimCode'),
            __('claims.index.status'),
            __('claimsTransports.transports'),
            __('claimsTransports.transportStatus'),
            __('warehouse.expenseNumber'),
            __('receiving.warehouseReceipt'),
            __('article.article'),
            __('claims.index.centre'),
            __('administration.statistics.warehouseExpenseCreated'),
            __('administration.claimsWithoutDamageTypeStatistics.sentToWarehouse'),
            __('administration.statistics.daysInTransport'),

        ];
    }

    /**
     * @return array
     * @var Claim $claim
     */
    public function map($row): array
    {
        $article = $row->is_supplier_claim
            ? $row->supplierClaimArticle->article_code
            : "{$row->claimArticle?->article?->name}({$row->claimArticle?->article?->goodsid})";

        $transport = $row->transports->first();

        return [
            $row->claim_code,
            $row->status->translated_name,
            $transport?->transport_id,
            isset($transport) ? ClaimsTransportsStatusesEnum::tryFrom($transport->status)->translate() : '',
            $row->dispatchNoteWarehouseExpense->eso_document_number,
            $receipt->eso_document_number ?? $row->dispatchNoteWarehouseExpense?->successor?->eso_document_number,
            $article,
            $row->centre->nameWithCode,
            formatDateByLang($row->dispatchNoteWarehouseExpense->created_at),
            isset($transport->order_confirmed_at) ? formatDateByLang($transport->order_confirmed_at) : '',
            isset($transport->order_confirmed_at) ? Carbon::parse($transport->order_confirmed_at)->diffInDays() : '',
        ];
    }

    /**
     * @return Object
     */
    public function collection(): object
    {
        return $this->claims;
    }

}
