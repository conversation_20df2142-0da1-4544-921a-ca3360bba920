<?php

namespace App\Exports;

use App\Enums\ClaimsTransports\ClaimsTransportsStatusesEnum;
use App\Models\Claim;
use App\Models\ClaimType;

use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class OverviewOfClaimsSentToCleanersExport implements FromCollection, WithHeadings, WithMapping, WithColumnWidths
{

    private object $claims;

    public function __construct($claims)
    {
        $this->claims = $claims;
    }

    public function columnWidths(): array
    {
        return [
            'A' => 20,
            'B' => 20,
            'C' => 20,
            'D' => 20,
            'E' => 20,
            'F' => 20,
        ];
    }

    public function headings(): array
    {
        return [
            __('claims.index.claimCode'),
            __('claims.index.goodsId'),
            __('claims.index.centre'),
            __('batches.claimsCount'),
            __('batches.batch'),
            __('batches.enums.sentToCleaners')

        ];
    }

    /**
     * @return array
     * @var Claim $claim
     */
    public function map($row): array
    {

        return [
            $row->claim_code,
            $row->goodsid,
            $row->centreCode,
            $row->claimsCount,
            "#{$row->selectedBatch->id}",
            formatDateByLang($row->selectedBatch->sentToCleanersLog->created_at)
        ];
    }

    /**
     * @return Object
     */
    public function collection(): object
    {
        return $this->claims;
    }

}
