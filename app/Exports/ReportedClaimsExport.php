<?php

namespace App\Exports;

use App\Models\Claim;
use App\Models\ClaimType;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ReportedClaimsExport implements FromCollection, WithHeadings, WithMapping, WithColumnWidths
{

    private object $claims;

    public function __construct($claims)
    {
        $this->claims = $claims;
    }

    public function columnWidths(): array
    {
        return [
            'A' => 20,
            'B' => 20,
            'C' => 20,
            'D' => 20,
            'E' => 20,
            'F' => 20,
            'G' => 20,
            'H' => 20,
            'I' => 20,
            'J' => 20,
        ];
    }

    public function headings(): array
    {
        return [
            __('claims.index.claimCode'),
            __('batches.batch'),
            __('article.name'),
            __('article.goodsid'),
            __('article.ean'),
            __('article.supplier'),
            __('article.quantity'),
            __('warehouse.warehouse'),
            __('warehouse.received'),
            __('statuses.status'),
            __('general.state'),
        ];
    }

    /**
     * @return array
     * @var Claim $claim
     */
    public function map($row): array
    {


        $closed = $row->is_active == 1 ? __('warehouse.active') : __('warehouse.inactive');

        if (in_array($row->claim_type_id, [ClaimType::CLAIM_TYPE_SUPPLIER, ClaimType::CLAIM_TYPE_SUPPLIER_WAREHOUSE])) {
            $rows = [];
            foreach ($row->supplierClaimArticles(true)->get() as $article) {
                $rows[] = [
                    $row->claim_code,
                    $row->batch_id,
                    $article->name,
                    $article->goodsid,
                    $article->ean,
                    $article->supplier_name,
                    $article->pivot->quantity,
                    $row->warehouseCentre?->code,
                    isset($row->warehouse_received_at) ? Carbon::parse($row->warehouse_received_at)->format(
                        'd.m.Y h:i:s'
                    ) : '',
                    $row->warehouseStatus?->translated_name,
                    $closed,
                ];
            }

            return $rows;
        } else {
            return [
                $row->claim_code,
                $row->batch_id,
                $row->claimArticle?->article?->name,
                $row->claimArticle?->article?->goodsid,
                $row->claimArticle?->article?->ean,
                $row->claimArticle?->article?->supplier_name,
                $row->claimArticle?->quantity,
                $row->warehouseCentre?->code,
                isset($row->warehouse_received_at) ? Carbon::parse($row->warehouse_received_at)->format(
                    'd.m.Y h:i:s'
                ) : '',
                $row->warehouseStatus?->translated_name,
                $closed,
            ];
        }
    }

    /**
     * @return object
     */
    public function collection()
    {
        return $this->claims;
    }
}
