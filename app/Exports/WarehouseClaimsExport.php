<?php

namespace App\Exports;

use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\Status;
use App\Models\WarehouseDocumentType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class WarehouseClaimsExport implements FromQuery, WithHeadings, WithMapping, ShouldAutoSize
{

    private $filter;

    public function __construct($filter)
    {
        $this->filter = $filter;
    }

    public function headings(): array
    {
        return [
            __('claims.index.claimCode'),
            __('validation.attributes.claimTypeId'),
            __('batches.batch'),
            __('receiving.warehouseReceipt'),
            __('article.name'),
            __('article.goodsid'),
            __('damage.damageType'),
            __('article.ean'),
            __('article.trademark'),
            __('article.supplier'),
            __('warehouse.warehouse'),
            __('statuses.status'),
            __('general.state'),
            __('article.quantity'),
            __('warehouse.received'),
            __('warehouse.initialWarehouse'),
            __('warehouse.sentToCleaners'),
        ];
    }

    /**
     * @return array
     * @var Claim $claim
     */
    public function map($row): array
    {
        $closed = $row->is_active == 1 ? __('warehouse.active') : __('warehouse.inactive');

        $cleaned = $row->batchesSentToCleaners->isNotEmpty() ? __('general.yes') : __('general.no');
        $initialWarehouse = $row->claim_type_id === ClaimType::CLAIM_TYPE_B2B_B2C_WAREHOUSE_RETURNS && preg_match('/^cl-(gcecz|wms-vratka-p)-/i', $row->claim_code)
            ? $row->b2bB2cWarehouseDocument?->from_warehouse
            : '';

        $receivedAt = $row->warehouse_received_at ? Carbon::parse($row->warehouse_received_at)->format('d.m.Y H:i') : null;

        if (in_array($row->claim_type_id, [ClaimType::CLAIM_TYPE_SUPPLIER, ClaimType::CLAIM_TYPE_SUPPLIER_WAREHOUSE])) {
            $rows = [];
            foreach ($row->supplierClaimArticlesWhereQuantityNotNull as $article) {
                $rows[] = [
                    $row->claim_code,
                    $row->type->translated_name,
                    $row->batch_id,
                    $row->lastWarehouseReceipt?->eso_document_number,
                    $article->name,
                    $article->goodsid,
                    $row->supplierClaimArticle->damage_type_with_subtype,
                    $article->ean,
                    $article->trademark,
                    $article->supplier_name,
                    $row->warehouseCentre?->code,
                    $row->warehouseStatus?->translated_name,
                    $closed,
                    $article->pivot->quantity,
                    $receivedAt,
                    $initialWarehouse,
                    $cleaned
                ];
            }

            return $rows;
        } else {
            return [
                $row->claim_code,
                $row->type->translated_name,
                $row->batch_id,
                $row->lastWarehouseReceipt?->eso_document_number,
                $row->claimArticle?->article?->name,
                $row->claimArticle?->article?->goodsid,
                $row->claimArticle?->damage_type_with_subtype,
                $row->claimArticle?->article?->ean,
                $row->claimArticle?->article?->trademark,
                $row->claimArticle?->article?->supplier_name,
                $row->warehouseCentre?->code,
                $row->warehouseStatus?->translated_name,
                $closed,
                $row->claimArticle?->quantity,
                $receivedAt,
                $initialWarehouse,
                $cleaned
            ];
        }
    }


    public function query()
    {
        $warehouseClaimTypeId = ClaimType::CLAIM_TYPE_WAREHOUSE;
        $warehouseSupplierClaimTypeId = ClaimType::CLAIM_TYPE_SUPPLIER_WAREHOUSE;
        $b2bB2cClaimTypeId = ClaimType::CLAIM_TYPE_B2B_B2C_WAREHOUSE_RETURNS;


        return Claim::query()
            ->where(function ($q) {
                $q->whereNotNull('warehouse_status_id')
                    ->orWhereIn('claim_type_id', [
                        ClaimType::CLAIM_TYPE_WAREHOUSE,
                        ClaimType::CLAIM_TYPE_SUPPLIER_WAREHOUSE,
                        ClaimType::CLAIM_TYPE_B2B_B2C_WAREHOUSE_RETURNS
                    ]);
            })->filter(filter: $this->filter, closure: function (Builder $query) {
                $query->when(isset($this->filter['closure.article']), function ($q) {
                    return $q->where(function (Builder $q) {
                        return $q->whereHas('claimArticle.article', function ($q) {
                            $q->where('goodsid', 'LIKE', "%{$this->filter['closure.article']}%");
                        })->orWhereHas('supplierClaimArticle', function ($q) {
                            $q->where('article_code', 'LIKE', "%{$this->filter['closure.article']}%");
                        });
                    });
                })
                    ->when(isset($this->filter['closure.receipt']), function ($q) {
                        return $q->whereHas('warehouseDocuments', function ($q) {
                            return $q->whereIn('document_type_id', [WarehouseDocumentType::WAREHOUSE_RECEIPT, WarehouseDocumentType::B2B_B2C_WAREHOUSE_RECEIPT])
                                ->where('eso_document_number', 'LIKE', "%{$this->filter['closure.receipt']}%");
                        });
                    })
                    ->when(isset($this->filter['closure.sentToCleaners']), function ($q)  {
                        return $q->has('batchesSentToCleaners');
                    });
            })->with([
                'claimArticle:id,article_id,damage_description,quantity,damage_type_id,damage_subtype_id',
                'claimArticle.article:id,goodsid,name,supplier_name,trademark,ean,code',
                'claimArticle.damageType',
                'claimArticle.damageSubtype',
                'supplierClaimArticlesWhereQuantityNotNull',
                'centre:id,name,code',
                'type:id,name',
                'warehouseCentre:id:code',
                'warehouseStatus:id,name',
                'lastWarehouseReceipt',
            ])
            ->orderByRaw("CASE WHEN claim_type_id in ($warehouseSupplierClaimTypeId,$b2bB2cClaimTypeId,$warehouseClaimTypeId) THEN created_at ELSE warehouse_received_at END desc");
    }


}
