<?php

namespace App\Exports;

use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\Report;
use App\Models\Trademark;
use App\Services\Warehouse\ReportsService;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class WarehouseReportExport implements FromCollection, WithHeadings, WithMapping,
    ShouldAutoSize //WithStyles, WithEvents

{
    private mixed $claims;
    private array $selects;
    private mixed $report;
    private bool $typeRequired;
    private array $KLTrademarks;
    private ReportsService $reportService;

    public function __construct($claims, Report $report)
    {
        $this->claims = $claims;
        $this->report = $report;
        $this->KLTrademarks = [Trademark::KL, Trademark::KLJ];
        $this->typeRequired = !empty(array_intersect($this->report->decoded_filter->trademark, $this->KLTrademarks));
        $this->reportService = new ReportsService();
    }

    public function headings(): array
    {
        $baseHeading = [
            'Claim code',
            'Batch id',
            'Goods id',
            'Article code',
            'Season',
            'Color',
            'Color name',
            'Size',
            'Nip',
            'Nip currency',
            'Supplier',
            'damage type',
            'Photo id',
        ];

        if ($this->typeRequired) {
            $baseHeading = array_merge($baseHeading, ['type', 'purchase document number']);
        }

        return $baseHeading;
    }


    /**
     * @return array
     * @var Claim $claim
     */
    public function map($row): array
    {
        if ($row->claimArticle->quantity > 1) {
            $rows = [];

            for ($i = 1; $i <= $row->claimArticle->quantity; $i++) {
                $rows[] = $this->getFormatedClaim($row);
            }
            return $rows;
        } else {
            return $this->getFormatedClaim($row);
        }
    }

    private function getFormatedClaim(object $row): array
    {

        $claim = [
            $row->claim_code,
            $row->batch_id,
            $row->claimArticle?->article?->goodsid,
            $this->reportService->getArticleCode($row->claimArticle?->article),
            $this->reportService->getSeasonByTrademark($row->claimArticle?->article),
            $this->reportService->getArticleColor($row->claimArticle?->article?->goodsid),
            $row->claimArticle?->article?->color,
            $row->claimArticle?->article?->size,
            $row->claimArticle?->article?->nip,
            $row->claimArticle?->article?->nip_currency,
            $row->claimArticle?->article?->supplier_name,
            $row->claimArticle?->engDamageTypeWithSubtype,
            $row->id,
            //null
        ];

        if ($this->typeRequired && in_array($row->claimArticle?->article?->trademark, $this->KLTrademarks)) {
            $claim = array_merge($claim, [
                $row->claim_type_id === ClaimType::CLAIM_TYPE_CUSTOMER ? 'Customer' : 'Internal',
                $row->claim_type_id === ClaimType::CLAIM_TYPE_CUSTOMER ? $row->purchaseDocument->document_number : ''
            ]);
        }

        return $claim;
    }

    /**
     * @return object
     */
    public function collection()
    {
        return $this->claims;
    }
}
