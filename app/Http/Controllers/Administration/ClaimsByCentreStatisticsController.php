<?php

namespace App\Http\Controllers\Administration;

use App\Http\Controllers\Controller;
use App\Models\Centre;
use App\Models\Country;
use App\Models\Solution;
use App\Models\Status;
use App\Services\Administration\AdministrationNavigationService;
use App\Services\CentresService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

class ClaimsByCentreStatisticsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, CentresService $centresService)
    {
        $this->authorize('view admin statistics / reports');
        $filter = ($request->filter) ?: ((session()->get('claimsStatisticsByCentre')) ? session()->get(
            'claimsStatisticsByCentre'
        ) : []);

        if ($filter) {
            session()->put('claimsStatisticsByCentre', $filter);
        }

        $Warehouses = $centresService->allWarehouses()->pluck('id');
        $officeCentres = $centresService->officeCentres()->pluck('id');
        $otherExceptions = collect([
            Centre::Warehouse_3S20D,
            Centre::Warehouse_3S20I,
            Centre::Warehouse_3S20,
            Centre::Warehouse_3S00,
        ]);

        $exceptions = $Warehouses->merge($officeCentres)->merge($otherExceptions)->toArray();
        $allCentres = Centre::whereNotIn('id', $exceptions)->select('id', 'city', 'code', 'name')->get();
        $allCities = $allCentres->unique('city')->pluck('city');

        //helper function
        $filterByDate = function ($filter, $query) {
            return $query->when(isset($filter['created_at,>=,claims']), function ($query) use ($filter) {
                return $query->whereDate(
                    'created_at',
                    '>=',
                    Carbon::createFromFormat('Y-m-d', $filter['created_at,>=,claims'])->startOfDay()
                );
            })->when(isset($filter['created_at,<=,claims']), function ($query) use ($filter) {
                return $query->whereDate(
                    'created_at',
                    '<=',
                    Carbon::createFromFormat('Y-m-d', $filter['created_at,<=,claims'])->endOfDay()
                );
            });
        };
        $centres = Centre::select('id', 'code', 'name', 'city', 'country_id')
            ->where('is_active', 1)
            ->whereNotIn('id', $exceptions)
            ->when($filter && !empty(json_decode($filter['id,in'])), function ($q) use ($filter) {
                return $q->whereIn('id', json_decode($filter['id,in']));
            })
            ->when($filter && !empty(json_decode($filter['city,in'])), function ($q) use ($filter) {
                return $q->whereIn('city', json_decode($filter['city,in']));
            })
            ->withCount([
                'claims' => function (Builder $q) use ($filter, $filterByDate) {
                    $q->whereNot('claim_status_id', Status::CLAIM_STATUS_CANCELED);
                    return $filterByDate($filter, $q);
                },
                'claims as solved_in_centre_count' => function (Builder $q) use (
                    $filter,
                    $filterByDate,
                ) {
                    $q->whereIn(
                        'solution_id',
                        [Solution::SOLUTION_REPAIR, Solution::SOLUTION_RETURN_ARTICLE_TO_CUSTOMER]
                    );
                    return $filterByDate($filter, $q);
                },
                'claims as received_in_warehouse_count' => function (Builder $q) use ($filter, $filterByDate) {
                    $q->whereNotNull('warehouse_status_id');
                    return $filterByDate($filter, $q);
                },
                'claims as sent_to_warehouse_count' => function (Builder $q) use ($filter, $filterByDate) {
                    $q->where(function (Builder $q) {
                        $q->where('claim_status_id', Status::CLAIM_SEND_TO_WAREHOUSE)
                            ->orWhere('claim_status_id', Status::CLAIM_STATUS_WAREHOUSE_EXPENSE_CREATED);
                    })
                        ->whereNull('warehouse_status_id');
                    return $filterByDate($filter, $q);
                }
            ])
            ->with([
                'claims.historyLogs' => function ($q) use ($filter, $filterByDate) {
                    $q->select(
                        'claim_id',
                        \DB::raw(
                            "ABS(DATEDIFF (
                            MAX(CASE WHEN claim_history_logs.action_type ='resolved' OR claim_history_logs.action_type ='warehouseExpense' THEN claim_history_logs.created_at END),
                            MAX(CASE WHEN claim_history_logs.action_type ='received' THEN claim_history_logs.created_at END)))
                            AS in_transport_to_warehouse"
                        )
                    );
                    $q->where(function (Builder $q) {
                        $q->where('action_type', 'resolved')
                            ->orWhere('action_type', 'received');
                    });
                    $q = $filterByDate($filter, $q);
                    $q->groupBy('claim_id')->havingRaw('in_transport_to_warehouse');
                }
            ])
            ->orderBy('code', 'asc')
            ->get();


        $centres->map(function ($centre) {
            $historyLogs = $centre->claims->pluck('historyLogs')->flatten();
            $centre->min_days_in_transport = $historyLogs->min('in_transport_to_warehouse');
            $centre->max_days_in_transport = $historyLogs->max('in_transport_to_warehouse');

            $centre->avg_days_in_transport = $historyLogs->avg('in_transport_to_warehouse');
        });


        $skCentres = $centres->where('country_id', 1);
        $czCentres = $centres->where('country_id', Country::CZ);
        $huCentres = $centres->where('country_id', Country::HU);
        $summaries = collect([
            __('claims.create.allCentres') => (object)[
                'claims_count' => $centres->sum('claims_count'),
                'solved_in_centre_count' => $centres->sum('solved_in_centre_count'),
                'received_in_warehouse_count' => $centres->sum('received_in_warehouse_count'),
                'sent_to_warehouse_count' => $centres->sum('sent_to_warehouse_count'),
                'min_days_in_transport' => $centres->min('min_days_in_transport'),
                'avg_days_in_transport' => $centres->avg('avg_days_in_transport'),
                'max_days_in_transport' => $centres->max('max_days_in_transport'),
            ],
            'sk' => (object)[
                'claims_count' => $skCentres->sum('claims_count'),
                'solved_in_centre_count' => $skCentres->sum('solved_in_centre_count'),
                'received_in_warehouse_count' => $skCentres->sum('received_in_warehouse_count'),
                'sent_to_warehouse_count' => $skCentres->sum('sent_to_warehouse_count'),
                'min_days_in_transport' => $skCentres->min('min_days_in_transport'),
                'avg_days_in_transport' => $skCentres->avg('avg_days_in_transport'),
                'max_days_in_transport' => $skCentres->max('max_days_in_transport'),
            ],
            'cz' => (object)[
                'claims_count' => $czCentres->sum('claims_count'),
                'solved_in_centre_count' => $czCentres->sum('solved_in_centre_count'),
                'received_in_warehouse_count' => $czCentres->sum('received_in_warehouse_count'),
                'sent_to_warehouse_count' => $czCentres->sum('sent_to_warehouse_count'),
                'min_days_in_transport' => $czCentres->min('min_days_in_transport'),
                'avg_days_in_transport' => $czCentres->avg('avg_days_in_transport'),
                'max_days_in_transport' => $czCentres->max('max_days_in_transport'),
            ],
            'hu' => (object)[
                'claims_count' => $huCentres->sum('claims_count'),
                'solved_in_centre_count' => $huCentres->sum('solved_in_centre_count'),
                'received_in_warehouse_count' => $huCentres->sum('received_in_warehouse_count'),
                'sent_to_warehouse_count' => $huCentres->sum('sent_to_warehouse_count'),
                'min_days_in_transport' => $huCentres->min('min_days_in_transport'),
                'avg_days_in_transport' => $huCentres->avg('avg_days_in_transport'),
                'max_days_in_transport' => $huCentres->max('max_days_in_transport'),
            ]
        ]);

        return view('admin.statistics.claimsByCentre', [
            'navigation' => new AdministrationNavigationService(auth()->user()),
            'centres' => $centres,
            'summaries' => $summaries,
            'allCentres' => $allCentres,
            'allCities' => $allCities,
            'filter' => $filter ?? null
        ]);
    }
}
