<?php

namespace App\Http\Controllers\Administration\ClaimsWithoutDamageTypesStatistics;

use App\Http\Controllers\Controller;
use App\Models\Centre;
use App\Models\ClaimsWithoutDamageTypesStatistic;
use App\Models\ClaimType;
use App\Models\DamageType;
use App\Services\Administration\AdministrationNavigationService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class ClaimsWithoutDamageTypesStatisticsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function __invoke(Request $request)
    {
        $this->authorize('view admin statistics / reports', auth()->user());

        $claimTypes = ClaimType::getSortedClaimTypes();

        $damageTypes = DamageType::query()
            ->subTypes()
            ->withOther()
            ->onlyActive()
            ->withParent()
            ->formatCollectionForSelectInput();

        $centres = Centre::orderBy('code')->get();

        $filter = ($request->filter) ?: ((session()->get('claimsWithoutDamageTypes')) ? session()->get(
            'claimsWithoutDamageTypes'
        ) : []);

        if ($filter) {
            session()->put('claimsWithoutDamageTypes', $filter);
        }

        $query = ClaimsWithoutDamageTypesStatistic::query();
        $query = filterRequest($query, $filter, function (Builder $query) use ($filter) {

            $warehouses = isset($filter['closure.warehouse']) ? json_decode($filter['closure.warehouse']) : null;


            return $query->when(isset($warehouses) && !empty($warehouses), function ($q) use ($filter, $warehouses) {
                return $q->whereHas('claim', function ($q) use ($filter, $warehouses) {
                    return $q->where(function (Builder $q) use ($filter, $warehouses) {
                        return $q->whereIn('warehouse_centre_id', $warehouses)
                            ->orWhere(function (Builder $q) use ($filter, $warehouses) {
                                $q->whereIn('claim_centre_id', $warehouses)->whereNull('warehouse_centre_id');
                            });
                    });
                });
            });
        });


        $claimsWithoutDamageTypes = $query
            ->with(
                'claim.claimArticle.article',
                'claim.claimArticle.damageType',
                'claim.claimArticle.damageSubType',
                'claim.type',
                'claim.centre',
            )->whereHas('claim', function (Builder $query) use ($filter) {
                return $query->whereNull('deleted_at');
            })
            ->latest('created_at')
            ->paginate(50);

        return view('admin.claimsWithoutDamageTypesStatistics.index', ['navigation' => new AdministrationNavigationService(auth()->user()),
            'claimsWithoutDamageTypes' => $claimsWithoutDamageTypes,
            'filter' => $filter,
            'centres' => $centres,
            'damageTypes' => $damageTypes,
            'claimTypes' => $claimTypes,]);


    }
}
