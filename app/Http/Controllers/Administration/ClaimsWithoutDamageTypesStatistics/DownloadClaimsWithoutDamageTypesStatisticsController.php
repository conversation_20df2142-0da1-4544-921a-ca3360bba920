<?php

namespace App\Http\Controllers\Administration\ClaimsWithoutDamageTypesStatistics;

use App\Exports\ClaimsExport;
use App\Exports\ClaimsWithoutDamageTypesExport;
use App\Exports\OverviewOfClaimsInTransportExport;
use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\ClaimsWithoutDamageTypesStatistic;
use App\Models\Status;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;


class DownloadClaimsWithoutDamageTypesStatisticsController extends Controller

{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $user = auth()->user();
        $this->authorize('view admin statistics / reports', $user);
        $date = date("Y.m.d");

        $filter = ($request->filter) ?: ((session()->get('claimsWithoutDamageTypes')) ? session()->get(
            'claimsWithoutDamageTypes'
        ) : []);

        if ($filter) {
            session()->put('claimsWithoutDamageTypes', $filter);
        }

        $query = ClaimsWithoutDamageTypesStatistic::query();
        $query = filterRequest($query, $filter, function (Builder $query) use ($filter) {

            $warehouses = isset($filter['closure.warehouse']) ? json_decode($filter['closure.warehouse']) : null;


            return $query->when(isset($warehouses) && !empty($warehouses), function ($q) use ($filter, $warehouses) {
                return $q->whereHas('claim', function ($q) use ($filter, $warehouses) {
                    return $q->where(function (Builder $q) use ($filter, $warehouses) {
                        return $q->whereIn('warehouse_centre_id', $warehouses)
                            ->orWhere(function (Builder $q) use ($filter, $warehouses) {
                                $q->whereIn('claim_centre_id', $warehouses)->whereNull('warehouse_centre_id');
                            });
                    });
                });
            });
        });


        $claimsWithoutDamageTypes = $query
            ->with(
                'claim.claimArticle.article',
                'claim.claimArticle.damageType',
                'claim.claimArticle.damageSubType',
                'claim.type',
                'claim.centre',
            )
            ->latest('created_at')
            ->get();

        return Excel::download(
            new ClaimsWithoutDamageTypesExport($claimsWithoutDamageTypes),
            "claims-without-damage-types-$date.xlsx"
        );
    }
}
