<?php

namespace App\Http\Controllers\Administration\DamagePlaces;

use App\Http\Controllers\Controller;
use App\Models\DamagePlace;
use App\Models\DamageType;
use App\Services\Administration\AdministrationNavigationService;
use Illuminate\Http\Request;

class DamagePlaceLogsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, DamagePlace $damagePlace)
    {
        $this->authorize('damage types administration');

        return view('admin.damagePlaces.logs', [
            'logs' => $damagePlace->logs()->paginate(20),
            'damagePlace' => $damagePlace,
        ]);
    }
}
