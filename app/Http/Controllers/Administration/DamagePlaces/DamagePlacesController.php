<?php

namespace App\Http\Controllers\Administration\DamagePlaces;

use App\Http\Controllers\Controller;
use App\Http\Requests\Administration\StoreDamagePlacesRequest;
use App\Http\Requests\Administration\UpdateDamagePlacesRequest;
use App\Http\Requests\Administration\UpdateDamageTypesRequest;
use App\Models\DamagePlace;
use App\Models\DamageType;
use App\Models\OrderGroup;
use App\Services\Administration\AdministrationNavigationService;


class DamagePlacesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $this->authorize('damage places administration');

        return view('admin.damagePlaces.index', [
            'navigation' => new AdministrationNavigationService(auth()->user()),
            'damagePlaces' => DamagePlace::query()->paginate(50)
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('damage places administration');

        return view('admin.damagePlaces.create', [
            'orderGroups' => OrderGroup::where('is_active',1)->get(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreDamagePlacesRequest $request)
    {
        $this->authorize('damage places administration');

        $damagePlace = DamagePlace::create([
            'sk_name' => $request->sk_name,
            'cz_name' => $request->cz_name,
            'en_name' => $request->en_name,
            'hu_name' => $request->hu_name,
        ]);

        $damagePlace->orderGroups()->attach($request->order_groups);

        $damagePlace->createHistoryLog('created', 'damagedPlaceCreated', $damagePlace->toJson());
        return redirect(route('administration.damagePlaces.index'))->with('status-success', __('administration.damagePlaces.damagePlaceAction', ['action' => trans_choice('general.created', 3)]));

    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(DamagePlace $damagePlace)
    {
        $this->authorize('damage places administration');

        return view('admin.damagePlaces.edit', [
            'damagePlace' => $damagePlace,
            'orderGroups' => OrderGroup::where('is_active',1)->get(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateDamagePlacesRequest $request, DamagePlace $damagePlace)
    {
        $this->authorize('damage places administration');

        $damagePlace->orderGroups()->sync($request->order_groups);

        $damagePlace->createHistoryLog('updated', 'damagePlaceUpdated', $damagePlace->toJson());
        return redirect(route('administration.damagePlaces.edit', $damagePlace->id))->with('status-success', __('administration.damagePlaces.damagePlaceAction', ['action' => trans_choice('general.updated', 3)]));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DamagePlace $damagePlace)
    {
        $this->authorize('damage places administration');

        $damagePlace->createHistoryLog('deleted', 'damagePlaceDeleted', $damagePlace->toJson());
        $damagePlace->delete();

        return redirect(route('administration.damagePlaces.index'))->with('status-success', __('administration.damagePlaces.damagePlaceAction', ['action' => trans_choice('general.deleted', 3)]));


    }
}
