<?php

namespace App\Http\Controllers\Administration\DamageTypes;

use App\Http\Controllers\Controller;
use App\Models\DamageType;
use App\Services\Administration\AdministrationNavigationService;
use Illuminate\Http\Request;

class DamageTypeLogsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, DamageType $damageType)
    {
        $this->authorize('damage types administration');

        return view('admin.damageTypes.logs', [
            'logs' => $damageType->logs()->paginate(20),
            'damageType' => $damageType,
        ]);
    }
}
