<?php

namespace App\Http\Controllers\Administration\DamageTypes;

use App\Enums\DamageTypes\OrderGroupsEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Administration\StoreDamageTypesRequest;
use App\Http\Requests\Administration\UpdateDamageTypesRequest;
use App\Models\DamageType;
use App\Services\Administration\AdministrationNavigationService;
use App\Services\Administration\DamageTypesService;

class DamageTypesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(DamageTypesService $damageTypesService)
    {
        $this->authorize('damage types administration');
        $damageTypes = DamageType::mainTypes()
            ->with('subtypes')
            ->whereNot('en_name', 'other')
            ->paginate();

        return view('admin.damageTypes.index', [
            'navigation' => new AdministrationNavigationService(auth()->user()),
            'damageTypes' => $damageTypes,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('damage types administration');


        return view('admin.damageTypes.create', [
            'mainTypes' => DamageType::mainTypes()->whereNot('en_name', 'other')->get(),
            'damageSubTypes' => DamageType::whereNotNull('parent_id')->paginate(50),
            'orderGroups' => OrderGroupsEnum::translatedAvailableValues(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreDamageTypesRequest $request)
    {
        $this->authorize('damage types administration');
        $damageType = DamageType::create([
            'parent_id' => $request->parent_id,
            'order_group' => $request->order_group,
            'sk_name' => $request->sk_name,
            'cz_name' => $request->cz_name,
            'en_name' => $request->en_name,
            'hu_name' => $request->hu_name,
        ]);


        $damageType->createHistoryLog('created', 'damagedTypeCreated', $damageType->toJson());
        return redirect(route('administration.damageTypes.index'))->with('status-success', __('administration.damageTypes.damageTypeAction', ['action' => trans_choice('general.created', 1)]));

    }

    /**
     * Display the specified resource.
     */
    public function show(DamageType $damageType)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(DamageType $damageType)
    {
        $this->authorize('damage types administration');

        $damageType->load('subTypes');

        return view('admin.damageTypes.edit', [
            'damageType' => $damageType,
            'mainTypes' => DamageType::mainTypes()->whereNot('en_name', 'other')->get(),
            'damageSubTypes' => DamageType::whereNotNull('parent_id')->paginate(50),
            'orderGroups' => OrderGroupsEnum::translatedAvailableValues(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateDamageTypesRequest $request, DamageType $damageType)
    {
        $this->authorize('damage types administration');

        $damageType->fill([
            'parent_id' => $request->parent_id,
            'order_group' => $request->order_group,
            'is_active' => $request->is_active,
            'sk_name' => $request->sk_name,
            'en_name' => $request->en_name,
            'hu_name' => $request->hu_name,
            'cz_name' => $request->cz_name,
        ])->save();

        $damageType->createHistoryLog('updated', 'damageTypeUpdated', $damageType->toJson());
        return redirect(route('administration.damageTypes.edit', $damageType->id))->with('status-success', __('administration.damageTypes.damageTypeAction', ['action' => trans_choice('general.updated', 1)]));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DamageType $damageType)
    {
//        $this->authorize('damage types administration');
//
//        $damageType->subTypes()->delete();
//        $damageType->delete();
//        $damageType->createHistoryLog('deleted', 'damageTypeDeleted', $damageType->toJson());
//
//        return redirect(route('administration.damageTypes.index'))->with('status-success', __('administration.damageTypes.damageTypeAction', ['action' => trans_choice('general.deleted', 1)]));
//

    }
}
