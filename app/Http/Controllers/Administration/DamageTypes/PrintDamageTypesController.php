<?php

namespace App\Http\Controllers\Administration\DamageTypes;

use App\Http\Controllers\Controller;
use App\Models\DamageType;
use Illuminate\Http\Request;

class PrintDamageTypesController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $backUrl = route('claims.index');

        $damageTypes = DamageType::mainTypes()
            ->with('subtypes')
            ->whereNot('en_name', 'other')
            ->get();

        $damageTypesChunked = [];

        $count =0;
        $index = 0;
        $limit = 48;

        foreach ($damageTypes as $damageType){

            if ($count + $damageType->subtypes->count() + 1 < $limit){
                $damageTypesChunked[$index][]=$damageType;
                $count= $count + $damageType->subtypes->count() + 1;
            } else{
                $index++;
                $damageTypesChunked[$index][]=$damageType;
                $count = $damageType->subtypes->count() + 1;
            }

        }


    return view('admin.damageTypes.printDamageTypesList', ['damageTypesChunked' => $damageTypesChunked, 'backUrl' => $backUrl]);
    }
}
