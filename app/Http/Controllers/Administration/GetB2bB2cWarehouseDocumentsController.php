<?php

namespace App\Http\Controllers\Administration;

use App\Http\Controllers\Controller;
use App\Services\Administration\AdministrationNavigationService;
use App\Services\EsoApi\EsoService;
use Illuminate\Http\Request;

class GetB2bB2cWarehouseDocumentsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $this->authorize('claims administration');

        if ($request->b2b_b2c_warehouse_document_number) {
            $request->validate(['b2b_b2c_warehouse_document_number' => 'string']);

            $esoService = new EsoService();
            $esoResponse = $esoService->getB2bB2cWarehouseDocument($request->b2b_b2c_warehouse_document_number)->getResponse();
        }

        return view('admin.b2bB2cWarehouseDocuments.index', [
            'navigation' => new AdministrationNavigationService(auth()->user()),
            'esoResponse' => $esoResponse ?? null,
            'b2b_b2c_warehouse_document_number' => $request->b2b_b2c_warehouse_document_number
        ]);
    }
}
