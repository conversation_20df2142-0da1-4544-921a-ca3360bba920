<?php

namespace App\Http\Controllers\Administration;

use App\Http\Controllers\Controller;
use App\Services\Administration\AdministrationNavigationService;
use App\Services\EsoApi\EsoService;
use Illuminate\Http\Request;

class GetEsoTransactionsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $this->authorize('claims administration');

        if ($request->idws_journal) {
            $request->validate(['idws_journal' => 'numeric']);

            $esoService = new EsoService();
            $esoResponse = $esoService->getEsoTransactionByIdJournal($request->idws_journal)->getResponse();
        }

        return view('admin.esoTransactions.index', [
            'navigation' => new AdministrationNavigationService(auth()->user()),
            'esoResponse' => $esoResponse ?? null,
            'idws_journal' => $request->idws_journal
        ]);
    }
}
