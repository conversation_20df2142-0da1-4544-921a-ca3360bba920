<?php

namespace App\Http\Controllers\Administration;

use App\Http\Controllers\Controller;
use App\Services\Administration\AdministrationNavigationService;
use App\Services\EsoApi\EsoService;
use Illuminate\Http\Request;

class GetEsoWarehouseExpensesController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $this->authorize('claims administration');

        if ($request->warehouse_expense) {
            $request->validate(['warehouse_expense' => 'numeric']);

            $esoService = new EsoService();
            $esoResponse = $esoService->getWarehouseExpense($request->warehouse_expense)->getResponse();
        }

        return view('admin.esoWarehouseExpenses.index', [
            'navigation' => new AdministrationNavigationService(auth()->user()),
            'esoResponse' => $esoResponse ?? null,
            'warehouse_expense' => $request->warehouse_expense
        ]);
    }
}
