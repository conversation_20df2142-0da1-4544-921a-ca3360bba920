<?php

namespace App\Http\Controllers\Administration;

use App\Http\Controllers\Controller;
use App\Services\Administration\AdministrationNavigationService;
use App\Services\EsoApi\EsoService;
use Illuminate\Http\Request;

class GetEsoWarehouseReceiptsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $this->authorize('claims administration');

        if ($request->warehouse_receipt) {
            $request->validate(['warehouse_receipt' => 'numeric']);

            $esoService = new EsoService();
            $esoResponse = $esoService->getWarehouseReceipt($request->warehouse_receipt)->getResponse();
        }

        return view('admin.esoWarehouseReceipts.index', [
            'navigation' => new AdministrationNavigationService(auth()->user()),
            'esoResponse' => $esoResponse ?? null,
            'warehouse_expense' => $request->warehouse_receipt
        ]);
    }
}
