<?php

namespace App\Http\Controllers\Administration;

use App\Http\Controllers\Controller;
use App\Services\Administration\AdministrationNavigationService;
use App\Services\Administration\SupplierClaimsService;
use App\Services\Administration\MailLogsService;
use Illuminate\Http\Request;
use Illuminate\View\View;


class Mail<PERSON>og<PERSON>ontroller extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:users administration']);
    }

    /**
     * List of mail logs
     * @param Request $request
     * @param MailLogsService $mailLogsService
     * @return View
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function index(Request $request, MailLogsService $mailLogsService): View
    {
        $filter = ($request->filter) ?: ((session()->get('mailLogsFilter')) ? session()->get(
            'mailLogsFilter'
        ) : []);
        $navigation = new AdministrationNavigationService(auth()->user());
        $logs = $mailLogsService->filterMailLogs($filter, 50);

        return view('admin.maillog.index', [
            'logs' => $logs,
            'filter' => $filter,
            'navigation' => $navigation,
        ]);
    }
}
