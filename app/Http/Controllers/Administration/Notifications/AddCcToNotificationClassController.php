<?php

namespace App\Http\Controllers\Administration\Notifications;

use App\Http\Controllers\Controller;
use App\Http\Requests\Administration\NotifiablesRequest;
use App\Models\Department;
use App\Models\NotificationClass;
use App\Models\User;
use Illuminate\Http\Request;

class AddCcToNotificationClassController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(NotifiablesRequest $request, NotificationClass $notificationClass)
    {
        $this->authorize('attachCc',$notificationClass);

        if ($request->department){
            $recipient = Department::find($request->department);
            $notificationClass->departments()->syncWithoutDetaching([$recipient->id=>['cc'=>1]]);
        }elseif($request->user){
            $recipient = User::find($request->user);
            $notificationClass->users()->syncWithoutDetaching([$recipient->id => ['cc'=>1]]);;
        }

        $notificationClass->createHistoryLog('updated', 'notificationCcAdded', $recipient?->email ?? null);

        return redirect(route('administration.notifications.edit', $notificationClass->id))
            ->with('status-success', __('administration.notifications.notificationCcAdded'));

    }
}
