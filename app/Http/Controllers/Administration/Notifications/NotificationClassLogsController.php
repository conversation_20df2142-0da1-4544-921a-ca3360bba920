<?php

namespace App\Http\Controllers\Administration\Notifications;

use App\Http\Controllers\Controller;
use App\Models\NotificationClass;
use Illuminate\Http\Request;

class NotificationClassLogsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, NotificationClass $notificationClass)
    {
        $this->authorize('notifications administration');


        return view('admin.notifications.logs',[
            'notificationClass'=>$notificationClass,
            'logs'=> $notificationClass->logs()->paginate(20),
        ]);
    }
}
