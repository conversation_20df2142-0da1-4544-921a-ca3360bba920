<?php

namespace App\Http\Controllers\Administration\Notifications;

use App\Enums\Notifications\RecipientsLangsEnum;
use App\Enums\Notifications\RecipientsTypesEnum;
use App\Http\Controllers\Controller;
use App\Models\Department;
use App\Models\NotificationClass;
use App\Models\User;
use App\Services\Administration\AdministrationNavigationService;
use Illuminate\Http\Request;

class NotificationClassesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $this->authorize('notifications administration');

        return view('admin.notifications.index', [
            'notificationClasses' => NotificationClass::orderBy('type')->paginate(20),
            'navigation' => new AdministrationNavigationService(auth()->user()),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(NotificationClass $notificationClass)
    {
        $this->authorize('notifications administration');

        $allDepartments = Department::all();
        $allUsers  = User::select(['id','email','lang'])->whereNotNull('email')->where('email','!=','')->get();

        $departments = $notificationClass->departments()->where('is_active',1)->get();
        $users = $notificationClass->users()->get();

        $recipients = $departments->where('pivot.cc',0)->merge($users->where('pivot.cc',0));
        $cc=$departments->where('pivot.cc',1)->merge($users->where('pivot.cc',1));


        return view('admin.notifications.edit', [
            'recipients' => $recipients,
            'cc' => $cc,
            'notificationClass' => $notificationClass,
            'recipientsTypes'=>RecipientsTypesEnum::translatedValues(),
            'recipientsLangs'=>RecipientsLangsEnum::valuesToArray(),
            'departments'=>$allDepartments,
            'users'=>$allUsers
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function update(Request $request,NotificationClass $notificationClass)
    {
        $this->authorize('notifications administration');

        $request->validate(['description'=>'required|string']);
        $notificationClass->update(['description' => $request->description]);
        $notificationClass->createHistoryLog('updated','descriptionUpdated',$request->description);

        return redirect(route('administration.notifications.edit',$notificationClass->id))
            ->with('status-success',__('administration.notifications.notificationUpdated'));
    }


}
