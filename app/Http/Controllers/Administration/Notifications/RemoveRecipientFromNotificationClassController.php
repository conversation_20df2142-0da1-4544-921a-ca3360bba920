<?php

namespace App\Http\Controllers\Administration\Notifications;

use App\Http\Controllers\Controller;

use App\Models\NotificationClass;
use Illuminate\Http\Request;

class RemoveRecipientFromNotificationClassController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, NotificationClass $notificationClass)
    {
        $this->authorize('notifications administration');

        $request->validate([
        'type'=>'required|string',
        'id'=>'required|numeric'
        ]);

        $recipient = $notificationClass->{$request->type}()->where('recipientable_id',$request->id)->first();
        $notificationClass->createHistoryLog('deleted', 'notificationRecipientRemoved',$recipient?->email);
        $notificationClass->{$request->type}()->detach($recipient->id);


        return redirect(route('administration.notifications.edit',$notificationClass->id))
            ->with('status-success', __('administration.notifications.notificationRecipientDeleted'));

    }
}
