<?php

namespace App\Http\Controllers\Administration\OverviewClaimsInTransport;

use App\Exports\ClaimsExport;
use App\Exports\OverviewOfClaimsInTransportExport;
use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\Status;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;


class DownloadOverviewClaimsInTransport extends Controller

{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $user = auth()->user();
        $this->authorize('view admin statistics / reports', $user);
        $date = date("Y.m.d");

        $filter = ($request->filter) ?: ((session()->get('claimsInTransportFilter')) ? session()->get(
            'claimsInTransportFilter'
        ) : []);

        if ($filter) {
            session()->put('claimsInTransportFilter', $filter);
        }

        $inTransportStatuses = [
            Status::CLAIM_STATUS_WAREHOUSE_EXPENSE_CREATED,
            Status::CLAIM_STATUS_IN_TRANSPORT,
        ];

        $query = Claim::query();
        $query = filterRequest($query, $filter);
        $claims = $query->whereHas('dispatchNoteWarehouseExpense')->whereIn('claim_status_id', $inTransportStatuses)
            ->with(
                'claimArticle.article',
                'centre',
                'solution',
                'status',
                'preferredSolutions',
                'dispatchNoteWarehouseExpense'
            )
            ->get()
            ->sortBy('dispatchNoteWarehouseExpense.created_at');

        return Excel::download(
            new OverviewOfClaimsInTransportExport($claims),
            "overview-of-claims-in-transport-$date.xlsx"
        );
    }
}
