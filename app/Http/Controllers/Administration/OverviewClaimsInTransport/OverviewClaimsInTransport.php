<?php

namespace App\Http\Controllers\Administration\OverviewClaimsInTransport;

use App\Enums\ClaimsTransports\ClaimsTransportsStatusesEnum;
use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\QueueLog;
use App\Models\Solution;
use App\Models\Status;
use App\Services\Administration\AdministrationNavigationService;
use App\Services\CentresService;
use App\Services\Claims\ArticlesService;
use App\Services\Claims\ClaimsNavigationService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class OverviewClaimsInTransport extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function __invoke(Request $request, ArticlesService $articlesService, CentresService $centresService)
    {
        $this->authorize('view admin statistics / reports', auth()->user());


        $claimType = new ClaimType();
        $statuses = Status::whereIn('id', [Status::CLAIM_STATUS_IN_TRANSPORT, Status::CLAIM_STATUS_WAREHOUSE_EXPENSE_CREATED])->get();
        $userCentres = $centresService->loggedUserCentres();
        $solutions = Solution::all();
        $claimTypes = $claimType->getClaimTypesForRetail();
        $trademarks = $articlesService->getAllArticlesTrademarks();
        $Centres = $centresService->allRetailCentres();
        $syncAlreadyRunning = QueueLog::where('displayName', 'App\Jobs\SyncOverviewOfClaimsInTransportJob')
            ->where('status_id', QueueLog::RUNNING)
            ->exists();


        $filter = ($request->filter) ?: ((session()->get('claimsInTransportFilter')) ? session()->get(
            'claimsInTransportFilter'
        ) : []);

        if ($filter) {
            session()->put('claimsInTransportFilter', $filter);
        }

        $inTransportStatuses = [
            Status::CLAIM_STATUS_WAREHOUSE_EXPENSE_CREATED,
            Status::CLAIM_STATUS_IN_TRANSPORT,
        ];

        $query = Claim::query();
        $query = filterRequest($query, $filter);
        $claims = $query->whereHas('dispatchNoteWarehouseExpense')->whereIn('claim_status_id', $inTransportStatuses)
            ->with(
                'transports',
                'claimArticle.article',
                'centre',
                'solution',
                'status',
                'preferredSolutions',
                'dispatchNoteWarehouseExpense.successor',
                'lastClosureLog'
            )
            ->get()
            ->sortBy('dispatchNoteWarehouseExpense.created_at');

        foreach ($claims as $claim) {
            $navigation = new ClaimsNavigationService($claim);
            $claim->firstRoute = $navigation->showClaim();

            if ($claim->claim_status_id == Status::CLAIM_STATUS_IN_TRANSPORT && $claim->transports->isNotEmpty()) {
                $transport = $claim->transports->first();
                $claim->transport = $claim->transports->first();
                $claim->days_in_transport = Carbon::parse($transport->order_confirmed_at)->diffInDays();
            }
        }

        return view('admin.overviewOfClaimsInTransport.overviewOfClaimsInTransport', [
            'navigation' => new AdministrationNavigationService(auth()->user()),
            'filter' => $filter,
            'userCentres' => $userCentres,
            'claims' => $claims,
            'centres' => $Centres,
            'solutions' => $solutions,
            'trademarks' => $trademarks,
            'statuses' => $statuses,
            'transportStatuses' => ClaimsTransportsStatusesEnum::translatedValues(),
            'claimTypes' => $claimTypes,
            'syncAlreadyRunning' => $syncAlreadyRunning
        ]);
    }


}
