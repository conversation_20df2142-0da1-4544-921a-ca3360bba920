<?php

namespace App\Http\Controllers\Administration\OverviewClaimsInTransport;

use App\Http\Controllers\Controller;
use App\Jobs\SyncOverviewOfClaimsInTransportJob;
use App\Models\Claim;
use App\Models\QueueLog;
use App\Models\Status;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;
use Throwable;

class SyncOverviewOfClaimsInTransport extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        if (QueueLog::where('displayName', 'App\Jobs\SyncOverviewOfClaimsInTransportJob')
            ->where('status_id', QueueLog::RUNNING)
            ->exists()
        ) {
            return redirect()->back()->withErrors(__('administration.statistics.syncAlreadyRunning'));
        };

        $inTransportStatuses = [
            Status::CLAIM_STATUS_WAREHOUSE_EXPENSE_CREATED,
            Status::CLAIM_STATUS_IN_TRANSPORT,
        ];

        $claims = Claim::whereHas(
            'dispatchNoteWarehouseExpense',
            function (Builder $q) {
                $q->whereNull('successor_id');
            }
        )
            ->whereIn('claim_status_id', $inTransportStatuses)
            ->with(
                'claimArticle.article',
                'centre',
                'solution',
                'status',
                'preferredSolutions',
                'dispatchNoteWarehouseExpense.successor'
            )
            ->get();

        SyncOverviewOfClaimsInTransportJob::dispatchAfterResponse($claims);

        return redirect()->back()->with(
            'status-success',
            __('administration.statistics.syncHasStartedPleaseWaitItMayTakeFewMinutes')
        );
    }
}
