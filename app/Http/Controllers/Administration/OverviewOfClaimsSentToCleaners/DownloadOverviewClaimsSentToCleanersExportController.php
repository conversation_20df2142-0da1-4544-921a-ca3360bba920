<?php

namespace App\Http\Controllers\Administration\OverviewOfClaimsSentToCleaners;

use App\Exports\ClaimsExport;
use App\Exports\OverviewOfClaimsInTransportExport;
use App\Exports\OverviewOfClaimsSentToCleanersExport;
use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\Status;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;


class DownloadOverviewClaimsSentToCleanersExportController extends Controller

{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $this->authorize('view admin statistics / reports', auth()->user());

        $filter = ($request->filter) ?: ((session()->get('claimsSentToCleanersFilter')) ? session()->get(
            'claimsSentToCleanersFilter'
        ) : []);


        $claims = Claim::has('batchesSentToCleaners')
            ->with([
                'claimArticle:claim_articles.id,claim_articles.article_id,quantity',
                'claimArticle.article:articles.id,articles.goodsid',
                'batchesSentToCleaners:warehouse_batches.id,warehouse_batch_type_id',
                'batchesSentToCleaners.SentToCleanersLog:warehouse_batches_logs.id,warehouse_batches_logs.batch_id,created_at',
                'supplierClaimArticles:id,goodsid',
                'centre:cdb_centres.id,code'
            ])
            ->filter($filter, 'claimsSentToCleanersFilter', 'Y-m-d', function (Builder $query) use ($filter) {
                $query->when(isset($filter['closure.centre']), function (Builder $query) use ($filter) {
                    $query->where(function ($subQuery) use ($filter) {
                        $subQuery->where(function ($q) use ($filter) {
                            $q->where(function ($inner) {
                                $inner->where('claim_code', 'like', 'cl-gcecz-%')
                                    ->orWhere('claim_code', 'like', 'cl-wms-vratka-p%');
                            })
                                ->whereHas('b2bB2cWarehouseDocument', function (Builder $query) use ($filter) {
                                    $query->where('from_warehouse', $filter['closure.centre']);
                                });
                        })
                            ->orWhere(function ($q) use ($filter) {
                                $q->where('claim_code', 'not like', 'cl-gcecz-%')
                                    ->where('claim_code', 'not like', 'cl-wms-vratka-p%')
                                    ->whereHas('centre', function (Builder $query) use ($filter) {
                                        $query->where('code', $filter['closure.centre']);
                                    });
                            });
                    });
                });

            })
            ->latest('claims.id')
            ->get();


        $limit = 10000;
        if ($claims->count() > $limit) {
            session()->flash('status-danger', __('general.export_limit_exceeded', ['limit' => $limit, 'count' => $claims->count()]));

            return response([
                'success' => false,
                'message' => 'Limit for export exceeded'
            ], 422);
        }


        $claims->map(function (Claim $claim) {
            $claim->centreCode = $claim->claim_type_id === ClaimType::CLAIM_TYPE_B2B_B2C_WAREHOUSE_RETURNS && preg_match('/^cl-(gcecz|wms-vratka-p)-/i', $claim->claim_code)
                ? $claim->b2bB2cWarehouseDocument?->from_warehouse
                : $claim->centre?->code;

            $claim->claimsCount = $claim->is_supplier_claim
                ? $claim->supplierClaimArticles->pluck('pivot')->sum('quantity')
                : $claim->claimArticle->quantity;

            $claim->goodsid = $claim->is_supplier_claim
                ? $claim->supplierClaimArticleExample->goodsid
                : $claim->claimArticle->article->goodsid;

            $claim->selectedBatch = $claim->batchesSentToCleaners->first();

            return $claim;
        });


        $date = date("Y.m.d");

        return Excel::download(
            new OverviewOfClaimsSentToCleanersExport($claims),
            "overview-of-claims-sent-to-cleaners-$date.xlsx"
        );
    }
}
