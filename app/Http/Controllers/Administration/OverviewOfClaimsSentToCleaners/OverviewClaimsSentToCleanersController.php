<?php

namespace App\Http\Controllers\Administration\OverviewOfClaimsSentToCleaners;

use App\Enums\ClaimsTransports\ClaimsTransportsStatusesEnum;
use App\Enums\Warehouse\BatchStatusesEnum;
use App\Http\Controllers\Controller;
use App\Models\Batch;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\QueueLog;
use App\Models\Solution;
use App\Models\Status;
use App\Models\WarehouseBatchType;
use App\Services\Administration\AdministrationNavigationService;
use App\Services\CentresService;
use App\Services\Claims\ArticlesService;
use App\Services\Claims\ClaimsNavigationService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class OverviewClaimsSentToCleanersController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function __invoke(Request $request, ArticlesService $articlesService, CentresService $centresService)
    {
        $this->authorize('view admin statistics / reports', auth()->user());

        $filter = ($request->filter) ?: ((session()->get('claimsSentToCleanersFilter')) ? session()->get(
            'claimsSentToCleanersFilter'
        ) : []);


        $claims = Claim::has('batchesSentToCleaners')
            ->with([
                'claimArticle:claim_articles.id,claim_articles.article_id,quantity',
                'claimArticle.article:articles.id,articles.goodsid',
                'batchesSentToCleaners:warehouse_batches.id,warehouse_batch_type_id',
                'batchesSentToCleaners.SentToCleanersLog:warehouse_batches_logs.id,warehouse_batches_logs.batch_id,created_at',
                'supplierClaimArticles:id,goodsid',
                'centre:cdb_centres.id,code'
            ])
            ->filter($filter, 'claimsSentToCleanersFilter', 'Y-m-d', function (Builder $query) use ($filter) {
                $query->when(isset($filter['closure.centre']), function (Builder $query) use ($filter) {
                    $query->where(function ($subQuery) use ($filter) {
                        $subQuery->where(function ($q) use ($filter) {
                            $q->where(function ($inner) {
                                $inner->where('claim_code', 'like', 'cl-gcecz-%')
                                    ->orWhere('claim_code', 'cl-wms-vratka-p%');
                            })
                                ->whereHas('b2bB2cWarehouseDocument', function (Builder $query) use ($filter) {
                                    $query->where('from_warehouse', $filter['closure.centre']);
                                });
                        })
                            ->orWhere(function ($q) use ($filter) {
                                $q->where('claim_code', 'not like', 'cl-gcecz-%')
                                    ->where('claim_code', '!=', 'cl-wms-vratka-p%')
                                    ->whereHas('centre', function (Builder $query) use ($filter) {
                                        $query->where('code', $filter['closure.centre']);
                                    });
                            });
                    });
                });
            })
            ->latest('claims.id')
            ->paginate(50);

        $claims->getCollection()
            ->map(function (Claim $claim) {

                $claim->centreCode = $claim->claim_type_id === ClaimType::CLAIM_TYPE_B2B_B2C_WAREHOUSE_RETURNS && preg_match('/^cl-(gcecz|wms-vratka-p)-/i', $claim->claim_code)
                    ? $claim->b2bB2cWarehouseDocument?->from_warehouse
                    : $claim->centre?->code;

                $claim->claimsCount = $claim->is_supplier_claim
                    ? $claim->supplierClaimArticles->pluck('pivot')->sum('quantity')
                    : $claim->claimArticle->quantity;

                $claim->goodsid = $claim->is_supplier_claim
                    ? $claim->supplierClaimArticleExample->goodsid
                    : $claim->claimArticle->article->goodsid;

                $claim->selectedBatch = $claim->batchesSentToCleaners->first();

                return $claim;
            });


        return view('admin.overviewOfClaimsSentToCleaners.overviewOfClaimsSentToCleaners', [
            'navigation' => new AdministrationNavigationService(auth()->user()),
            'filter' => $filter,
            'claims' => $claims,
            'centres' => $centresService->allCentres(),
        ]);
    }


}
