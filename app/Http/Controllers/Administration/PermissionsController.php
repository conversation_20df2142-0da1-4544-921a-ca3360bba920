<?php

namespace App\Http\Controllers\Administration;

use App\Http\Controllers\Controller;
use App\Services\Administration\AdministrationNavigationService;
use App\Services\Administration\SupplierClaimsService;
use Illuminate\View\View;
use Spatie\Permission\Models\Permission;

class PermissionsController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:users administration']);
    }

    /**
     * Handle the incoming request.
     *
     * @return View
     */
    public function __invoke(): View
    {
        $navigation = new AdministrationNavigationService(auth()->user());
        $permissions = Permission::all();

        return view('admin.permissions.index', [
            'permissions' => $permissions,
            'navigation' => $navigation,
        ]);
    }
}
