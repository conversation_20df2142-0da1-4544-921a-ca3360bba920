<?php

namespace App\Http\Controllers\Administration;

use App\Http\Controllers\Controller;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Spatie\Permission\PermissionRegistrar;

class ResetCacheController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:users administration']);
    }

    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @return RedirectResponse
     * @throws BindingResolutionException
     */
    public function __invoke(Request $request): RedirectResponse
    {
        app()->make(PermissionRegistrar::class)
            ->forgetCachedPermissions();

        return redirect()->back()->with('status-success', __('administration.permissionsCacheCleared'));
    }
}
