<?php

namespace App\Http\Controllers\Administration;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class ResetMailLogFilterController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:users administration']);
    }

    /**
     * Reset mail log filter
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function __invoke(Request $request): RedirectResponse
    {
        session()->remove('mailLogsFilter');
        return redirect(route('administration.mailLog.index'));
    }
}
