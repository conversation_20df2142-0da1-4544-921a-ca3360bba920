<?php

namespace App\Http\Controllers\Administration;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class ResetUserFilterController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:users administration']);
    }

    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function __invoke(Request $request): RedirectResponse
    {
        session()->remove('userAdministrationFilter');
        return redirect(route('administration.users.index'));
    }
}
