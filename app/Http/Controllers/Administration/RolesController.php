<?php

namespace App\Http\Controllers\Administration;

use App\Http\Controllers\Controller;
use App\Services\Administration\AdministrationNavigationService;
use App\Services\Administration\SupplierClaimsService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolesController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:users administration']);
    }

    /**
     * Display a listing of the resource.
     *
     * @return View
     */
    public function index(): View
    {
        $roles = Role::all();
        $navigation = new AdministrationNavigationService(auth()->user());

        return view('admin.roles.index', [
            'roles' => $roles,
            'navigation' => $navigation,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function store(Request $request): RedirectResponse
    {
        $role = Role::create($request->role);
        $role->syncPermissions($request->permissions);

        return redirect()->route('administration.roles.index')->with(
            'status-success',
            __('administration.role.roleWasCreated')
        );
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return View
     */
    public function create(): View
    {
        $permissions = Permission::all();

        return view('admin.roles.create', [
            'permissions' => $permissions
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param Role $role
     * @return View
     */
    public function edit(Role $role): View
    {
        $permissions = Permission::all();

        return view('admin.roles.edit', [
            'role' => $role,
            'permissions' => $permissions,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param Role $role
     * @return RedirectResponse
     */
    public function update(Request $request, Role $role): RedirectResponse
    {
        $role->update($request->role);
        $role->syncPermissions($request->permissions);

        return redirect()->route('administration.roles.index')->with(
            'status-success',
            __('administration.role.roleWasUpdated')
        );
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Role $role
     * @return RedirectResponse
     */
    public function destroy(Role $role): RedirectResponse
    {
        if (!$role->delete()) {
            return redirect()->back()->with('status-danger', __('administration.deleteFail'));
        }

        return redirect()->route('administration.roles.index')->with(
            'status-success',
            __('administration.role.roleWasDeleted')
        );
    }
}
