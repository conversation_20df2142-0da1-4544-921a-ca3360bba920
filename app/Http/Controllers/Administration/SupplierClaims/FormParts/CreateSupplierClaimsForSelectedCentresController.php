<?php

namespace App\Http\Controllers\Administration\SupplierClaims\FormParts;

use App\Http\Controllers\Controller;
use App\Jobs\SupplierClaims\CreateSupplierClaimJob;
use App\Models\Centre;
use App\Models\SupplierClaimArticle;
use App\Services\CentresService;
use Illuminate\Http\Request;


class CreateSupplierClaimsForSelectedCentresController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(
        Request              $request,
        CentresService       $centresService,
        SupplierClaimArticle $supplierClaimArticle
    )
    {
        $this->authorize('supplier claims administration');
        $request->validate(['centres.*' => 'required|exists:cdb_centres,id']);
        $supplierClaimArticle->load([
            'selectedArticles',
            'centresNotifiedLog'
        ]);
        $centres = Centre::whereIn('id', json_decode($request->centres))->get();
        $warehouseCentres = $centresService->allWarehouses();
        $centresWithoutEmailAddresses = $centres->where('email', '')->whereNotIn('code', $warehouseCentres->pluck('code')->toArray())->pluck('code');

        CreateSupplierClaimJob::dispatch($supplierClaimArticle, $centres, $warehouseCentres, auth()->user());

        $supplierClaimArticle->fill([
            'supplier_claims_was_created' => 1
        ])->save();

        $supplierClaimArticle->createHistoryLog(
            'updated',
            'supplierClaimsForSelectedCentresWasCreated',
            $centres->pluck('code')->toArray()
        );

        if ($centresWithoutEmailAddresses->isNotEmpty()) {
            $request->session()->flash(
                'status-warning',
                __(
                    'administration.supplierClaims.centresDoesntHaveEmailAddressesPleaseNotifyCentres',
                    ['centres' => createStringFromIterable($centresWithoutEmailAddresses->toArray(), true)]
                )
            );
        }

        return redirect(route('administration.supplierClaimArticles.summary', $supplierClaimArticle->id))
            ->with('status-success', __('administration.supplierClaims.claimsWasCreatedForSelectedCentres'));
    }
}
