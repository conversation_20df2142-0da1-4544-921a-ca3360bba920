<?php

namespace App\Http\Controllers\Administration\SupplierClaims\FormParts;

use App\Http\Controllers\Controller;
use App\Http\Requests\Administration\SupplierClaims\GetSupplierClaimArticleRequest;
use App\Models\Diagram;
use App\Models\SupplierClaimArticle;
use App\Services\Claims\ArticlesService;
use App\Services\VermontApiService;
use App\Traits\Responses;

class GetArticlesController extends Controller
{
    use Responses;

    /**
     * Handle the incoming request.
     */
    public function __invoke(
        GetSupplierClaimArticleRequest $request,
        ArticlesService $articlesService,
        VermontApiService $vermontApiService,
        SupplierClaimArticle $supplierClaimArticle
    ) {
        $this->authorize('claimsAdministration', auth()->user());
        $apiArticles = $vermontApiService->getAllArticles('Goods_ID', "$request->goodsid%");

        $supplierClaimArticle->createHistoryLog(
            'search',
            'searchArticles',
            [
                'request' => $request->all(),
                'response' => $apiArticles,
            ]
        );

        if (empty($apiArticles)) {
            return $this->response(message: __('general.goodsIdNotFound'));
        }

        return $this->response([
            'articles' => $apiArticles,
        ]);
    }
}
