<?php

namespace App\Http\Controllers\Administration\SupplierClaims\FormParts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\SupplierClaimArticle;
use App\Notifications\InternalNotifications\SupplierClaims\SupplierClaimCreatedNotification;
use Illuminate\Http\Request;

class NotifyCentreController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, SupplierClaimArticle $supplierClaimArticle, Claim $claim)
    {
        $this->authorize('sendSupplierClaimNotification', $claim);
        $claim->load('centre');
        $supplierClaimArticle->load('selectedArticles');
        $claim->centre->notify(new SupplierClaimCreatedNotification(claim: $claim, articles: $supplierClaimArticle->selectedArticles, decision: $supplierClaimArticle->decision));
        $claim->createHistoryLog(actionType: 'notificationSent', action: "supplierClaimWasCreated", actionUserId: auth()->id());

        return back()->with('status-success', __('administration.supplierClaims.centreWasNotified'));
    }
}
