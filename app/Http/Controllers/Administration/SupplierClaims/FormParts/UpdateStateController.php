<?php

namespace App\Http\Controllers\Administration\SupplierClaims\FormParts;

use App\Http\Controllers\Controller;
use App\Http\Requests\Administration\SupplierClaims\UpdateStateRequest;
use App\Models\SupplierClaimArticle;

class UpdateStateController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(UpdateStateRequest $request, SupplierClaimArticle $supplierClaimArticle)
    {
        $this->authorize('updateSupplierClaimArticleStatus', $supplierClaimArticle);

        $supplierClaimArticle->fill([
            'status_id' => $request->status_id,
            'decision' => $request->decision
        ])->save();

        $supplierClaimArticle->createHistoryLog(
            'updated',
            'statusWasUpdated',
            $supplierClaimArticle->toArray()
        );

        return back()->with('status-success', __('statuses.statusWasUpdated'));
    }
}
