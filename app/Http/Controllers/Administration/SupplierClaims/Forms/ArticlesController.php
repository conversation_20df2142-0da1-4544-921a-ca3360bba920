<?php

namespace App\Http\Controllers\Administration\SupplierClaims\Forms;

use App\Http\Controllers\Controller;
use App\Http\Requests\Administration\SupplierClaims\SupplierClaimArticlesRequest;
use App\Models\SupplierClaimArticle;
use App\Services\Administration\SupplierClaimArticlesNavigationService;
use App\Services\Claims\ArticlesService;

class ArticlesController extends Controller
{

    /**
     * Show the form for creating a new resource.
     */
    public function create(SupplierClaimArticle $supplierClaimArticle)
    {
        $this->authorize('isAccessible', $supplierClaimArticle);
        $supplierClaimArticlesNavigationService = new SupplierClaimArticlesNavigationService($supplierClaimArticle);
        return view('admin.supplierClaims.forms.articles', [
            'navigation' => $supplierClaimArticlesNavigationService,
            'formActionUrl' => route('administration.supplierClaimArticles.articles.store', $supplierClaimArticle->id),
            'formMethod' => 'POST',
            'supplierClaimArticle' => $supplierClaimArticle
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(
        SupplierClaimArticlesRequest $request,
        ArticlesService $articlesService,
        SupplierClaimArticle $supplierClaimArticle
    ) {

        if (!auth()->user()->can('createOrUpdateSupplierClaimArticleForms', $supplierClaimArticle)) {
            return back()->with('status-danger', __('general.unauthorized'));
        }
        $allArticles = collect($request->articles);
        $selectedArticles = $allArticles->where('selected', true);
        $ArticleOrderGroup = getOrderGroup('name', $allArticles->first()['order_group']);

        if (is_null($ArticleOrderGroup)){
            return  back()->withErrors(__('article.orderGroupMissing'));
        }


        $articlesForSave = $allArticles->map(function ($article, $key) use ($ArticleOrderGroup) {
            $article['order_group_id'] = $ArticleOrderGroup->id;
            $article['created_at'] = now();
            $article['updated_at'] = now();
            unset($article['order_group'], $article['id'], $article['selected']);
            return $article;
        });

        $savedArticles = $articlesService->saveNewArticles($articlesForSave);
        $articlesForAttach = $savedArticles->mapWithKeys(function ($article, $key) use ($selectedArticles) {
            $article->is_selected = in_array($article->goodsid, $selectedArticles->pluck('goodsid')->toArray()) ? 1 : 0;
            return [$article->id => ['is_selected' => $article->is_selected]];
        });

        $article = $savedArticles->first();
        $articleCode = sliceGoodsId($article->goodsid,0,4);

        $supplierClaimArticle->fill([
            'article_code' => $articleCode,
            'diagram_id' => $ArticleOrderGroup?->diagram_id
        ])->save();

        $supplierClaimArticle->createHistoryLog(
            'updated',
            'articlesFormWasUpdated',
            $supplierClaimArticle->toArray()
        );

        $supplierClaimArticle->articles()->sync($articlesForAttach->toArray());
        $supplierClaimArticle->createHistoryLog(
            'updated',
            'articlesWasUpdated',
            $supplierClaimArticle->articles()->get()->pluck('pivot')->toArray()
        );

        $navigation = new SupplierClaimArticlesNavigationService($supplierClaimArticle);


        return redirect($navigation->nextRoute())
            ->with('status-success', __('claims.messages.articlesWasSaved'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SupplierClaimArticle $supplierClaimArticle)
    {
        $this->authorize('isAccessible', $supplierClaimArticle);
        $supplierClaimArticlesNavigationService = new SupplierClaimArticlesNavigationService($supplierClaimArticle);
        $articles = $supplierClaimArticle->articles()
            ->get()
            ->map(function ($article, $key) {
                $article->selected = $article->pivot->is_selected;
                return $article;
            });

        return view('admin.supplierClaims.forms.articles', [
            'navigation' => $supplierClaimArticlesNavigationService,
            'formActionUrl' => route('administration.supplierClaimArticles.articles.update', $supplierClaimArticle->id),
            'formMethod' => 'PUT',
            'supplierClaimArticle' => $supplierClaimArticle,
            'articles' => $articles,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(
        SupplierClaimArticlesRequest $request,
        ArticlesService $articlesService,
        SupplierClaimArticle $supplierClaimArticle
    ) {
        if (!auth()->user()->can('createOrUpdateSupplierClaimArticleForms', $supplierClaimArticle)) {
            return back()->with('status-danger', __('general.unauthorized'));
        }
        $allArticles = collect($request->articles);

        if ($request->goodsid != $supplierClaimArticle->article_code) {
            $selectedArticles = $allArticles->where('selected', true);
            $ArticleOrderGroup = getOrderGroup('name', $allArticles->first()['order_group']);


            $articlesForSave = $allArticles->map(function ($article, $key) use ($ArticleOrderGroup) {
                $article['order_group_id'] = $ArticleOrderGroup->id;
                $article['created_at'] = now();
                $article['updated_at'] = now();
                unset($article['order_group'], $article['id'], $article['selected']);
                return $article;
            });

            $savedArticles = $articlesService->saveNewArticles($articlesForSave);
            $articlesForAttach = $savedArticles->mapWithKeys(function ($article, $key) use ($selectedArticles) {
                $article->is_selected = in_array(
                    $article->goodsid,
                    $selectedArticles->pluck('goodsid')->toArray()
                ) ? 1 : 0;
                return [$article->id => ['is_selected' => $article->is_selected]];
            });

            $article = $savedArticles->first();
            $articleCode = sliceGoodsId($article->goodsid,0,4);

            $supplierClaimArticle->articles()->sync($articlesForAttach->toArray());
            $supplierClaimArticle->fill([
                'article_code' => $articleCode,
                'diagram_id' => $ArticleOrderGroup?->diagram_id
            ])->save();

            $supplierClaimArticle->createHistoryLog(
                'updated',
                'articlesFormWasUpdated',
                $supplierClaimArticle->toArray()
            );
        } else {
            $articlesForAttach = $allArticles->mapWithKeys(function ($article, $key) {
                return [$article['id'] => ['is_selected' => $article['selected']]];
            });
            $supplierClaimArticle->articles()->sync($articlesForAttach->toArray());

            $supplierClaimArticle->createHistoryLog(
                'updated',
                'articlesWasUpdated',
                $supplierClaimArticle->articles()->get()->pluck('pivot')->toArray()
            );
        }

        $navigation = new SupplierClaimArticlesNavigationService($supplierClaimArticle);
        return redirect($navigation->nextRoute())
            ->with('status-success', __('claims.messages.articlesWasSaved'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
