<?php

namespace App\Http\Controllers\Administration\SupplierClaims\Forms;

use App\Http\Controllers\Controller;

use App\Http\Requests\Administration\SupplierClaims\SupplierClaimDamagesRequest;
use App\Models\DamagePlace;
use App\Models\DamageType;
use App\Models\SupplierClaimArticle;
use App\Services\Administration\SupplierClaimArticleService;
use App\Services\Administration\SupplierClaimArticlesNavigationService;
use Illuminate\Http\Request;

class DamagesController extends Controller
{

    /**
     * Show the form for creating a new resource.
     */
    public function create(SupplierClaimArticle $supplierClaimArticle)
    {
        $this->authorize('isAccessible', $supplierClaimArticle);
        $supplierClaimArticlesNavigationService = new SupplierClaimArticlesNavigationService($supplierClaimArticle);
        $orderGroupId = $supplierClaimArticle->articles()->whereNotNull('order_group_id')->first()->order_group_id;

        $damageTypes = DamageType::byOrderGroup($orderGroupId)
            ->subTypes()
            ->withOther()
            ->onlyActive()
            ->withParent()
            ->formatCollectionForSelectInput();


        return view('admin.supplierClaims.forms.damages', [
            'navigation' => $supplierClaimArticlesNavigationService,
            'formActionUrl' => route('administration.supplierClaimArticles.damages.store', $supplierClaimArticle->id),
            'formMethod' => 'POST',
            'supplierClaimArticle' => $supplierClaimArticle,
            'damageTypes'=>$damageTypes,
            //'damagePlaces'=>DamagePlace::byOrderGroup($orderGroupId)->get(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(
        SupplierClaimDamagesRequest $request,
        SupplierClaimArticleService $supplierClaimArticleService,
        SupplierClaimArticle $supplierClaimArticle
    ) {
        if (!auth()->user()->can('createOrUpdateSupplierClaimArticleForms', $supplierClaimArticle)) {
            return back()->with('status-danger', __('general.unauthorized'));
        }
        $supplierClaimArticle = $supplierClaimArticleService->updateArticleDamage(
            $supplierClaimArticle,
            (object)$request->validated()
        );

        $supplierClaimArticle->createHistoryLog(
            'updated',
            'damageFormWasUpdated',
            $supplierClaimArticle->toArray()
        );

        $navigation = new SupplierClaimArticlesNavigationService($supplierClaimArticle);
        return redirect($navigation->nextRoute())->with('status--success', __('damageFormSaved'));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SupplierClaimArticle $supplierClaimArticle)
    {
        $this->authorize('isAccessible', $supplierClaimArticle);
        $supplierClaimArticlesNavigationService = new SupplierClaimArticlesNavigationService($supplierClaimArticle);
        $orderGroupId = $supplierClaimArticle->articles()->whereNotNull('order_group_id')->first()->order_group_id;

        if (auth()->user()->cannot('createOrUpdateSupplierClaimArticleForms',$supplierClaimArticle)){
            $supplierClaimArticle->load('damageType','damageSubType');
        } else {
            $damageTypes = DamageType::byOrderGroup($orderGroupId)
                ->subTypes()
                ->withOther()
                ->onlyActive()
                ->withParent()
                ->formatCollectionForSelectInput();
        }

        return view('admin.supplierClaims.forms.damages', [
            'navigation' => $supplierClaimArticlesNavigationService,
            'formActionUrl' => route('administration.supplierClaimArticles.damages.update', $supplierClaimArticle->id),
            'formMethod' => 'PUT',
            'supplierClaimArticle' => $supplierClaimArticle,
            'diagram' => "supplier-claim-articles/$supplierClaimArticle->id/diagram.png",
            'damageTypes'=>$damageTypes ?? [],
            //'damagePlaces'=>DamagePlace::byOrderGroup($orderGroupId)->get(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(
        SupplierClaimDamagesRequest $request,
        SupplierClaimArticleService $supplierClaimArticleService,
        SupplierClaimArticle $supplierClaimArticle
    ) {
        if (!auth()->user()->can('createOrUpdateSupplierClaimArticleForms', $supplierClaimArticle)) {
            return back()->with('status-danger', __('general.unauthorized'));
        }
        $supplierClaimArticle = $supplierClaimArticleService->updateArticleDamage(
            $supplierClaimArticle,
            (object)$request->validated()
        );

        $supplierClaimArticle->createHistoryLog(
            'updated',
            'damageFormWasUpdated',
            $supplierClaimArticle->toArray()
        );
        $navigation = new SupplierClaimArticlesNavigationService($supplierClaimArticle);
        return redirect($navigation->nextRoute())->with('status--success', __('damageFormSaved'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
