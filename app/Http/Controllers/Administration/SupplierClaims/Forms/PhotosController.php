<?php

namespace App\Http\Controllers\Administration\SupplierClaims\Forms;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\StorePhotoRequest;
use App\Models\Photo;
use App\Models\SupplierClaimArticle;
use App\Models\Survey;
use App\Services\Administration\SupplierClaimArticlesNavigationService;
use App\Services\FileService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Pion\Laravel\ChunkUpload\Exceptions\UploadMissingFileException;
use Pion\Laravel\ChunkUpload\Receiver\FileReceiver;

class PhotosController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(SupplierClaimArticle $supplierClaimArticle)
    {
        $this->authorize('isAccessible', $supplierClaimArticle);
        $navigation = new SupplierClaimArticlesNavigationService($supplierClaimArticle);
        $photos = $supplierClaimArticle->photos->toArray();

        return view('admin.supplierClaims.forms.photos', [
            'navigation' => $navigation,
            'photos' => array_values($photos),
            'supplierClaimArticle' => $supplierClaimArticle,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(
        StorePhotoRequest $request,
        FileService $fileService,
        FileReceiver $receiver,
        SupplierClaimArticle $supplierClaimArticle
    ) {
        if (!auth()->user()->can('createOrUpdateSupplierClaimArticleForms', $supplierClaimArticle)) {
            return back()->with('status-danger', __('general.unauthorized'));
        }

        if ($receiver->isUploaded() === false) {
            throw new UploadMissingFileException();
        }

        $save = $receiver->receive();
        if ($save->isFinished()) {


            $file = $save->getFile();

            //Size validation
            if ($fileService->validateFileSize($file)) {
                unlink($save->getFile()->getPathname());

                return $this->baseErrorJsonResponse(__('photos.file_size_exceeded', ['size' => config('media.mb_size_limit_for_video')]));
            }

            //file validation
            if ($fileService->validateFileType($file->getClientOriginalExtension())) {
                unlink($save->getFile()->getPathname());
                Log::info('Unsuported file type uploaded: ' . $file->getClientOriginalExtension());
                return $this->baseErrorJsonResponse(__('photos.unsupported_file_type', ['ext' => $file->getClientOriginalExtension()]));
            }

            $file = $fileService->storeUploadedFile($file, "/supplier-claim-articles/$supplierClaimArticle->id");



            $file = $supplierClaimArticle->photos()->create([
                'name' => $file->name,
                'file_path' => $file->file_path,
                'thumbnail_path' => $file->thumbnail_path ?? null,
                'preview_path' => $file->preview_path ?? null,
                'size' => $file->size,
                'extension' => $file->extension,
            ]);

            $supplierClaimArticle->createHistoryLog(
                'updated',
                'fileUploaded',
                $file->toArray()
            );

            return $file;
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FileService $fileService, SupplierClaimArticle $supplierClaimArticle, Photo $photo): bool
    {
        $this->authorize('createOrUpdateSupplierClaimArticleForms', $supplierClaimArticle);

        $supplierClaimArticle->createHistoryLog(
            'delete',
            'FileDeleted',
            $photo->toArray()
        );


        $fileService->deletePhoto($photo->thumbnail_path);

        if (isset($photo->thumbnail_path)) {
            $fileService->deletePhoto($photo->thumbnail_path);
        }

        if (isset($photo->preview_path)) {
            $fileService->deletePhoto($photo->preview_path);
        }

        return $photo->delete();
    }
}
