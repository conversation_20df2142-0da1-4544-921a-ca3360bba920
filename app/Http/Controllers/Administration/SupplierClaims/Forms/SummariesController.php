<?php

namespace App\Http\Controllers\Administration\SupplierClaims\Forms;

use App\Http\Controllers\Controller;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\SupplierClaimArticle;
use App\Services\Administration\SupplierClaimArticlesNavigationService;
use App\Services\StatusesService;
use Illuminate\Http\Request;

class SummariesController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(
        Request              $request,
        StatusesService      $statusesService,
        SupplierClaimArticle $supplierClaimArticle
    )
    {
        $this->authorize('isAccessible', $supplierClaimArticle);
        $supplierClaimArticle->load('diagram', 'selectedArticles', 'creator', 'status');
        $centres = Centre::orderBy('code', 'asc')->get();

        $createdSupplierClaims = Claim::select(['id','claim_centre_id','supplier_claim_article_id','claim_code','claim_status_id','warehouse_centre_id'])
            ->where('supplier_claim_article_id', $supplierClaimArticle->id)
            ->with('supplierClaimCreatedLog:id,claim_history_logs.claim_id,created_at','centre:id,code','status:id,name','warehouseCentre:id,code')
            ->paginate();



        return view('admin.supplierClaims.forms.summaries', [
            'navigation' => new SupplierClaimArticlesNavigationService($supplierClaimArticle),
            'supplierClaimArticle' => $supplierClaimArticle,
            'statuses' => $statusesService->allSupplierClaimArticleStatuses(),
            'diagram' => "supplier-claim-articles/$supplierClaimArticle->id/diagram.png",
            'centres' => $centres,
            'createdSupplierClaims' => $createdSupplierClaims
        ]);
    }
}
