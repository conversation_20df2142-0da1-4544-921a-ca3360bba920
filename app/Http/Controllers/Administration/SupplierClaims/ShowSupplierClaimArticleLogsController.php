<?php

namespace App\Http\Controllers\Administration\SupplierClaims;

use App\Http\Controllers\Controller;
use App\Models\SupplierClaimArticle;
use App\Services\Administration\SupplierClaimArticlesNavigationService;
use Illuminate\Http\Request;

class ShowSupplierClaimArticleLogsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, SupplierClaimArticle $supplierClaimArticle)
    {
        $this->authorize('viewSupplierClaimArticleLogs', $supplierClaimArticle);
        $supplierClaimArticle->generalHistoryLogs()->with('actionUser')->paginate('50');
        $navigation = new SupplierClaimArticlesNavigationService($supplierClaimArticle);

        return view('admin.supplierClaims.logs', [
            'supplierClaimArticle' => $supplierClaimArticle,
            'logs' => $supplierClaimArticle->generalHistoryLogs()->with('actionUser')->paginate('50'),
            'redirectUrl' => $navigation->firstAvailableRoute()
        ]);
    }
}
