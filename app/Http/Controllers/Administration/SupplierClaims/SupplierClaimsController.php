<?php

namespace App\Http\Controllers\Administration\SupplierClaims;

use App\Http\Controllers\Controller;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\Status;
use App\Models\SupplierClaimArticle;
use App\Services\Administration\AdministrationNavigationService;
use App\Services\Administration\SupplierClaimArticlesNavigationService;
use App\Services\StatusesService;
use Illuminate\Http\Request;

class SupplierClaimsController extends Controller
{

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, StatusesService $statusesService)
    {
        $this->authorize('claimsAdministration', auth()->user());

        $supplierClaimArticlesQuery = SupplierClaimArticle::query();

        $filter = ($request->filter) ?: ((session()->get('supplierClaimArticleFilter')) ? session()->get(
            'supplierClaimArticleFilter'
        ) : []);

        if ($filter) {
            session()->put('supplierClaimArticleFilter', $filter);
        }

        $supplierClaimArticlesQuery = filterRequest($supplierClaimArticlesQuery, $filter);
        $supplierClaimArticles = $supplierClaimArticlesQuery
            ->with('status')
            ->withCount([
                'claims' => function ($query) {
                    $query->where('claim_status_id', '!=', Status::CLAIM_STATUS_CANCELED);
                },
                'claims as received_in_3S12_claims_count' => function ($query) {
                    $query->where('warehouse_centre_id', Centre::Warehouse_3S12)
                        ->where('claim_status_id', '!=', Status::CLAIM_STATUS_CANCELED);
                },
                'claims as other_warehouses_claims_count' => function ($query) {
                    $query->where('claim_status_id', '!=', Status::CLAIM_STATUS_CANCELED)
                        ->where(function ($query) {
                            $query->where('warehouse_centre_id', '!=', Centre::Warehouse_3S12)
                                ->orWhere('warehouse_centre_id', null);
                        });

                },
            ])
            ->latest('id')
            ->paginate(20);


        return view('admin.supplierClaims.index', [
            'navigation' => new AdministrationNavigationService(auth()->user()),
            'supplierClaimArticles' => $supplierClaimArticles,
            'statuses' => $statusesService->allSupplierClaimArticleStatuses(),
            'filter' => $filter ?? null
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('claimsAdministration', auth()->user());
        $supplierClaimArticle = SupplierClaimArticle::create([
            'status_id' => Status::SUPPLIER_CLAIM_ARTICLE_CREATED,
            'creator_id' => auth()->id()
        ]);

        $supplierClaimArticle->createHistoryLog('created', 'supplierClaimArticleWasCreated');
        $supplierClaimArticlesNavigationService = new SupplierClaimArticlesNavigationService($supplierClaimArticle);


        return redirect($supplierClaimArticlesNavigationService->firstAvailableRoute());
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(
        Request              $request,
        SupplierClaimArticle $supplierClaimArticle
    )
    {
        $supplierClaimArticlesNavigationService = new SupplierClaimArticlesNavigationService($supplierClaimArticle);
        $this->authorize('claimsAdministration', auth()->user());
        return redirect($supplierClaimArticlesNavigationService->firstAvailableRoute($supplierClaimArticle));
    }


    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SupplierClaimArticle $supplierClaimArticle)
    {
        $this->authorize('claimsAdministration', auth()->user());

        if (Claim::where('supplier_claim_article_id', $supplierClaimArticle->id)->exists()) {
            return back()
                ->with('status-danger', __('claims.messages.articleCannotBeDeletedBecauseContainsExistingClaims'));
        }

        $supplierClaimArticle->delete();

        return redirect(route('administration.supplierClaimArticles.index'))
            ->with('status-success', __('claims.messages.articleWasDeleted'));
    }
}
