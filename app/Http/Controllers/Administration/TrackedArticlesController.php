<?php

namespace App\Http\Controllers\Administration;

use App\Http\Controllers\Controller;
use App\Http\Requests\Administration\StoreTrackedArticleRequest;
use App\Models\Claim;
use App\Models\Status;
use App\Models\TrackedArticle;
use App\Services\Administration\AdministrationNavigationService;
use App\Services\Claims\ArticlesService;
use App\Services\Claims\ClaimsNavigationService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class TrackedArticlesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, ArticlesService $articleService)
    {
        $this->authorize('supplier claims administration');
        $trademarks = $articleService->getAllArticlesTrademarks();

        $filter = ($request->filter) ?: ((session()->get('trackingArticlesFilter')) ? session()->get(
            'trackingArticlesFilter'
        ) : []);

        if ($filter) {
            session()->put('trackingArticlesFilter', $filter);
        }

        $query = TrackedArticle::query();
        $query = filterRequest($query, $filter);
        $trackedArticle = $query->with('creator', 'status')
            ->withCount('claims')
            ->paginate(20);

        return view('admin.trackedArticles.index', [
            'filter' => $filter,
            'navigation' => new AdministrationNavigationService(auth()->user()),
            'trademarks' => $trademarks,
            'trackedArticles' => $trackedArticle
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('supplier claims administration');
        return view('admin.trackedArticles.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreTrackedArticleRequest $request)
    {
        $this->authorize('supplier claims administration');
        $trackedArticle = TrackedArticle::create([
            'article_code' => $request->article,
            'creator_id' => auth()->id(),
            'status_id' => Status::TRACKED_ARTICLE_ACTIVE
        ]);

        $trackedArticle->createHistoryLog('created', 'trackedArticleWasCreated', $trackedArticle->toArray());

        $alreadyCreatedClaimsIds = Claim::whereHas('claimArticle.article', function (Builder $q) use ($trackedArticle) {
            $q->where('goodsid', 'LIKE', "$trackedArticle->article_code%");
        })
            ->get()
            ->pluck('id')
            ->toArray();

        $trackedArticle->claims()->attach($alreadyCreatedClaimsIds);

        return redirect(route('administration.trackedArticles.index'))
            ->with('status-success', __('administration.trackedArticles.articleWasAddedToTrackedList'));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $this->authorize('supplier claims administration');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TrackedArticle $trackedArticle)
    {
        $this->authorize('supplier claims administration');
        $trackedArticle->load('claims.claimArticle.article', 'creator', 'status',);
        $status = new Status();

        foreach ($trackedArticle->claims as $claim) {
            $navigation = new ClaimsNavigationService($claim);
            $claim->showRoute = $navigation->firstRoute();
        }

        return view('admin.trackedArticles.edit', [
            'trackedArticle' => $trackedArticle,
            'statuses' => $status->getTrackedArticleStatuses(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TrackedArticle $trackedArticle)
    {
        $this->authorize('supplier claims administration');
        $request->validate(['status_id' => 'required|string']);
        $trackedArticle->fill([
            'status_id' => $request->status_id,
            'finished_at' => $request->status_id == Status::TRACKED_ARTICLE_INACTIVE ? now() : null,
        ])->save();

        $trackedArticle->createHistoryLog('updated', 'trackedArticleWasUpdated', $trackedArticle->toArray());

        return redirect(route('administration.trackedArticles.index'))
            ->with('status-success', __('administration.trackedArticles.articleWasUpdated'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TrackedArticle $trackedArticle)
    {
        $this->authorize('supplier claims administration');

        $trackedArticle->createHistoryLog('deleted', 'trackedArticleWasDeleted', $trackedArticle->toArray());
        $trackedArticle->delete();

        return redirect(route('administration.trackedArticles.index'))
            ->with('status-success', __('administration.trackedArticles.articleWasDeleted'));
    }
}
