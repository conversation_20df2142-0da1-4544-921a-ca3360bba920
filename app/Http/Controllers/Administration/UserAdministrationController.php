<?php

namespace App\Http\Controllers\Administration;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Repositories\UserRepository;
use App\Services\Administration\AdministrationNavigationService;
use App\Services\Administration\SupplierClaimsService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\View\View;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class UserAdministrationController extends Controller
{
    private SupplierClaimsService $navigation;

    public function __construct()
    {
        $this->middleware(['permission:users administration']);
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return View
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function index(Request $request): View
    {
        $roles = Role::all();
        $filter = ($request->filter) ?: ((session()->get('administrationFilter')) ? session()->get(
            'administrationFilter'
        ) : []);
        $navigation = new AdministrationNavigationService(auth()->user());

        $query = User::query();
        if ($filter) {
            $query = filterRequest($query, $filter);
        }

        $users = $query->with(['roles'])->latest()->paginate(50);

        return view('admin.users.index', [
            'users' => $users,
            'roles' => $roles,
            'filter' => $filter,
            'navigation' => $navigation,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function store(Request $request): RedirectResponse
    {
        $user = User::create([
            'login' => $request->input('login'),
            'password' => Hash::make($request->input('password')),
            'name' => $request->input('name'),
            'email' => $request->input('email'),
        ]);

        $user->syncRoles($request->input('roles'));
        $user->syncPermissions($request->input('permissions'));

        return redirect()->route('administration.users.index')->with(
            'status-success',
            __('administration.user.userWasCreated')
        );
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return View
     */
    public function create(): View
    {
        $roles = Role::all();
        $permissions = Permission::all();

        return view('admin.users.create', [
            'roles' => $roles,
            'permissions' => $permissions,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param User $user
     * @return View
     */
    public function edit(User $user): View
    {
        $roles = Role::all();
        $permissions = Permission::all();

        return view('admin.users.edit', [
            'user' => $user,
            'roles' => $roles,
            'permissions' => $permissions,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param User $user
     * @return RedirectResponse
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        if (!$user->update([
            'login' => $request->login,
            'name' => $request->name,
            'email' => $request->email,
        ])) {
            return redirect()->back()->with('status-danger', __('administration.deleteFail'));
        };

        $user->syncRoles($request->roles);
        $user->syncPermissions($request->permissions);

        return redirect()->route('administration.users.index')->with(
            'status-success',
            __('administration.user.userWasUpdated')
        );
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param User $user
     * @return RedirectResponse
     */
    public function destroy(User $user): RedirectResponse
    {
        if (!$user->delete()) {
            redirect()->back()->with('status-danger', __('administration.deleteFail'));
        }

        return redirect()->back()->with('status-success', __('administration.user.userWasDeleted'));
    }
}
