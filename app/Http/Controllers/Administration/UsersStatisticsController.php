<?php

namespace App\Http\Controllers\Administration;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\Administration\AdministrationNavigationService;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

class UsersStatisticsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('view admin statistics / reports', auth()->user());

        $roles = Role::all();
        $request->validate(['range.*' => 'date']);
        $filter = $request->filter;
        $navigation = new AdministrationNavigationService(auth()->user());

        if ($filter) {
            $from = Carbon::parse(date('Y-m-d', strtotime($request->date_range['from'])))->startOfDay();
            $to = Carbon::parse(date('Y-m-d', strtotime($request->date_range['to'])))->endOfDay();
            $query = User::query();
            $query = filterRequest($query, $filter);
            $difference = $from->diffInDays($to);
            $errors = [];
            $filteredUsers = $query
                ->select(['id', 'name'])
                ->get();

            if ($filteredUsers->count() > 50) {
                $errors[] = __('administration.user.usersCountCannotBeGreaterThan', ['no' => 50]);
            }

            if ($difference > 365) {
                $errors[] = __('administration.user.datesRangeCannotBeGreaterThanOneYear');
            }

            if (!empty($errors)) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors($errors);
            }

            $period = CarbonPeriod::create($from, $to);
            $range = [];
            foreach ($period->toArray() as &$date) {
                $range[$date->format("Y-m-d")] = 0;
            }

            $filteredUsers
                ->load([
                    'claims' =>
                        function ($q) use ($from, $to) {
                            $q->select([
                                DB::raw("DATE(created_at) as date"),
                                'claim_creator_id',
                                DB::raw('count(*) as actions')
                            ])
                                ->whereBetween('created_at', [$from, $to])
                                ->with('user:id,name')
                                ->groupBy('date', 'claims.claim_creator_id');
                        },
                    'claimHistoryLogs' =>
                        function ($q) use ($from, $to) {
                            $q->select([
                                DB::raw("DATE(created_at) as date"),
                                'action_user_id',
                                DB::raw('count(*) as actions')
                            ])
                                ->whereBetween('created_at', [$from, $to])
                                ->with('actionUser:id,name')
                                ->groupBy('date', 'claim_history_logs.action_user_id');
                        },
                ]);


            foreach ($filteredUsers as &$user) {
                $actionsRange = $range;
                $claimsRange = $range;

                $actions = collect()
                    ->merge($user->claimHistoryLogs)
                    ->groupBy('date')
                    ->sortBy(function ($value, $key) {
                        return strtotime($key);
                    }, SORT_REGULAR, true);

                $createdClaims = collect()->merge($user->claims)
                    ->groupBy('date')
                    ->sortBy(function ($value, $key) {
                        return strtotime($key);
                    }, SORT_REGULAR, true);

                foreach ($actions as $key => $statistic) {
                    $actionsRange[$key] += $statistic[0]->actions ?? 0;
                }
                foreach ($createdClaims as $key => $statistic) {
                    $claimsRange[$key] += $statistic[0]->actions ?? 0;
                }
                $user->createdClaims = array_values($claimsRange);
                $user->actions = array_values($actionsRange);
            }
        }

        return view('admin.usersStatistics.index', [
            'users' => User::all(),
            'range' => array_keys($range ?? []),
            'dateRange' => $request?->date_range,
            'filteredUsers' => isset($filteredUsers) ? $filteredUsers->toArray() : null,
            'roles' => $roles,
            'filter' => $filter,
            'navigation' => $navigation,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
