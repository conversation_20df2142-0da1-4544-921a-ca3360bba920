<?php

namespace App\Http\Controllers\Administration\WarehouseReceivingSettings;

use App\Http\Controllers\Controller;
use App\Models\ReceivingSetting;
use App\Models\User;
use App\Services\Administration\AdministrationNavigationService;
use Illuminate\Http\Request;

class DeletedReceivingSettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:warehouse administration']);
    }
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $navigation = new AdministrationNavigationService(auth()->user());
        $receivingSettings = ReceivingSetting::onlyTrashed()->with(
            'trademark',
            'orderGroup',
            'creator',
            'seasons:id,code',
            'suppliers'
        )->get();

        return view('admin.warehouseReceivingSettings.deletedReceivingSettings', [
            'receivingSettings' => $receivingSettings,
            'navigation' => $navigation
        ]);
    }
}
