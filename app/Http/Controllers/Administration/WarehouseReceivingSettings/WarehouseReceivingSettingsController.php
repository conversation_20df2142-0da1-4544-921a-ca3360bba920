<?php

namespace App\Http\Controllers\Administration\WarehouseReceivingSettings;

use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\StoreReceivingSettingRequest;
use App\Http\Requests\Warehouse\UpdateReceivingSettingRequest;
use App\Models\OrderGroup;
use App\Models\ReceivingSetting;
use App\Models\Season;
use App\Models\Trademark;
use App\Models\User;
use App\Services\Administration\AdministrationNavigationService;
use App\Services\Claims\ArticlesService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class WarehouseReceivingSettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:warehouse administration']);
    }

    /**
     * Display a listing of the resource.
     *
     * @return View
     * @throws AuthorizationException
     */
    public function index(): View
    {
        $this->authorize('viewWarehouse', User::class);
        $navigation = new AdministrationNavigationService(auth()->user());
        $receivingSettings = ReceivingSetting::with(
            'trademark',
            'orderGroup',
            'creator',
            'seasons:id,code',
            'suppliers'
        )->get();

        return view('admin.warehouseReceivingSettings.index', [
            'receivingSettings' => $receivingSettings,
            'navigation' => $navigation
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreReceivingSettingRequest $request
     * @return RedirectResponse
     * @throws AuthorizationException
     */
    public function store(StoreReceivingSettingRequest $request): RedirectResponse
    {
        $this->authorize('createWarehouseReceivingSettings', User::class);

        $receivingSetting = ReceivingSetting::create([
            'creator_id' => auth()->id(),
            'trademark_id' => $request->trademark_id,
            'order_group_id' => $request->order_group_id,
        ]);
        $suppliers = [];

        foreach ($request->suppliers as $supplier) {
            $suppliers[] = ['supplier_name' => $supplier];
        }

        $receivingSetting->suppliers()->createMany($suppliers);
        $receivingSetting->seasons()->sync((array)$request->seasons);


        //logs
        $log = $receivingSetting->load('seasons:id,name', 'suppliers:id,supplier_name,receiving_setting_id')->toArray();
        $receivingSetting->createHistoryLog('created', 'receivingSettingsCreated', createStringFromIterable($log));

        return redirect(route('administration.warehouseReceiving.index'))
            ->with('status-success', __('receiving.receivingSettingWasCreated'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @param ArticlesService $articlesService
     * @return View
     */
    public function create(ArticlesService $articlesService): View
    {
        $this->authorize('createWarehouseReceivingSettings', User::class);
        $suppliers = $articlesService->getAllArticlesSuppliers();

        return view('admin.warehouseReceivingSettings.create', [
            'trademarks' => Trademark::all(),
            'orderGroups' => OrderGroup::all(),
            'seasons' => Season::orderBy('id', 'DESC')->get(),
            'suppliers' => $suppliers,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param ArticlesService $articlesService
     * @param ReceivingSetting $receivingSetting
     * @return View
     */
    public function edit(ArticlesService $articlesService, ReceivingSetting $receivingSetting): View
    {
        $this->authorize('updateWarehouseReceivingSettings', User::class);
        $receivingSetting->load('trademark', 'orderGroup', 'creator', 'seasons:id,code');
        $suppliers = $articlesService->getAllArticlesSuppliers();


        return view('admin.warehouseReceivingSettings.edit', [
            'receivingSetting' => $receivingSetting,
            'trademarks' => Trademark::all(),
            'orderGroups' => OrderGroup::all(),
            'seasons' => Season::orderBy('id', 'DESC')->get(),
            'suppliers' => $suppliers,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateReceivingSettingRequest $request
     * @param ReceivingSetting $receivingSetting
     * @return RedirectResponse
     * @throws AuthorizationException
     */
    public function update(UpdateReceivingSettingRequest $request, ReceivingSetting $receivingSetting): RedirectResponse
    {
        $this->authorize('updateWarehouseReceivingSettings', User::class);

        $receivingSetting->update([
            'creator_id' => auth()->id(),
            'trademark_id' => $request->trademark_id,
            'order_group_id' => $request->order_group_id,
        ]);

        $receivingSetting->suppliers()->delete();
        $suppliers = [];

        foreach ($request->suppliers as $supplier) {
            $suppliers[] = ['supplier_name' => $supplier];
        }

        $receivingSetting->suppliers()->createMany($suppliers);
        $receivingSetting->seasons()->sync((array)$request->seasons);

        //logs
        $log = $receivingSetting->load('seasons:id,name', 'suppliers:id,supplier_name,receiving_setting_id')->toArray();
        $receivingSetting->createHistoryLog('updated', 'receivingSettingsUpdated', createStringFromIterable($log));

        return redirect(route('administration.warehouseReceiving.edit', $receivingSetting->id))->with('status-success', __('receiving.receivingSettingWasUpdated'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param ReceivingSetting $receivingSetting
     * @return RedirectResponse
     * @throws AuthorizationException
     */
    public function destroy(ReceivingSetting $receivingSetting): RedirectResponse
    {
        $this->authorize('deleteWarehouseReceivingSettings', User::class);
        $receivingSetting->delete();
        $receivingSetting->createHistoryLog('deleted', 'receivingSettingsWasDeleted', createStringFromIterable($receivingSetting->load('seasons', 'suppliers')->toArray()));

        return redirect(route('administration.warehouseReceiving.index'))->with('status-success', __('receiving.receivingSettingWasDeleted'));
    }
}
