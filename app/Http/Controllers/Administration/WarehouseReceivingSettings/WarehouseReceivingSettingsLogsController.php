<?php

namespace App\Http\Controllers\Administration\WarehouseReceivingSettings;

use App\Http\Controllers\Controller;
use App\Models\ReceivingSetting;
use Illuminate\Http\Request;

class WarehouseReceivingSettingsLogsController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:warehouse administration']);
    }

    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request,ReceivingSetting $receivingSetting)
    {
        return view('admin.warehouseReceivingSettings.logs', [
            'receivingSetting' => $receivingSetting,
            'logs' => $receivingSetting->generalHistoryLogs()->with('actionUser')->paginate('50'),
        ]);
    }
}
