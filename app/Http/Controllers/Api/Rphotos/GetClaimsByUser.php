<?php

namespace App\Http\Controllers\Api\Rphotos;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\Status;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class GetClaimsByUser extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @return LengthAwarePaginator
     */
    public function __invoke(Request $request): object
    {
        $claims = Claim::
        with('claimArticle')
            ->where([
                ['claim_creator_id', auth()->id()],
                ['claim_status_id', '!=', Status::CLAIM_STATUS_CANCELED],
            ])
            ->whereIn('claim_status_id', [1, 2, 5])
            ->has('claimArticle')
            ->with('claimArticle.article:id,name,goodsid', 'status:id,name', 'customer:id,first_name,last_name',)
            ->latest()
            ->paginate(20);

        //TODO: Opravit az pride auth
        $user = User::find(auth()->id());

        if ($user->hasPermissionTo('upload photos from warehouse')) {
            $forbiddenStatusesForUpload = [
                Status::CLAIM_STATUS_CANCELED,
            ];
        } else {
            $forbiddenStatusesForUpload = [
                Status::CLAIM_STATUS_CANCELED,
                Status::CLAIM_STATUS_CLOSED,
                Status::CLAIM_STATUS_WAREHOUSE_EXPENSE_CREATED,
                Status::CLAIM_SEND_TO_WAREHOUSE,
                Status::CLAIM_STATUS_WAREHOUSE_RECEIVED

            ];
        }

        if ($claims->items()) {
            foreach ($claims->items() as $claim) {
                if (!$claim->claimArticle) {
                    $claim['error'] = 'articleNotFilled';
                } elseif (in_array($claim->claim_status_id, $forbiddenStatusesForUpload)) {
                    $claim['error'] = 'statusNotAllowed';
                };
            }
        }
        return $claims;
    }
}
