<?php

namespace App\Http\Controllers\Api\Rphotos;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\StorePhotoRequest;
use App\Models\Claim;
use App\Models\Photo;
use App\Models\Status;
use App\Models\User;
use App\Services\FileService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Pion\Laravel\ChunkUpload\Exceptions\UploadMissingFileException;
use Pion\Laravel\ChunkUpload\Receiver\FileReceiver;

class StoreClaimPhotos extends Controller
{
    /**
     *
     * @param StorePhotoRequest $request
     * @param FileReceiver $receiver
     * @param FileService $fileService
     * @return Photo|Model|JsonResponse|void
     * @throws UploadMissingFileException
     */
    public function __invoke(Request $request, FileReceiver $receiver, FileService $fileService)
    {
        //TODO: dokoncit autorizaciu, zatial nemame sposob ako zistovat centrum kam patri zamestnanec

        if ($receiver->isUploaded() === false) {
            throw new UploadMissingFileException();
        }
        $save = $receiver->receive();

        if ($save->isFinished()) {
            //TODO: Opravit az pride auth
            $user = User::find(auth()->id());

            if ($user->hasPermissionTo('upload photos from warehouse')) {
                $forbiddenStatusesForUpload = [
                    Status::CLAIM_STATUS_CANCELED,
                ];
            } else {
                $forbiddenStatusesForUpload = [
                    Status::CLAIM_STATUS_CANCELED,
                    Status::CLAIM_STATUS_CLOSED,
                    Status::CLAIM_STATUS_WAREHOUSE_EXPENSE_CREATED,
                    Status::CLAIM_SEND_TO_WAREHOUSE,
                    Status::CLAIM_STATUS_WAREHOUSE_RECEIVED
                ];
            }

            $claim = Claim::where('claim_code', $request->claim_code)
                ->whereNotIn('claim_status_id', $forbiddenStatusesForUpload)
                ->select('id', 'claim_code')
                ->firstOrFail();


            $file = $save->getFile();

            //Size validation
            if ($fileService->validateFileSize($file)) {
                unlink($save->getFile()->getPathname());

                return $this->baseErrorJsonResponse(__('photos.fileSizeExceeded', ['size' => config('media.mb_size_limit_for_video')]));
            }

            //file validation
            if ($fileService->validateFileType($file->getClientOriginalExtension())) {
                unlink($save->getFile()->getPathname());
                Log::info('Unsuported file type uploaded: ' . $file->getClientOriginalExtension());
                return $this->baseErrorJsonResponse(__('photos.invalidFileType', ['ext' => $file->getClientOriginalExtension()]));
            }

            $file = $fileService->storeUploadedFile($file, "/$claim->claim_code/article");


            $claim->createHistoryLog(
                'newPhoto',
                'fileUploaded',
                $file,
            );


            return $claim->photos()->create([
                'name' => $file->name,
                'file_path' => $file->file_path,
                'thumbnail_path' => $file->thumbnail_path ?? null,
                'preview_path' => $file->preview_path ?? null,
                'extension' => $file?->extension,
                'size' => $file->size,
            ]);
        }
    }
}
