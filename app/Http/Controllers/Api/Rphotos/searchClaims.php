<?php

namespace App\Http\Controllers\Api\Rphotos;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Rphotos\SearchClaimsRequest;
use App\Models\Claim;
use App\Models\Status;
use App\Models\User;

class searchClaims extends Controller
{
    /**
     * get claim id by claim code.
     *
     * @param SearchClaimsRequest $request
     * @return object
     */
    public function __invoke(SearchClaimsRequest $request): object
    {
        //TODO: dokoncit autorizaciu, zatial nemame sposob ako zistovat centrum kam patri zamestnanec
        $claims = Claim::where([
            ['claim_code', 'like', "%$request->claim_code%"],
            ['claim_status_id', '!=', Status::CLAIM_STATUS_CANCELED],
        ])
            ->with('claimArticle.article:id,name', 'customer:id,first_name,last_name', 'status:id,name')
            ->latest()
            ->paginate(20)
            ->withQueryString();


        //TODO: Opravit az pride auth
        $user = User::find(auth()->id());

        if ($user->hasPermissionTo('upload photos from warehouse')) {
            $forbiddenStatusesForUpload = [
                Status::CLAIM_STATUS_CANCELED,
            ];
        } else {
            $forbiddenStatusesForUpload = [
                Status::CLAIM_STATUS_CANCELED,
                Status::CLAIM_STATUS_CLOSED,
                Status::CLAIM_STATUS_WAREHOUSE_EXPENSE_CREATED,
                Status::CLAIM_SEND_TO_WAREHOUSE,
                Status::CLAIM_STATUS_WAREHOUSE_RECEIVED

            ];
        }

        if ($claims->items()) {
            foreach ($claims->items() as $claim) {
                if (!$claim->claimArticle) {
                    $claim['error'] = 'articleNotFilled';
                } elseif (in_array($claim->claim_status_id, $forbiddenStatusesForUpload)) {
                    $claim['error'] = 'statusNotAllowed';
                };
            }
        }
        return $claims;
    }
}
