<?php

namespace App\Http\Controllers\Api\TransportHub;

use App\Enums\ApiHistoryLogsActionsEnum;
use App\Enums\ClaimsTransports\CarrierStatusesEnum;
use App\Enums\ClaimsTransports\ClaimsTransportsStatusesEnum;
use App\Enums\Logs\LogActionTypes;
use App\Http\Controllers\Controller;
use App\Models\ApiRequestsLog;
use App\Models\ClaimsTransport;
use App\Models\ClaimsTransportsPackage;
use App\Models\TransportHubApiLog;
use App\Services\Logs\LogsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ReceiptIntoStock extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $apiUser = auth('basic_auth')->user();

        $apiLog = new TransportHubApiLog([
            'creator_id' => $apiUser->id,
            'creator_type' => $apiUser->getMorphClass(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'request' => json_encode($request->all()),
        ]);

        $validator = Validator::make($request->all(), [
            'trackingNo' => 'required|exists:claims_transports_packages,tracking_no',
            'transportId' => 'required|exists:claims_transports,transport_id',
        ]);

        $validator->after(function ($validator) use (&$claimsTransportPackage, &$claimsTransport, $request) {

            $claimsTransport = ClaimsTransport::where('transport_id', $request->transportId)->first();

            if ($claimsTransport && $claimsTransport->status !== ClaimsTransportsStatusesEnum::ORDERED->value) {
                $validator->errors()->add('transportId', 'Selected transport cannot be modified');
            }

            $claimsTransportPackage = ClaimsTransportsPackage::where('claims_transport_id', $claimsTransport->id)->where('tracking_no', $request->trackingNo)->first();

            if ($claimsTransportPackage && $claimsTransportPackage->status !== CarrierStatusesEnum::ORDERED->value) {
                $validator->errors()->add('trackingNo', 'Selected package cannot be modified');
            }

        });


        if ($validator->fails()) {

            $apiLog->fill([
                'claims_transport_id' => $claimsTransport?->id ?? null,
                'response' => $validator->errors()->toJson(),
            ])->save();

            return $this->baseErrorJsonResponse('Validation failed', 422, $validator->errors()->toArray());
        }

        try {

            //Update package
            $logsService = new LogsService();
            $changes = [];


            $claimsTransportPackage->fill([
                'status' => CarrierStatusesEnum::RECEIVED_AT_WAREHOUSE->value,
            ]);
            $changes['items'] = $logsService->getModelChanges($claimsTransportPackage);

            $claimsTransportPackage->save();


            //Update transport if all packaged arrived to warehosue
            $claimsTransport->load('packages');


            if ($claimsTransport->packages->firstWhere('status', '<', CarrierStatusesEnum::RECEIVED_AT_WAREHOUSE->value) === null) {

                $claimsTransport->fill([
                    'status' => ClaimsTransportsStatusesEnum::RECEIVED_AT_WAREHOUSE->value,
                ]);

                $changes = array_merge($changes, $logsService->getModelChanges($claimsTransport));

                //TOTO SI NIESOM ISTY
//                $claimsLogs = [];
//                $claimsIds = [];
//
//                foreach ($claimsTransport->claims as $claim) {
//                    $claimsIds[] = $claim->id;
//
//                    $timestamp = now();
//                    $claimsLogs[] = [
//                        'claim_id' => $claim->id,
//                        'claim_responsible_person_id' => $claim->claim_responsible_person_id,
//                        'action_user_id' => auth()->id(),
//                        'status_id' => $claim->claim_status_id,
//                        'action_type' => 'updated',
//                        'action' => 'receivedAtWarehouse',
//                        'note' => "Transport: {$claimsTransport->transport_id}",
//                        'created_at' => $timestamp,
//                        'updated_at' => $timestamp,
//                    ];
//                }
//
//                Claim::whereIn('id', $claimsIds)->update(['claim_status_id' => Status::CLAIM_STATUS_WAREHOUSE_RECEIVED]);
//                ClaimHistoryLog::insert($claimsLogs);

            }

            $claimsTransport->save();

            $claimsTransport->logs()->create([
                'action' => LogActionTypes::UPDATED,
                'creator_id' => $apiUser->id,
                'creator_type' => $apiUser->getMorphClass(),
                'changes' => json_encode($changes),
            ]);


            $response = $this->baseResponseStructure();

            $apiLog->fill([
                'claims_transport_id' => $claimsTransport?->id ?? null,
                'response' => json_encode($response),
            ])->save();

            return response()->json($response);


        } catch (\Throwable $exception) {

            $response = config('app.env' === 'production')
                ? $this->baseErrorResponseStructure(message: 'Request could not be processed.')
                : $exception->getMessage();

            $apiLog->fill([
                'claims_transport_id' => $claimsTransport?->id ?? null,
                'response' => json_encode($response),
                'internal_exceptions' => json_encode($exception->getMessage()),
            ])->save();

            return response()->json($response, 500);
        }

    }
}
