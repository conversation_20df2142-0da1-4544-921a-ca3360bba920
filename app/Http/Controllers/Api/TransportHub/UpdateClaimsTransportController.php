<?php

namespace App\Http\Controllers\Api\TransportHub;

use App\Enums\ApiHistoryLogsActionsEnum;
use App\Enums\ClaimsTransports\CarrierTypesEnum;
use App\Enums\ClaimsTransports\ClaimsTransportsLogActionsEnum;
use App\Enums\ClaimsTransports\ClaimsTransportsStatusesEnum;
use App\Enums\Logs\LogActionTypes;
use App\Http\Controllers\Controller;
use App\Models\ApiRequestsLog;
use App\Models\ClaimsTransport;
use App\Models\TransportHubApiLog;
use App\Services\ClaimsTransports\ClaimsTransportsService;
use App\Services\Logs\LogsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class UpdateClaimsTransportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $apiUser = auth('basic_auth')->user();

        $apiLog = new TransportHubApiLog([
            'creator_id' => $apiUser->id,
            'creator_type' => $apiUser->getMorphClass(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'request' => json_encode($request->all()),
        ]);


        $validator = Validator::make($request->all(), [
            'claimsPortalTransportId' => 'required|exists:claims_transports,transport_id',
            'shippingLabelUrl' => 'nullable|string',
            'items' => 'required|array',
            'items.*.trackingNo' => 'required|string',
            'items.*.trackingUrl' => 'required|string',
            'items.*.shippingLabelUrl' => 'required|string',
            'items.*.carrierTitle' => 'required|string',
        ]);

        $validator->after(function ($validator) use (&$claimsTransport, $request) {

            $claimsTransport = ClaimsTransport::where('transport_id', $request->claimsPortalTransportId)->first();

            if ($claimsTransport && !in_array($claimsTransport->status, [ClaimsTransportsStatusesEnum::WAITING_FOR_PROCESSING->value, ClaimsTransportsStatusesEnum::ORDERED->value])) {
                $validator->errors()->add(
                    'claimsTransport', 'The selected transport cannot be modified'
                );
            }
        });


        if ($validator->fails()) {

            $apiLog->fill([
                'claims_transport_id' => $claimsTransport?->id ?? null,
                'response' => $validator->errors()->toJson(),
            ])->save();

            return $this->baseErrorJsonResponse('Validation failed', 422, $validator->errors()->toArray());
        }

        try {
            $claimsTransport->fill([
                'status' => ClaimsTransportsStatusesEnum::ORDERED->value,
                'shipping_label' => $request->shippingLabelUrl ?? null,
                'order_confirmed_at' => now(),
            ]);

            $logsService = new LogsService();
            $changes = $logsService->getModelChanges($claimsTransport);

            $claimsTransportService = new ClaimsTransportsService();


            $claimsTransportService->syncPackages($claimsTransport, $request->items, $changes);

            $claimsTransport->logs()->create([
                'action' => ClaimsTransportsLogActionsEnum::TRANSPORT_HUB_CONFIRMED_ORDER->value,
                'creator_id' => $apiUser->id,
                'creator_type' => $apiUser->getMorphClass(),
                'changes' => json_encode($changes),
            ]);

            $claimsTransport->save();

            $response = $this->baseResponseStructure();

            $apiLog->fill([
                'claims_transport_id' => $claimsTransport?->id ?? null,
                'response' => json_encode($response),
            ])->save();

            return response()->json($response);


        } catch (\Throwable $exception) {

            $response = config('app.env' === 'production')
                ? $this->baseErrorResponseStructure(message: 'Request could not be processed.')
                : $exception->getMessage();

            $apiLog->fill([
                'claims_transport_id' => $claimsTransport?->id ?? null,
                'response' => json_encode($response),
                'internal_exceptions' => json_encode($exception->getMessage()),
            ])->save();

            return response()->json($response, 500);
        }

    }
}
