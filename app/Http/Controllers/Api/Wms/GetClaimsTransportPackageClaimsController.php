<?php

namespace App\Http\Controllers\Api\Wms;


use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\ClaimsTransportsPackage;
use App\Models\ClaimType;
use Carbon\Carbon;
use Illuminate\Http\Request;


class GetClaimsTransportPackageClaimsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $request->validate([
            'package_no' => [
                'required',
                function ($attribute, $value, $fail) {
                    if (!is_string($value) && !is_array($value)) {
                        $fail('package_no must be a string or an array');
                    }
                }
            ],
        ]);

        $b2cClaimTypes = [
            ClaimType::CLAIM_TYPE_CUSTOMER,
            ClaimType::CLAIM_TYPE_CUSTOMER_ESHOP,
        ];


        try {

            $packageNo = $request->package_no;
            if (!is_array($packageNo)) {
                $packageNo = [$packageNo];
            }

            $packages = ClaimsTransportsPackage::whereIn('tracking_no', $packageNo)
                ->with([
                    'claimsTransport:id,transport_id',
                    'claimsTransport.claims:id,claim_article_id,claim_code,claim_type_id',
                    'claimsTransport.claims.claimArticle:id,article_id,quantity',
                    'claimsTransport.claims.claimArticle.article:id,goodsid',
                    'claimsTransport.claims.supplierClaimArticlesWhereQuantityNotNull:goodsid,id',
                ])->get()
                ->map(function ($package) use ($b2cClaimTypes) {
                    $claims = $package->claimsTransport->claims->map(function ($claim) use ($b2cClaimTypes) {

                        if ($claim->is_supplier_claim) {
                            $articles = $claim->supplierClaimArticlesWhereQuantityNotNull->map(function ($article) {
                                return [
                                    'goodsid' => $article->goodsid,
                                    'quantity' => $article->pivot->quantity,
                                ];
                            });
                        } else {
                            $articles = [
                                'goodsid' => $claim->claimArticle->article->goodsid,
                                'quantity' => $claim->claimArticle->quantity,
                            ];
                        }

                        return [
                            'claim_code' => $claim->claim_code,
                            'type' => in_array($claim->claim_type_id, $b2cClaimTypes) ? 'b2c' : "b2b",
                            'articles' => $articles
                        ];
                    });


                    return [
                        'package' => $package->tracking_no,
                        'transport_id' => $package->claimsTransport->transport_id,
                        'claims' => $claims
                    ];
                });


            return $this->baseJsonResponse($packages);

        } catch (\Throwable $throwable) {

            $errorMessage = $throwable->getMessage();
            \Log::error("New error from WMS api. Message: $errorMessage");
            return $this->baseErrorJsonResponse(__('An unexpected error has occurred.'));
        }

    }
}
