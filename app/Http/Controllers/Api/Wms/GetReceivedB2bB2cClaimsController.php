<?php

namespace App\Http\Controllers\Api\Wms;


use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\ClaimType;
use Carbon\Carbon;
use Illuminate\Http\Request;
use PHPUnit\Framework\Exception;


class GetReceivedB2bB2cClaimsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $request->validate([
            'received_from' => 'required|date|date_format:Y-m-d H:i|before_or_equal:received_to',
            'received_to' => 'nullable|date|date_format:Y-m-d H:i|after_or_equal:received_from',
        ]);


        try {
            $from = Carbon::parse($request->received_from);
            $fromDate = $from->toDateString();
            $fromTime = $from->format('H:i');

            $to = $request->received_to;


            $b2bClaimTypes = [
                ClaimType::CLAIM_TYPE_INTERNAL,
                ClaimType::CLAIM_TYPE_SUPPLIER,
                ClaimType::CLAIM_TYPE_SUPPLIER_WAREHOUSE,
                ClaimType::CLAIM_TYPE_WAREHOUSE,
            ];

            $b2cClaimTypes = [
                ClaimType::CLAIM_TYPE_CUSTOMER,
            ];


            $claims = Claim::select(['id', 'claim_code', 'claim_article_id', 'claim_type_id'])
                ->with(['claimArticle:id,quantity', 'supplierClaimArticles:id'])
                ->where('claim_type_id', '!=', ClaimType::CLAIM_TYPE_CUSTOMER_ESHOP)
                ->whereDate('warehouse_received_at', '>=', $fromDate)
                ->whereTime('warehouse_received_at', '>=', $fromTime)
                ->when(isset($to), function ($query) use ($to) {

                    $to = Carbon::parse($to);
                    $toDate = $to->toDateString();
                    $toTime = $to->format('H:i');

                    $query->whereDate('warehouse_received_at', '<=', $toDate)
                        ->whereTime('warehouse_received_at', '<=', $toTime);
                })
                ->get();


            $response = [
                'b2b' => 0,
                'b2c' => 0,
            ];

            foreach ($claims as $claim) {

                if (in_array($claim->claim_type_id, $b2bClaimTypes) || preg_match('/^cl-(gcecz|wms-vratka-p)-/i', $claim->claim_code)) {

                    if ($claim->is_supplier_claim) {
                        $response['b2b'] += $claim->supplierClaimArticles->sum('pivot.quantity');
                    } else {
                        $response['b2b'] += $claim->claimArticle->quantity;
                    }

                }

                if (in_array($claim->claim_type_id, $b2cClaimTypes) || preg_match('/^cl-vsersk-/i', $claim->claim_code)) {
                    $response['b2c'] += $claim->claimArticle->quantity;
                }
            };

            return $this->baseJsonResponse($response);

        } catch (\Throwable $throwable) {
            $errorMessage = $throwable->getMessage();
            \Log::error("New error from WMS api. Message: $errorMessage");
            return $this->baseErrorJsonResponse(__('An unexpected error has occurred.'));
        }

    }
}
