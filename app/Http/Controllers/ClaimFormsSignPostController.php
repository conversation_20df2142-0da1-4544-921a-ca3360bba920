<?php

namespace App\Http\Controllers;

use App\Models\Claim;
use App\Services\Claims\ClaimsNavigationService;
use Illuminate\Http\Request;

class ClaimFormsSignPostController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Claim $claim)
    {
        $this->authorize('isAccessible', $claim);
        $claim->load(
            'claimArticle:id,article_id,damage_description,damage_type_id',
            'preferredSolutions',
            'dispatchNoteWarehouseExpense:id'
        );
        $navigation = new ClaimsNavigationService($claim);
        return redirect($navigation->showClaim());
    }
}
