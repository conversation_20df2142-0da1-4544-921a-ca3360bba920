<?php

namespace App\Http\Controllers\Claims;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;

class ChangeClaimStatusController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function __invoke(Request $request, Claim $claim): RedirectResponse
    {
        if (!Gate::allows('claimsAdministration')) {
            abort(403);
        }

        return redirect(route('claims.administration', $claim->id));
    }
}
