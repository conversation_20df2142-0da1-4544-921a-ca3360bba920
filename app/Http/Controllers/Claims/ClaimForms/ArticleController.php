<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\ArticleRequest;
use App\Models\Claim;
use App\Models\ClaimArticle;
use App\Models\ClaimType;
use App\Services\CentresService;
use App\Services\Claims\ArticlesService;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\VermontApiService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;


class ArticleController extends Controller
{
    /**
     * Show the form for creating a new resource.
     * @param CentresService $centresService
     * @param Claim $claim
     * @return View|RedirectResponse
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function create(CentresService $centresService, Claim $claim): View|RedirectResponse
    {
        $this->authorize('viewClaimForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $claim->load('claimArticle:id,price,quantity');

        if ($claim->is_customer_claim) {
            $currency = $claim->purchaseDocument
                ? getCurrency('id', $claim->purchaseDocument->currency_id)
                : $centresService->getCentreCurrency($claim->claim_centre_id);
        }


        return view('claimForm/createArticle', [
            'navigation' => $navigation,
            'claim' => $claim,
            'currency' => $currency ?? null,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\ClaimForms\ArticleRequest $request
     * @param VermontApiService $vermontApiService
     * @param ArticlesService $articlesService
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function store(
        ArticleRequest $request,
        VermontApiService $vermontApiService,
        ArticlesService $articlesService,
        Claim $claim
    ) {
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('claimFormsEditing', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->articleRoute());
        }

        $apiArticle = $vermontApiService->getFormattedArticle('Goods_ID', $request->goodsid);
        if (!$apiArticle) {
            return redirect()->back()->with('status-danger', __('general.goodsIdNotFound'));
        }

        $currency = $request->currency ? getCurrency('code', $request->currency) : null;
        $articlesService->createOrUpdateClaimArticle(
            $claim,
            $apiArticle,
            (object)$request->validated(),
            $currency
        );

        $claim->refresh();
        $navigation->updateState($claim);
        return redirect($navigation->nextRoute());
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param CentresService $centresService
     * @param Claim $claim
     * @param ClaimArticle $claimArticle
     * @return View
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function edit(CentresService $centresService, Claim $claim, ClaimArticle $claimArticle): View
    {
        $this->authorize('viewClaimForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $article = $claimArticle->article;
        $articleOrderGroup = getOrderGroup('id', $article->order_group_id);
        $article->price = $claimArticle?->price ?? null;
        $article->quantity = $claimArticle?->quantity ?? null;

        if ($claim->is_customer_claim) {
            $currency = $claim->purchaseDocument
                ? getCurrency('id', $claim->purchaseDocument->currency_id)
                : $centresService->getCentreCurrency($claim->claim_centre_id);
        }


        return view('claimForm/editArticle', [
            'navigation' => $navigation,
            'claim' => $claim,
            'article' => $article,
            'claimArticle' => $claimArticle,
            'articleOrderGroup' => $articleOrderGroup,
            'currency' => $currency ?? null
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \App\Http\Requests\ClaimForms\ArticleRequest $request
     * @param VermontApiService $vermontApiService
     * @param ArticlesService $articlesService
     * @param Claim $claim
     * @param ClaimArticle $claimArticle
     * @return RedirectResponse
     */
    public function update(
        ArticleRequest $request,
        VermontApiService $vermontApiService,
        ArticlesService $articlesService,
        Claim $claim,
    ) {
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('claimFormsEditing', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->articleRoute());
        }

        $apiArticle = $vermontApiService->getFormattedArticle('Goods_ID', $request->goodsid);
        if (!$apiArticle) {
            return redirect()->back()->with('status-danger', __('general.goodsIdNotFound'));
        }

        $currency = $request->currency ? getCurrency('code', $request->currency) : null;
        $articlesService->createOrUpdateClaimArticle(
            $claim,
            $apiArticle,
            (object)$request->validated(),
            $currency
        );

        return redirect($navigation->nextRoute());
    }
}
