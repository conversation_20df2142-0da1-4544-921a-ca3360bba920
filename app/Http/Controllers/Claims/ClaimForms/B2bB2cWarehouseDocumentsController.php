<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\B2bB2cWarehouseDocumentRequest;
use App\Models\B2bB2cWarehouseDocument;
use App\Models\Claim;
use App\Services\Claims\ArticlesService;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\EsoApi\ClaimsWarehouseDocumentsService;
use App\Services\VermontApiService;

class B2bB2cWarehouseDocumentsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Claim $claim)
    {
        $this->authorize('viewClaimForm', $claim);

        $navigation = new ClaimsNavigationService($claim);
        return view('claimForm.createB2bB2cWarehouseDocument', [
            'submitUrl' => route('claims.b2bB2cWarehouseDocuments.store', $claim->id),
            'method' => 'POST',
            'navigation' => $navigation,
            'claim' => $claim,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(
        B2bB2cWarehouseDocumentRequest $request,
        VermontApiService              $vermontApiService,
        ArticlesService                $articlesService,
        Claim                          $claim
    )
    {
        $this->authorize('claimFormsEditing', $claim);

        $claim->b2bB2cWarehouseDocument()->create([
            'claim_number' => $request->ck_reklamacie,
            'warehouse_expense_number' => $request->cislo_vydajky,
            'eshop_invoice_number' => $request->cislo_objednavky,
            'from_warehouse' => $request->pociatocny_sklad,
            'to_warehouse' => $request->cis_sklad,
            'ean' => $request->ean,
            'goods_id' => $request->goods_id,
            'eso_goods_id' => $request->id_zbozi,
            'is_already_received' => $request->je_prijato_na_cis_sklad,
            'document_status' => $request->stav_vratky ?? null
        ]);


        $claim->fill(['claim_code' => $request->ck_reklamacie])->save();
        $apiArticle = $vermontApiService->getFormattedArticle('Goods_ID', $request->goods_id);
        $articlesService->createOrUpdateClaimArticle($claim, $apiArticle);
        $claim->refresh();
        $navigation = new ClaimsNavigationService($claim);

        return redirect($navigation->nextRoute());
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Claim $claim, B2bB2cWarehouseDocument $b2bB2cWarehouseDocument)
    {
        $this->authorize('viewClaimForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        return view('claimForm.createB2bB2cWarehouseDocument', [
            'submitUrl' => route(
                'claims.b2bB2cWarehouseDocuments.update',
                ['claim' => $claim->id, 'b2bB2cWarehouseDocument' => $b2bB2cWarehouseDocument->id]
            ),
            'method' => 'PUT',
            'navigation' => $navigation,
            'claim' => $claim,
            'b2bB2cWarehouseDocument' => $b2bB2cWarehouseDocument
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(
        B2bB2cWarehouseDocumentRequest $request,
        VermontApiService              $vermontApiService,
        ArticlesService                $articlesService,
        Claim                          $claim,
        B2bB2cWarehouseDocument        $b2bB2cWarehouseDocument
    )
    {
        $this->authorize('claimFormsEditing', $claim);

        $b2bB2cWarehouseDocument->update([
            'claim_number' => $request->ck_reklamacie,
            'warehouse_expense_number' => $request->cislo_vydajky,
            'eshop_invoice_number' => $request->cislo_objednavky,
            'from_warehouse' => $request->pociatocny_sklad,
            'to_warehouse' => $request->cis_sklad,
            'ean' => $request->ean,
            'goods_id' => $request->goods_id,
            'eso_goods_id' => $request->id_zbozi,
            'is_already_received' => $request->je_prijato_na_cis_sklad,
            'document_status' => $request->stav_vratky ?? null
        ]);


        $claim->fill(['claim_code' => $request->ck_reklamacie])->save();
        $apiArticle = $vermontApiService->getFormattedArticle('Goods_ID', $request->goods_id);
        $articlesService->createOrUpdateClaimArticle($claim, $apiArticle);
        $claim->refresh();
        $navigation = new ClaimsNavigationService($claim);

        return redirect($navigation->nextRoute());
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
