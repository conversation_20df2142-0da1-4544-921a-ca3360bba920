<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\WarehouseClaimRestockRequest;
use App\Jobs\CheckDamageTypeValidityJob;
use App\Jobs\Eso\ClaimRestockProcessJob;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\Status;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\ClaimsService;
use App\Services\EsoApi\ClaimsWarehouseDocumentsService;
use App\Services\EsoApi\EsoService;
use Illuminate\Support\Facades\Bus;
use Throwable;

class B2bB2cWarehouseReceiving extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(WarehouseClaimRestockRequest $request, ClaimsService $claimsService, Claim $claim)
    {
        $claim->load(['b2bB2cWarehouseDocument', 'claimArticle.article']);
        $this->authorize('receiveB2bB2cReturn', [$claim, $claim->b2bB2cWarehouseDocument]);
        $claimsWarehouseDocumentsService = new ClaimsWarehouseDocumentsService($claim);
        $selectedWarehouse = Centre::find($request->warehouse_id);
        $claimsWarehouse = Centre::find(Centre::Warehouse_3R00);
        $batchId = $request->batch_id;
        $user = auth()->user();

        CheckDamageTypeValidityJob::dispatch($claim);

        $additionalData = $claimsWarehouseDocumentsService->is_wms_return($claim->claim_code)
            ? $claimsWarehouseDocumentsService->formatAdditionalDataStringForWmsReturns($claim->claim_code)
            : $claim->claim_code;

        try {
            if ($claim->b2bB2cWarehouseDocument->is_already_received == 0) {
                $claimsWarehouseDocumentsService->receiveB2bB2cWarehouseReturn(
                    documentNumber: $claim->b2bB2cWarehouseDocument->claim_number,
                    toCentre: $selectedWarehouse,
                    actionUserId: $user->id,
                );
            } elseif ($selectedWarehouse->id != Centre::Warehouse_3R00) {

                $articles = $claimsWarehouseDocumentsService->formatArticleForDispatch(
                    article: $claim->claimArticle->article->goodsid,
                    count: $claim->claimArticle->quantity
                );

                $warehouseExpense = $claimsWarehouseDocumentsService->createExpense(
                    articles: $articles,
                    fromCentre: $claimsWarehouse,
                    toCentre: $selectedWarehouse,
                    actionUser: $user,
                    additionalData: $additionalData
                );
                $claimsWarehouseDocumentsService->increaseHeaderState(expense: $warehouseExpense);
                $claimsWarehouseDocumentsService->createWarehouseReceipt(
                    expense: $warehouseExpense,
                    toCentre: $selectedWarehouse,
                    user: $user
                );
            }
        } catch (\Throwable $e) {
            $error = is_json_string($e->getMessage())
                ? createStringFromIterable(json_decode($e->getMessage()))
                : $e->getMessage();

            //\Log::error($claim->claim_code . ': ' . $error);


            if ($e->getCode() == 408) {
                Bus::chain([
                    new ClaimRestockProcessJob(
                        claim: $claim,
                        additionalDataForEso: $additionalData,
                        actionUser: $user,
                        destinationWarehouse: $selectedWarehouse,

                    ),
                    function () use ($claimsService, $claim, $selectedWarehouse, $batchId, $user) {

                        if (isset($claim->b2bB2cWarehouseDocument->eshop_invoice_number)) {
                            $claimsService->receiveEshopClaimInWarehouse($claim, $claim->claimArticle->article->goodsid, $claim->b2bB2cWarehouseDocument->eshop_invoice_number);
                        }

                        $claimsService->receivedInWarehouse(
                            claim: $claim,
                            selectedWarehouse: $selectedWarehouse,
                            actionUserId: $user->id,
                            batchId: $batchId,
                        );
                    }
                ])->dispatch();
                return redirect()->back();
            } else {
                return redirect()->back()->withErrors(json_decode($e->getMessage()));
            }
        }


        if (isset($claim->b2bB2cWarehouseDocument->eshop_invoice_number)) {
            $claimsService->receiveEshopClaimInWarehouse($claim, $claim->claimArticle->article->goodsid, $claim->b2bB2cWarehouseDocument->eshop_invoice_number);
        }

        $claimsService->receivedInWarehouse(
            claim: $claim,
            selectedWarehouse: $selectedWarehouse,
            actionUserId: $user->id,
            batchId: $batchId
        );

        return redirect(route('warehouse.claims.index'))->with(
            'status-success',
            __("receiving.sentTo", ['warehouse' => $selectedWarehouse->code])
        );
    }

    /**
     * Display the specified resource.
     */
    public function show(ClaimsService $claimsService, Claim $claim)
    {
        $this->authorize('showWarehouseReceivingForm', $claim);
        $claim->load('b2bB2cWarehouseDocument');
        $navigation = new ClaimsNavigationService($claim);
        $availableWarehouses = $claimsService->getAvailableWarehouses($claim);
        $photos = $claim->photos;

        $photosRequired = $claim->warehouseClaimsPhotosRequired
            && $photos->count() == 0
            && $claim->claim_status_id == Status::CLAIM_STATUS_CREATED;


        return view('claimForm.b2bB2cWarehouseReceiving', [
            'navigation' => $navigation,
            'claim' => $claim,
            'availableWarehouses' => $availableWarehouses,
            'photos' => $photos,
            'photosRequired' => $photosRequired
        ]);
    }
}
