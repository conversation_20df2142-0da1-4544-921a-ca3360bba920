<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Enums\ClaimsTransports\ClaimsTransportsStatusesEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\ClosureRequest;
use App\Jobs\CheckDamageTypeValidityJob;
use App\Jobs\CheckIfArticleIsInListOfTrackedArticles;
use App\Jobs\Eso\GetPendingWarehouseDocumentJob;
use App\Mail\TypeOfDamageWasNoSpecifiedMail;
use App\Models\Claim;
use App\Models\ClaimsTransport;
use App\Models\ClaimType;
use App\Models\DamageType;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\ClaimsService;
use App\Services\ClaimsTransports\ClaimsTransportsService;
use App\Services\EsoApi\WarehouseDocumentsService;
use App\Services\NotificationsService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Bus;
use Illuminate\View\View;

class ClosureController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\ClaimForms\ClosureRequest $request
     * @param ClaimsService $claimsService
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function store(
        ClosureRequest $request,
        ClaimsService  $claimsService,
        Claim          $claim
    ): RedirectResponse
    {
        $this->authorize('sendClaimToWarehouse', $claim);
        $note = $request->note;
        $claim->load(
            'centre:id,code',
            'claimArticle:id,article_id,damage_type_id',
            'claimArticle.article:id,goodsid',
            'activeWarehouseExpense'
        );

        if ($claim->claim_type_id !== ClaimType::CLAIM_TYPE_SUPPLIER && !$claim->is_supplier_claim) {
            CheckIfArticleIsInListOfTrackedArticles::dispatch($claim);
        }

        if ($claim->claim_type_id !== ClaimType::CLAIM_TYPE_SUPPLIER && !$claim->is_supplier_claim && $claim->claimArticle->damage_type_id === DamageType::OTHER) {
            $notificationService = new NotificationsService();
            $notificationService->sendMails(TypeOfDamageWasNoSpecifiedMail::class, $claim);
            CheckDamageTypeValidityJob::dispatch($claim);
        }

        try {
            $warehouseDocumentsService = new WarehouseDocumentsService($claim);
            $warehouseDocumentsService->increaseHeaderState($claim->activeWarehouseExpense);
        } catch (\Throwable $e) {

            if ($e->getCode() == 408) {
                $claim->activeWarehouseExpense->refresh();
                $userId = auth()->id();
                Bus::chain([
                    new GetPendingWarehouseDocumentJob($claim, $claim->activeWarehouseExpense),
                    function () use ($claimsService, $claim, $note, $userId) {
                        $claimsService->sendClaimToWarehouse($claim, $userId, $note);
                    },
                ])->dispatch();
                return redirect()->back();
            } else {
                return redirect()->back()->withErrors(json_decode($e->getMessage()));
            }
        }

        $claimsService->sendClaimToWarehouse($claim, auth()->id(), $note);
        return redirect()->back()->with(
            'status-success',
            __('claims.messages.waitToBeSentToWarehouse')
        );
    }

    /**
     * Display the specified resource.
     *
     * @param Claim $claim
     * @return View
     * @throws AuthorizationException
     */
    public
    function show(
        Claim $claim
    ): View
    {
        $this->authorize('showClosure', $claim);
        $claim->load([
            'claimArticle:id,article_id,damage_description,damage_type_id',
            'status:id,name',
            'dispatchNoteWarehouseExpense',
            'photos',
            'closureNote.actionUser:id,name',
            'lastClosureLog.actionUser:id,name',
            'lastCreatedExpenseFromCentreLog.actionUser:id,name',
            'transports',
        ]);

        $claimsTransportService = new ClaimsTransportsService();
        $claimsTransferType = $claimsTransportService->convertClaimTypesToClaimsTransferType($claim);

        $openedTransports = ClaimsTransport::where('transport_type', $claimsTransferType)
            ->with('claims')
            ->where('centre_id', $claim->claim_centre_id)
            ->where('status', ClaimsTransportsStatusesEnum::CREATED)
            ->get();

        return view('claimForm.closure', [
            'claim' => $claim,
            'navigation' => new ClaimsNavigationService($claim),
            'note' => $claim->closureNote?->note,
            'closureLog' => $claim->lastClosureLog,
            'warehouseExpense' => $claim->dispatchNoteWarehouseExpense,
            'warehouseExpenseLog' => $claim->lastCreatedExpenseFromCentreLog,
            'transport' => $claim->transports->first(),
            'openedTransports' => $openedTransports,
        ]);
    }
}
