<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Enums\ClaimsTransports\ClaimsTransportsStatusesEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\ClosureRequest;
use App\Jobs\CheckIfArticleIsInListOfTrackedArticles;
use App\Jobs\Eso\GetPendingWarehouseDocumentJob;
use App\Models\Claim;
use App\Models\ClaimsTransport;
use App\Models\Solution;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\ClaimsService;
use App\Services\ClaimsTransports\ClaimsTransportsService;
use App\Services\EsoApi\WarehouseDocumentsService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Bus;
use Illuminate\View\View;

class ClosureCustomerController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param ClosureRequest $request
     * @param ClaimsService $claimsService
     * @param Claim $claim
     * @return RedirectResponse
     * @throws \Exception
     */
    public function store(
        ClosureRequest $request,
        ClaimsService  $claimsService,
        Claim          $claim
    ): RedirectResponse
    {
        if (
            auth()->user()->cannot('sendCustomerClaimToWarehouse', $claim)
            && auth()->user()->cannot('returnToCustomer', $claim)
        ) {
            return redirect()->back()->withErrors(__('general.unauthorized'));
        }

        $claim->load(
            'centre:id,code',
            'claimArticle:id,article_id,damage_description,damage_type_id',
            'claimArticle.article:id,goodsid',
            'activeWarehouseExpense',
            'purchaseDocument'
        );

        CheckIfArticleIsInListOfTrackedArticles::dispatch($claim);

        if (in_array($claim->solution_id, [Solution::SOLUTION_RETURN_ARTICLE_TO_CUSTOMER, Solution::SOLUTION_REPAIR])) {
            $claimsService->closeClaim($claim, $request->note);
            return redirect()->back()->with('status-success', __('claims.messages.claimWasClosed'));
        } else {
            $warehouseDocumentsService = new WarehouseDocumentsService($claim);

            try {
                $warehouseDocumentsService->increaseHeaderState($claim->activeWarehouseExpense);
            } catch (\Throwable $e) {

                if ($e->getCode() == 408) {
                    $claim->activeWarehouseExpense->refresh();
                    $note = $request->note;
                    $userId = auth()->id();
                    Bus::chain([
                        new GetPendingWarehouseDocumentJob($claim, $claim->activeWarehouseExpense),
                        function () use ($claimsService, $claim, $note, $userId) {
                            $claimsService->sendClaimToWarehouse($claim, $userId, $note);
                        },
                    ])->dispatch();
                    return redirect()->back();
                } else {
                    return redirect()->back()->withErrors($e->getMessage());
                }
            }

            $claimsService->sendClaimToWarehouse($claim, auth()->id(), $request->note);
            return redirect()->back()->with('status-success', __('claims.messages.waitToBeSentToWarehouse'));
        }
    }

    /**
     * Display the specified resource.
     *
     * @param Claim $claim
     * @return View
     */
    public
    function show(
        Claim $claim
    ): View
    {
        $this->authorize('showCustomerClaimClosure', $claim);
        $claim->load([
            'claimArticle:id,article_id,damage_type_id',
            'customer:id,bank_account',
            'status:id,name',
            'preferredSolutions:id,name',
            'solution:id,name',
            'closureNote',
            'lastNegativeReceiptLog',
            'lastClosureLog',
            'lastCreatedExpenseFromCentreLog',
            'transports',
        ]);

        $claimsTransportService = new ClaimsTransportsService();
        $claimsTransferType = $claimsTransportService->convertClaimTypesToClaimsTransferType($claim);

        $openedTransports = ClaimsTransport::where('transport_type', $claimsTransferType)
            ->with('claims')
            ->where('centre_id', $claim->claim_centre_id)
            ->where('status', ClaimsTransportsStatusesEnum::CREATED)
            ->get();

        $returnToCustomer = $claim->solution_id == Solution::SOLUTION_RETURN_ARTICLE_TO_CUSTOMER || $claim->solution_id == Solution::SOLUTION_REPAIR;

        return view('claimForm.closureCustomer', [
            'claim' => $claim,
            'returnToCustomer' => $returnToCustomer,
            'negativeReceiptNumberLog' => $claim->lastNegativeReceiptLog,
            'navigation' => new ClaimsNavigationService($claim),
            'note' => $claim->closureNote?->note,
            'closureLog' => $claim->lastClosureLog,
            'warehouseExpense' => $claim->dispatchNoteWarehouseExpense,
            'warehouseExpenseLog' => $claim->lastCreatedExpenseFromCentreLog,
            'transport' => $claim->transports->first(),
            'openedTransports' => $openedTransports,
        ]);
    }
}
