<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\ClosureRequest;
use App\Jobs\CheckDamageTypeValidityJob;
use App\Jobs\CheckIfArticleIsInListOfTrackedArticles;
use App\Models\Claim;
use App\Models\Status;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\ClaimsService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class ClosureEshopController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\ClaimForms\ClosureRequest $request
     * @param ClaimsService $claimsService
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function store(
        ClosureRequest $request,
        ClaimsService  $claimsService,
        Claim          $claim
    ): RedirectResponse
    {
        if (auth()->user()->cannot('sendEshopClaimToWarehouse', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect()->back()->withErrors(__('general.unauthorized'));
        }

        if (!$claimsService->articlePhotosExists($claim->id)) {
            return back()->withErrors(__('customValidation.rule.addArticlePhotosBeforePreview'));
        }
        $claim->load(
            'centre:id,code',
            'claimArticle:id,article_id,damage_type_id',
            'claimArticle.article:id,goodsid',
            'activeWarehouseExpense'
        );

        CheckIfArticleIsInListOfTrackedArticles::dispatch($claim);
        CheckDamageTypeValidityJob::dispatch($claim);

        $navigation = new ClaimsNavigationService($claim);

        if (isset($request->note)) {
            $claim->createHistoryLog('closure_note', 'closureNoteCreated', $request->note);
        }

        $claim->fill(['claim_status_id' => Status::CLAIM_SEND_TO_WAREHOUSE])->save();
        $claim->createHistoryLog('resolved', 'waitToBeSentToWarehouse');

        return redirect($navigation->eshopClaimClosureRoute())->with(
            'status-success',
            __('claims.messages.waitToBeSentToWarehouse')
        );
    }

    /**
     * Display the specified resource.
     *
     * @param Claim $claim
     * @return View
     * @throws AuthorizationException
     */
    public
    function show(
        Claim $claim
    ): View
    {
        $this->authorize('showClosure', $claim);
        $claim->load([
            'claimArticle:id,article_id,damage_type_id',
            'customer:id,bank_account',
            'status:id,name',
            'closureNote',
            'lastClosureLog',
            'transferOrderedLog',
            'transports'
        ]);


        return view('claimForm.closureEshop', [
            'claim' => $claim,
            'photosExists' => $claim->photos()->exists(),
            'navigation' => new ClaimsNavigationService($claim),
            'transport' => $claim->transports->first(),
        ]);
    }
}
