<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\CustomerRequest;
use App\Models\Claim;
use App\Models\Customer;
use App\Services\CentresService;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\CustomersService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;


class CustomerController extends Controller
{

    /**
     * Show the form for creating a new resource.
     * @param CentresService $centresService
     * @param Claim $claim
     * @return View
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function create(CentresService $centresService, Claim $claim): View
    {
        $this->authorize('viewClaimForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $centre = $centresService->getCentre($claim->claim_centre_id);

        return view('claimForm/createCustomer', [
            'navigation' => $navigation,
            'claim' => $claim,
            'centre' => $centre,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\ClaimForms\CustomerRequest $request
     * @param CustomersService $customersService
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function store(CustomerRequest $request, CustomersService $customersService, Claim $claim)
    {
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('claimFormsEditing', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->customerRoute());
        }

        $customersService->createOrUpdateCustomerForm($claim, $request->validated());
        $claim->refresh();
        $navigation->updateState($claim);
        return redirect($navigation->nextRoute());
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param CentresService $centresService
     * @param Claim $claim
     * @param Customer $customer
     * @return View
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function edit(CentresService $centresService, Claim $claim, Customer $customer): View
    {
        $this->authorize('viewClaimForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $centre = $centresService->getCentre($claim->claim_centre_id);

        return view('claimForm/editCustomer', [
            'navigation' => $navigation,
            'claim' => $claim,
            'customer' => $customer,
            'centre' => $centre,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \App\Http\Requests\ClaimForms\CustomerRequest $request
     * @param Claim $claim
     * @param CustomersService $customersService
     * @param Customer $customer
     * @return RedirectResponse
     */
    public function update(
        CustomerRequest $request,
        Claim $claim,
        CustomersService $customersService,
        Customer $customer
    ) {
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('claimFormsEditing', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->customerRoute());
        }

        $customersService->createOrUpdateCustomerForm($claim, $request->validated());
        return redirect($navigation->nextRoute());
    }

}
