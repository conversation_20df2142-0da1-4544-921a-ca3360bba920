<?php

namespace App\Http\Controllers\Claims\ClaimForms;


use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\ArticleDamageRequest;
use App\Models\Claim;
use App\Models\ClaimArticle;
use App\Models\ClaimType;
use App\Models\DamagePlace;
use App\Models\DamageType;
use App\Models\Diagram;
use App\Services\CentresService;
use App\Services\Claims\ArticlesService;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\PhotosService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\RedirectResponse;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class DamageController extends Controller
{

    /**
     * Show the form for creating a new resource.
     *
     * @param CentresService $centresService
     * @param Claim $claim
     * @return View
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function create(CentresService $centresService, Claim $claim): View
    {
        $this->authorize('viewClaimForm', $claim);
        $claim->load('claimArticle.article:id,order_group_id');
        $navigation = new ClaimsNavigationService($claim);
        $centre = $centresService->getCentre($claim->claim_centre_id);
        $diagram = Diagram::where('id', $claim->claimArticle->diagram_id)->first();

        $damageTypes = DamageType::byOrderGroup($claim->claimArticle->article->order_group_id)
            ->subTypes()
            ->withOther()
            ->onlyActive()
            ->withParent()
            ->formatCollectionForSelectInput();

        //$damagePlaces = DamagePlace::ByOrderGroup($claim->claimArticle->article->order_group_id)->get();

        return view('claimForm/createDamage', [
            'navigation' => $navigation,
            'diagram' => $diagram,
            'claim' => $claim,
            'centre' => $centre,
            'damageTypes' => $damageTypes,
            //'damagePlaces' => $damagePlaces,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param ArticleDamageRequest $request
     * @param ArticlesService $articlesService
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function store(
        ArticleDamageRequest $request,
        ArticlesService      $articlesService,
        Claim                $claim
    ): RedirectResponse
    {
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('claimFormsEditing', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->damageRoute());
        }

        $articlesService->storeDamage($claim, (object)$request->validated());
        return redirect($navigation->nextRoute());
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param CentresService $centresService
     * @param Claim $claim
     * @return View
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function edit(
        CentresService $centresService,
        Claim          $claim,
    ): View
    {
        $this->authorize('viewClaimForm', $claim);
        $claim->load('claimArticle.article:id,order_group_id');
        $navigation = new ClaimsNavigationService($claim);
        $centre = $centresService->getCentre($claim->claim_centre_id);
        $diagram = Diagram::where('id', $claim->claimArticle->diagram_id)->first();

        if (auth()->user()->cannot('updateClaimDamage', $claim)) {
            $claim->load( 'claimArticle.damageSubType.parent');
        }else{

            $damageTypes = DamageType::byOrderGroup($claim->claimArticle->article->order_group_id)
                ->subTypes()
                ->withOther()
                ->onlyActive()
                ->withParent()
                ->formatCollectionForSelectInput();
        }


        $savedDiagram = $claim->claim_type_id == ClaimType::CLAIM_TYPE_SUPPLIER
            ? "surveys/$claim->survey_id/diagram.png"
            : "$claim->claim_code/diagram.png";


        return view('claimForm/editDamage', [
            'navigation' => $navigation,
            'diagram' => $diagram ?? null,
            'claim' => $claim,
            'claimArticle' => $claim->claimArticle,
            'centre' => $centre,
            'savedDiagram' => $savedDiagram,
            'damageTypes' => $damageTypes ?? null,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param ArticleDamageRequest $request
     * @param ArticlesService $articlesService
     * @param Claim $claim
     * @param ClaimArticle $claimArticle
     * @return RedirectResponse
     */
    public function update(
        ArticleDamageRequest $request,
        ArticlesService      $articlesService,
        Claim                $claim,
        ClaimArticle         $claimArticle
    ): RedirectResponse
    {
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('updateClaimDamage', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->damageRoute());
        }

        $articlesService->updateDamage($claim, $claimArticle, (object)$request->validated());
        return redirect($navigation->nextRoute());
    }

}
