<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Enums\Decisions\ClaimsDecisionsEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\DecisionRequest;
use App\Models\Claim;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\ClaimsService;
use App\Services\Claims\DecisionsService;
use ErrorException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

class DecisionsController extends Controller
{

    private DecisionsService $decisionsService;

    public function __construct(DecisionsService $decisionsService,) {
        $this->decisionsService = $decisionsService;
    }

    /**
     * Show the form for creating a new resource.
     *
     * @param Claim $claim
     * @return View
     * @throws AuthorizationException
     */
    public function create(Claim $claim): View
    {
        $this->authorize('showDecision', $claim);

        return view('claimForm.createDecision', [
            'navigation' =>  new ClaimsNavigationService($claim),
            'claim' => $claim,
            'decisions' => ClaimsDecisionsEnum::getDecisionsByPermissions(auth()->user(),$claim->decision),
            'internalNotes' => $claim->getAllDecisionLogs(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param decisionRequest $request
     * @param ClaimsService $claimsService
     * @param Claim $claim
     * @return RedirectResponse
     * @throws ErrorException
     */
    public function store(DecisionRequest $request, ClaimsService $claimsService, Claim $claim): RedirectResponse
    {
        if (!auth()->user()->can('createOrUpdateDecision', $claim)) {
            return back()->with('status-danger', __('general.unauthorized'));
        } elseif (!$claimsService->articlePhotosExists($claim->id)) {
            return back()->with('status-danger', __('photos.pleaseUploadPhotosBeforeSend'));
        }

        $this->decisionsService->makeDecision($claim, (object)$request->validated());
        return redirect(route('claims.decisions.edit',$claim->id))->with('status-success',__('decision.decisionWasUpdated'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param Claim $claim
     * @return View
     * @throws AuthorizationException
     */
    public function edit(Claim $claim): View
    {
        $this->authorize('showDecision', $claim);
        $claim->load('guarantorStatement:id,claim_id,guarantor_statement');

        return view('claimForm.editDecision', [
            'navigation' => new ClaimsNavigationService($claim),
            'claim' => $claim,
            'decisions' => ClaimsDecisionsEnum::getDecisionsByPermissions(auth()->user(),$claim->decision),
            'allDecisions' => ClaimsDecisionsEnum::translatedValues(),
            'decisionNotes' => $claim->getAllDecisionLogs(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param DecisionRequest $request
     * @param ClaimsService $claimsService
     * @param Claim $claim
     * @return RedirectResponse
     * @throws ErrorException
     */
    public function update(DecisionRequest $request, ClaimsService $claimsService, Claim $claim): RedirectResponse
    {

        if (!auth()->user()->can('createOrUpdateDecision', $claim)) {
            return back()->with('status-danger', __('general.unauthorized'));
        } elseif (!$claimsService->articlePhotosExists($claim->id)) {
            return back()->with('status-danger', __('photos.pleaseUploadPhotosBeforeSend'));
        }

        $this->decisionsService->makeDecision($claim, (object)$request->validated());

        return redirect(route('claims.decisions.edit',$claim->id))->with('status-success',__('decision.decisionWasUpdated'));
    }
}
