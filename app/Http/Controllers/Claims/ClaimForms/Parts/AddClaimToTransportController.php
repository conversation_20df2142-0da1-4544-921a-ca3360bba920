<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\ClaimsTransport;
use App\Models\Status;
use App\Services\ClaimsTransports\ClaimsTransportsService;
use Illuminate\Http\Request;

class AddClaimToTransportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Claim $claim, ClaimsTransport $claimsTransport)
    {
//        $this->authorize('addClaimToTransport', $claim);
//
//        $claimsTransportService = new ClaimsTransportsService();
//
//        try {
//            $claimsTransportService->addClaimToTransport($claimsTransport, $claim);
//        } catch (\Exception $exception) {
//            return back()->withErrors($exception->getMessage());
//
//        }
//
//        $claim->update(['claim_status_id' => Status::CLAIM_STATUS_IN_TRANSPORT]);
//
//        return back()->with('status-success', __('closure.transportWasOrdered'));
    }
}
