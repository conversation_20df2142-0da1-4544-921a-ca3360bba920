<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\Status;
use App\Services\Claims\ClaimsService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class CancelClaimController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:edit claims|edit warehouse claims']);
    }

    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function __invoke(
        Request $request,
        Claim $claim
    ): RedirectResponse {
        $this->authorize('cancelClaim', $claim);
        $status = Status::where('name', 'cancelled')->first();
        $claim->update([
            'claim_status_id' => $status->id,
            'is_active' => 0
        ]);
        $claim->createHistoryLog('cancelled', 'claimWasCancelled',);


        if ($claim->claim_type_id == ClaimType::CLAIM_TYPE_WAREHOUSE) {
            return redirect(route('warehouse.claims.index'))->with(
                'status-success',
                __('claims.messages.claimWasCancelled')
            );
        } else {
            return redirect(route('claims.index'))->with(
                'status-success',
                __('claims.messages.claimWasCancelled')
            );
        }
    }
}
