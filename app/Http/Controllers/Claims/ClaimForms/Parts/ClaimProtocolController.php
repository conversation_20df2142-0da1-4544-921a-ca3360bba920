<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Services\CentresService;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\FileService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\App;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class ClaimProtocolController extends Controller
{
    /**
     * Generate customer claim protocol.
     *
     * @param FileService $fileService
     * @param CentresService $centresService
     * @param Claim $claim
     * @return View
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __invoke(
        FileService $fileService,
        CentresService $centresService,
        Claim $claim
    ): View {
        $this->authorize('showCustomerClaimProtocol', $claim);

        $claim->load(
            'purchaseDocument',
            'claimArticle.article',
            'customer',
            'solution',
            'claimArticle.currency',
            'type',
            'preferredSolutionNote.actionUser:id,name',
            'preferredSolutionNote.solution:id,name',
            'claimArticle.damageType',
            'claimArticle.damageSubType',
        );
        $centre = $centresService->getCentre($claim->claim_centre_id);
        $lang = $centresService->getCentreLanguage($claim->claim_centre_id);
        App::setLocale($lang);
        $navigation = new ClaimsNavigationService($claim);
        $summaryRoute = $navigation->SummaryRoute();
        if ($claim->claimArticle->diagram_id) {
            $damageDiagram = (string)$fileService->createImageFromCoordinates(
                $claim->claimArticle->diagram_id,
                $claim->claimArticle->diagram_coordinates,
                'data-url',
                'images/Damage_point_black.png'
            );
        }


        return view('claimForm.claimFormsParts.customerClaimProtocol', [
            'claim' => $claim,
            'centre' => $centre,
            'publicProtocol' => false,
            'summaryRoute' => $summaryRoute,
            'damageDiagram' => $damageDiagram ?? null,
            'preferredSolutionNote' => $claim->preferredSolutionNote ?? null,
        ]);
    }
}
