<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\SupplierClaimArticle;
use App\Services\Claims\ClaimsNavigationService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class ConvertClaimToSupplierClaim extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Claim $claim)
    {
        $this->authorize('convertToSupplierClaim', $claim);
        $request->validate(['goodsid' => 'required|exists:articles,goodsid']);

        $supplierClaimArticle = SupplierClaimArticle::whereHas(
            'selectedArticles',
            function (Builder $q) use ($request) {
                $q->where('goodsid', $request->goodsid);
            }
        )->first();

        $claim->fill([
            'claim_type_id' => ClaimType::CLAIM_TYPE_SUPPLIER,
            'claim_article_id' => null,
            'supplier_claim_article_id' => $supplierClaimArticle->id
        ])->save();

        if ($claim->claimArticle) {
            $claim->claimArticle->delete();
        }

        $articlesForAttach = $supplierClaimArticle->selectedArticles->pluck('id')->toArray();
        $claim->supplierClaimArticles()->sync($articlesForAttach);
        $navigation = new ClaimsNavigationService($claim);

        return redirect($navigation->supplierClaimArticlesRoute())->with(
            'status-success',
            __('claims.messages.claimWasChangedToSupplierClaim')
        );
    }
}
