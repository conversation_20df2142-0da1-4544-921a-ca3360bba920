<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\Status;
use Illuminate\Http\Request;

class DecisionFormReceiveClaimController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Claim $claim)
    {
        $this->authorize('decisionFormReceiveClaim',$claim);

        $request->validate(['status_id'=>'required|exists:statuses,id']);
        $claim->fill(['claim_status_id' => $request->status_id])->save();

        $action = match ((int)$request->status_id){
          Status::CLAIM_STATUS_GUARANTOR_RECEIVED =>'guarantorReceivedClaim',
          Status::CLAIM_STATUS_REJECTED =>'centreReceivedClaim',
        };

        $claim->createHistoryLog(
            actionType: 'decision_note',
            action: $action,
        );

        return back()->with('status-success', __('decision.claimWasReceived'));
    }
}
