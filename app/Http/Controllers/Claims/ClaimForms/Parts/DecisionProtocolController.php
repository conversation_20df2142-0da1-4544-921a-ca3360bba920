<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\Diagram;
use App\Models\GuarantorStatement;
use App\Services\CentresService;
use App\Services\Claims\ClaimsNavigationService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class DecisionProtocolController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param CentresService $centresService
     * @param Claim $claim
     * @return View
     * @throws AuthorizationException
     */
    public function __invoke(
        Request $request,
        CentresService $centresService,
        Claim $claim
    ): View {
        $this->authorize('showDecisionProtocol', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $claim->load('purchaseDocument', 'claimArticle.article', 'customer', 'solution', 'claimArticle.currency');
        $diagram = Diagram::where('id', $claim->claimArticle->diagram_id)->first();
        $centre = $centresService->getCentre($claim->claim_centre_id);
        $lang = $centresService->getCentreLanguage($claim->claim_centre_id);
        $guarantorStatement = GuarantorStatement::where('claim_id', $claim->id)->first();
        App::setLocale($lang);

        return view('claimForm/claimFormsParts/decisionProtocol', [
            'claim' => $claim,
            'centre' => $centre,
            'diagram' => $diagram,
            'backButtonUrl' => $navigation->closureRoute(),
            'guarantorStatement' => $guarantorStatement
        ]);
    }

}
