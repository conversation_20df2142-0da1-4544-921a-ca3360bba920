<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Mail\CustomersMails\ClaimWasClosed;
use App\Mail\CustomersMails\ClaimWasRegisteredMail;
use App\Models\Claim;
use App\Models\PublicProtocol;
use App\Services\CentresService;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\ClaimsService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class DecisionProtocolSignedController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param ClaimsService $claimsService
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function __invoke(Request $request, ClaimsService $claimsService, Claim $claim): RedirectResponse
    {
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('signDecisionProtocol', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->solutionRoute());
        }


        $claimsService->resolveClaim($claim);

        if ($request->send_confirmation_email_to_customer == 1) {
            if (isset($claim->customer->email)) {
                if (config('app.app_server') != 'production') {
                    $lang = (new CentresService())->getCentreLanguage($claim->claim_centre_id);

//                    Mail::to($claim->customer->email)->locale($lang)->send(
//                        new ClaimWasClosed($claim)
//                    );
                }
            } else {
                request()->session()->flash('status-warning', __('emails.customersEmails.emailWasNotFilledConfirmationMailWasNotSent'));
            }
        }


        return redirect($navigation->solutionRoute());
    }
}
