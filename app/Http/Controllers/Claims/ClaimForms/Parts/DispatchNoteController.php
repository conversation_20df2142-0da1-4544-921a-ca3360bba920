<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Services\CentresService;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\FileService;
use Illuminate\Contracts\View\View;
use Milon\Barcode\DNS1D;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class DispatchNoteController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param CentresService $centresService
     * @param FileService $fileService
     * @param Claim $claim
     * @param string $from
     * @return View
     */
    public function __invoke(CentresService $centresService, FileService $fileService, Claim $claim, string $from): View
    {
        $this->authorize('showDispatchNote', $claim);

        $claim->load([
            'claimArticle.article',
            'type',
            'dispatchNoteWarehouseExpense'
        ]);

        $barcodeGenerator = new DNS1D();
        $navigation = new ClaimsNavigationService($claim);
        $warehouseExpense = $claim->dispatchNoteWarehouseExpense?->eso_document_number;

        if ($from == 'warehouse') {
            $backUrl = route('warehouse.claims.show', $claim->id);
        } elseif ($from == 'claimForms') {
            $backUrl = $navigation->showClaim();
        }


        if ($claim->claimArticle->diagram_id) {
            $damageDiagram = (string)$fileService->createImageFromCoordinates(
                $claim->claimArticle->diagram_id,
                $claim->claimArticle->diagram_coordinates,
                'data-url',
                'images/Damage_point_black.png'
            );
        }

        if ($claim->claimArticle->article?->ean) {
            $barcode = $barcodeGenerator->getBarcodeSVG((string)$claim->claimArticle->article->ean, 'C128');
        }

        return view('claimForm.claimFormsParts.dispatchNote', [
            'claim' => $claim,
            'barcode' => $barcode ?? null,
            'navigation' => $navigation,
            'warehouseExpense' => $warehouseExpense,
            'damageDiagram' => $damageDiagram ?? null,
            'backUrl' => $backUrl ?? null,
        ]);
    }
}
