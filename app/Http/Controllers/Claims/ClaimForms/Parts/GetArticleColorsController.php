<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Services\Claims\ArticlesService;
use Illuminate\Http\Request;

class GetArticleColorsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, ArticlesService $articlesService, Claim $claim)
    {
        $this->authorize('claimFormsEditing', $claim);
        $request->validate([
            'article_code' => 'required|exists:articles,code',
            'article_season' => 'required|exists:articles,season'
        ]);

        return $articlesService->getArticleColors($request->article_code, $request->article_season);
    }
}
