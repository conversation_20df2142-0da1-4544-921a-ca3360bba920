<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\Claim;
use App\Services\Claims\ArticlesService;
use Illuminate\Http\Request;

class GetArticleSeasonsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, ArticlesService $articlesService, Claim $claim)
    {
        $this->authorize('claimFormsEditing', $claim);
        $request->validate(['article_code' => 'required|exists:articles,code']);
        return $articlesService->getArticleSeasons($request->article_code);
    }
}
