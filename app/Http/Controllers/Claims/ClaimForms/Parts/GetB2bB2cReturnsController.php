<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Services\EsoApi\ClaimsWarehouseDocumentsService;
use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Throwable;

class GetB2bB2cReturnsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Claim $claim)
    {
        $claimsWarehouseDocumentsService = new ClaimsWarehouseDocumentsService($claim);

        $validated = (object)$request->validate([
            'document_number' => [
                'required',
                'string',
                'regex:/^cl-(gcecz|wms|vsersk)-/i',
                function (string $attribute, mixed $value, Closure $fail) use ($claimsWarehouseDocumentsService) {
                    $claimLabel = $claimsWarehouseDocumentsService->createLabelForB2bB2cClaims($value);
                    $alreadyCreated = Claim::where('claim_code', $claimLabel)->exists();

                    if ($alreadyCreated) {
                        $fail(__('warehouse.documentNumberBelongsToExistingClaim'));
                    }
                },

            ],
        ], [
            'document_number.regex' => __('warehouse.labelHasIncorrectFormat')
        ]);

        $claimLabel = $claimsWarehouseDocumentsService->createLabelForB2bB2cClaims($validated->document_number);

        try {
            $warehouseDocument = $claimsWarehouseDocumentsService->getB2bB2cWarehouseReturn($claimLabel);
            return response(json_encode($warehouseDocument));
        } catch (Throwable $e) {
            return response($e->getMessage(), 422);
        }
    }
}
