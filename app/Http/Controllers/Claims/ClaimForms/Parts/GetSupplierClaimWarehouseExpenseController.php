<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Services\EsoApi\ClaimsWarehouseDocumentsService;
use App\Traits\Responses;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Throwable;

class GetSupplierClaimWarehouseExpenseController extends Controller
{
    use Responses;

    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param Claim $claim
     * @return object
     */
    public function __invoke(Request $request, Claim $claim)
    {
        $validated = (object)$request->validate([
            'cis_dok' => 'required',
        ]);

        $claimsWarehouseDocumentsService = new ClaimsWarehouseDocumentsService($claim);

        try {
            $warehouseExpense = $claimsWarehouseDocumentsService->getSupplierClaimWarehouseExpense($validated->cis_dok);
            return $this->response($warehouseExpense);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage(), 422);
        }
    }
}
