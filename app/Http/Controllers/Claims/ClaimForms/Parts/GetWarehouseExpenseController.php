<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Services\EsoApi\ClaimsWarehouseDocumentsService;
use App\Traits\Responses;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Throwable;

class GetWarehouseExpenseController extends Controller
{
    use Responses;

    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param Claim $claim
     * @return object
     */
    public function __invoke(Request $request, Claim $claim)
    {
        $validated = (object)$request->validate([
            'cis_dok' => 'required',
        ]);

        $claimsWarehouseDocumentsService = new ClaimsWarehouseDocumentsService($claim);

        try {
            $warehouseExpense = $claimsWarehouseDocumentsService->getClaimsWarehouseExpense($validated->cis_dok);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage(), 422);
        }

        $alreadyReceivedArticles = Claim::select(['id', 'claim_article_id'])
            ->with('claimArticle:id,article_id,quantity', 'claimArticle.article:id,goodsid')
            ->whereHas('warehouseDocuments', function (Builder $q) use ($validated) {
                $q->where('eso_document_number', $validated->cis_dok);
            })
            ->get()
            ->keyBy('claimArticle.article.goodsid');

        foreach ($warehouseExpense->artikle as &$article) {
            $alreadyReceivedArticle = $alreadyReceivedArticles[$article->goods_id] ?? null;

            if ($alreadyReceivedArticle) {
                $article->received_count = isset($article->received_count) ? $article->received_count + $alreadyReceivedArticle->claimArticle->quantity : $alreadyReceivedArticle->claimArticle->quantity;
                $article->already_received = isset($article->received_count) && $article->received_count >= $article->pocet;
            }
        }

        usort($warehouseExpense->artikle, function ($article) {
            return $article->already_received ?? false;
        });
        
        return $this->response($warehouseExpense);
    }
}
