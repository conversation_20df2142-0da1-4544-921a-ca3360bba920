<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\Status;
use App\Services\Claims\CustomersService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class NotifyCustomerController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param CustomersService $customersService
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function __invoke(Request $request, CustomersService $customersService, Claim $claim): RedirectResponse
    {
        $this->authorize('notifyCustomer', $claim);

        $validated = (object)$request->validate([
            'notify' => 'required',
            'note' => 'string|nullable',
        ]);

        $claim->fill(['claim_status_id' => Status::CLAIM_STATUS_CUSTOMER_CONTACTED])->save();

        $claim->createHistoryLog(
            'customer_notification_note',
            $validated->notify,
            $validated->note
        );

        return redirect()->back()->with('status-success', __('solution.customerWasNotified'));
    }
}
