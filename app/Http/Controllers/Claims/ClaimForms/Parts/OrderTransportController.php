<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Enums\ClaimsTransports\ClaimsTransportsLogActionsEnum;
use App\Enums\ClaimsTransports\ClaimsTransportsStatusesEnum;
use App\Enums\ClaimsTransports\ClaimsTransportTypesEnum;
use App\Enums\Logs\LogActionTypes;
use App\Http\Controllers\Controller;
use App\Jobs\CheckIfArticleIsInListOfTrackedArticles;
use App\Mail\TypeOfDamageWasNoSpecifiedMail;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\DamageType;
use App\Models\Status;
use App\Services\ClaimsTransports\ClaimsTransportsService;
use App\Services\ClaimsTransports\TransportHubService;
use App\Services\Logs\LogsService;
use App\Services\NotificationsService;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class OrderTransportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Claim $claim)
    {
        $this->authorize('orderTransport', $claim);

        $request->validate([
            'packages_count' => ['nullable', 'gte:1','lte:5', 'numeric', Rule::requiredIf($claim->is_supplier_claim)],
        ]);


        $claim->load('purchaseDocument');


        $claimsTransportService = new ClaimsTransportsService();
        $transportHubService = new TransportHubService();
        $packagesCount = $request->packages_count ?? 1;

        try {
            $transportType = $claimsTransportService->convertClaimTypesToClaimsTransferType($claim);
            $orderTransport = $transportType !== ClaimsTransportTypesEnum::INTERNAL_AND_CUSTOMER->value;

            $claimsTransport = $claimsTransportService->create($transportType, $claim->centre, auth()->user(), $orderTransport);
            $claimsTransportService->addClaimToTransport($claimsTransport, $claim);

            //Process for supplier and eshop claims
            $response = $transportHubService->orderTransport($claimsTransport, $packagesCount);

            if ($response->success === true) {

                if (!$claim->is_supplier_claim) {
                    CheckIfArticleIsInListOfTrackedArticles::dispatch($claim);
                }

                $claimsTransport->refresh();
                $claimsTransport->fill([
                    'status' => ClaimsTransportsStatusesEnum::WAITING_FOR_PROCESSING->value,
                ]);

                $logsService = new LogsService();
                $change = $logsService->getModelChanges($claimsTransport);

                $claimsTransport->logs()->create([
                    'action' => ClaimsTransportsLogActionsEnum::STATUS_UPDATED->value,
                    'creator_id' => auth()->id(),
                    'creator_type' => auth()->user()->getMorphClass(),
                    'changes' => json_encode($change),
                ]);

                $claimsTransport->save();

                $claim->update(['claim_status_id' => Status::CLAIM_STATUS_IN_TRANSPORT]);
                $claim->createHistoryLog('updated', 'transportOrdered', "Transport: $claimsTransport->transport_id");

                return back()->with('status-success', __('closure.transportWasOrdered'));
            } else {
                return back()->withErrors(__('claimsTransports.orderCreationFailed'));
            }

        } catch (\Exception $exception) {
            return back()->withErrors($exception->getMessage());
        }
    }
}
