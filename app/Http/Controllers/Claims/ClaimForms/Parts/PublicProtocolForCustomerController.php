<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\Diagram;
use App\Models\PublicProtocol;
use App\Services\CentresService;
use App\Services\FileService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\View\View;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class PublicProtocolForCustomerController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param FileService $fileService
     * @param CentresService $centresService
     * @return View
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __invoke(
        Request $request,
        FileService $fileService,
        CentresService $centresService,
    ): View {
        $publicProtocol = PublicProtocol::where('hash', $request->protocol)
            ->with(
                'claim',
                'claim.purchaseDocument',
                'claim.claimArticle.article',
                'claim.customer',
                'claim.solution',
                'claim.claimArticle.currency',
                'claim.type',
                'claim.preferredSolutionNote.actionUser:id,name',
                'claim.preferredSolutionNote.solution:id,name'
            )
            ->firstOrFail();

        if (!$publicProtocol) {
            abort(404);
        }

        $claim = $publicProtocol->claim;

        $lang = $centresService->getCentreLanguage($claim->claim_centre_id);
        App::setLocale($lang);

        $centre = $centresService->getCentre($publicProtocol->claim->claim_centre_id);
        if ($claim->claimArticle->diagram_id) {
            $damageDiagram = (string)$fileService->createImageFromCoordinates(
                $claim->claimArticle->diagram_id,
                $claim->claimArticle->diagram_coordinates,
                'data-url',
                'images/Damage_point_black.png'
            );
        }

        return view('claimForm.claimFormsParts.customerClaimProtocol', [
            'claim' => $claim,
            'centre' => $centre,
            'publicProtocol' => true,
            'damageDiagram' => $damageDiagram ?? null,
            'preferredSolutionNote' => $historyLogRepository->preferredSolutionNote ?? null,
        ]);
    }
}
