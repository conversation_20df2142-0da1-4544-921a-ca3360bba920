<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\Claim;
use App\Traits\Responses;
use Closure;
use Illuminate\Http\Request;

class RemoveSupplierClaimArticleController extends Controller
{
    use Responses;

    /**
     * Handle the incoming request.
     */
    public function __invoke(
        Request $request,
        Claim $claim,
        Article $article
    ) {
        $this->authorize('claimFormsEditing', $claim);
        $supplierClaimArticle = $claim->supplierClaimArticles()->find($article->id);
        $request->validate([
            'count' => [
                'required',
                'numeric',
                'gte:1',
                function (string $attribute, mixed $value, Closure $fail) use ($supplierClaimArticle) {
                    if (($supplierClaimArticle->pivot->quantity - $value) < 0) {
                        $fail(__('validation.resultingNumberOfPiecesCannotBeLessThan', ['value' => 0]));
                    }
                },
            ],
        ]);

        $claim->load('supplierClaimArticles');
        $supplierClaimArticle->pivot->quantity = $supplierClaimArticle->pivot->quantity - $request->count;
        $claim->supplierClaimArticles()
            ->updateExistingPivot(
                $article->id,
                ['quantity' => $supplierClaimArticle->pivot->quantity]
            );

        $log = [
            'goodsid' => $supplierClaimArticle->goodsid,
            'removed' => $request->count,
            'result' => $supplierClaimArticle->pivot->quantity,
        ];

        $claim->createHistoryLog(
            'updated',
            'articleWasRemoved',
            $log
        );

        return $this->response([
            'article' => $supplierClaimArticle
        ], message: __('article.articlesListWasUpdated'));
    }
}
