<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Jobs\Eso\ClaimRestockProcessJob;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\Status;
use App\Models\WarehouseDocumentType;
use App\Services\Claims\ClaimsService;
use App\Services\EsoApi\ClaimsWarehouseDocumentsService;
use App\Services\EsoApi\WarehouseDocumentsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;

class SendB2bB2cReturnsTo3S20Controller extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, ClaimsService $claimsService, Claim $claim)
    {
        $claim->load('b2bB2cWarehouseDocument', 'claimArticle.article');
        $this->authorize('sendB2bB2cClaimTo3s20', $claim);
        $claimsWarehouse = Centre::find(Centre::Warehouse_3R00);
        $destinationWarehouse = Centre::find(Centre::Warehouse_3S20);
        $claim->load('b2bB2cWarehouseDocument');
        $claimsWarehouseDocumentsService = new ClaimsWarehouseDocumentsService($claim);
        $warehouseDocumentsService = new WarehouseDocumentsService($claim);
        $user = auth()->user();
        $articles = $warehouseDocumentsService->formatArticleForDispatch(
            $claim->claimArticle->article->goodsid,
            $claim->claimArticle->quantity
        );

        $claim->fill(['claim_status_id' => Status::CLAIM_STATUS_SENDING_TO_3S20])->save();
        $claim->createHistoryLog('action', 'sendingTo3S20');

        $additionalData = $claimsWarehouseDocumentsService->is_wms_return($claim->claim_code)
            ? $claimsWarehouseDocumentsService->formatAdditionalDataStringForWmsReturns($claim->claim_code)
            : $claim->claim_code;

        try {
            if (!$claim->b2bB2cWarehouseDocument->is_already_received) {
                $b2bB2cWarehouseReceipt = $claimsWarehouseDocumentsService->receiveB2bB2cWarehouseReturn(
                    documentNumber: $claim->b2bB2cWarehouseDocument->claim_number,
                    toCentre: $claimsWarehouse,
                    actionUserId: $user->id
                );
            }

            $expense = $warehouseDocumentsService->createExpense(
                articles: $articles,
                fromCentre: $claimsWarehouse,
                toCentre: $destinationWarehouse,
                actionUser: $user,
                additionalData: $additionalData,
                documentType: WarehouseDocumentType::WAREHOUSE_EXPENSE_3S20
            );

            $warehouseDocumentsService->increaseHeaderState(expense: $expense);

            $warehouseDocumentsService->createWarehouseReceipt(
                expense: $expense,
                toCentre: $destinationWarehouse,
                user: $user
            );

        } catch (\Throwable $e) {
            $error = is_json_string($e->getMessage())
                ? createStringFromIterable(json_decode($e->getMessage()))
                : $e->getMessage();

            //\Log::error($claim->claim_code . ': ' . $error);

            if ($e->getCode() == 408) {
                $chainedJobs = [];
                if (!isset($b2bB2cWarehouseReceipt)) {
                    $chainedJobs[] = new ClaimRestockProcessJob(
                        claim: $claim,
                        additionalDataForEso: $additionalData,
                        actionUser: $user,
                        destinationWarehouse: $claimsWarehouse,
                    );
                }

                $chainedJobs [] = new ClaimRestockProcessJob(
                    claim: $claim,
                    additionalDataForEso: $additionalData,
                    actionUser: $user,
                    destinationWarehouse: $destinationWarehouse,
                    warehouse: $claimsWarehouse,
                    articles: $articles
                );

                $chainedJobs[] = function () use ($claimsService, $claim, $destinationWarehouse, $user) {

                    if (isset($claim->b2bB2cWarehouseDocument->eshop_invoice_number)) {
                        $claimsService->receiveEshopClaimInWarehouse($claim, $claim->claimArticle->article->goodsid, $claim->b2bB2cWarehouseDocument->eshop_invoice_number);
                    }

                    $claimsService->receivedInWarehouse(
                        claim: $claim,
                        selectedWarehouse: $destinationWarehouse,
                        actionUserId: $user->id,
                    );
                };

                Bus::chain($chainedJobs)->dispatch();
                return redirect()->back()->with('status-info', __('eso.esoTransactionMayTakeSeveralMinutesPleaseBePatient'));
            } else {
                return back()->withErrors(json_decode($e->getMessage()));
            }
        }

        if (isset($claim->b2bB2cWarehouseDocument->eshop_invoice_number)) {
            $claimsService->receiveEshopClaimInWarehouse($claim, $claim->claimArticle->article->goodsid, $claim->b2bB2cWarehouseDocument->eshop_invoice_number);
        }

        $claimsService->receivedInWarehouse(
            claim: $claim,
            selectedWarehouse: $destinationWarehouse,
            actionUserId: $user->id,
        );

        return redirect(route('warehouse.claims.show', $claim->id))
            ->with('status-success', __("receiving.sentTo$destinationWarehouse->code"));
    }
}
