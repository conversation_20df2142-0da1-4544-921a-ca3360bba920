<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\ArticleRequest;
use App\Models\Claim;
use App\Services\Claims\ArticlesService;
use App\Services\VermontApiService;
use App\Traits\Responses;

class StoreClaimArticleController extends Controller
{
    use Responses;

    /**
     * Handle the incoming request.
     */
    public function __invoke(
        ArticleRequest $request,
        ArticlesService $articlesService,
        VermontApiService $vermontApiService,
        Claim $claim,
    ) {
        $this->authorize('claimFormsEditing', $claim);

        $apiArticle = $vermontApiService->getFormattedArticle('Goods_ID', $request->goodsid);
        if (!$apiArticle) {
            return $this->errorResponse(__('general.goodsIdNotFound'), 422);
        }

        $currency = $request->currency ? getCurrency('code', $request->currency) : null;
        $claimArticle = $articlesService->createOrUpdateClaimArticle(
            $claim,
            $apiArticle,
            (object)$request->validated(),
            $currency
        );

        return $this->response($claimArticle);
    }
}
