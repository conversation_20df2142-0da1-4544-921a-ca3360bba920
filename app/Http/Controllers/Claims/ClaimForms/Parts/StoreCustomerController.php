<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\CustomerRequest;
use App\Models\Claim;
use App\Services\Claims\CustomersService;
use App\Traits\Responses;

class StoreCustomerController extends Controller
{
    use Responses;

    /**
     * Handle the incoming request.
     */
    public function __invoke(CustomerRequest $request, CustomersService $customersService, Claim $claim)
    {
        $this->authorize('claimFormsEditing', $claim);
        $customer = $customersService->createOrUpdateCustomerForm($claim, $request->validated());
        return $this->response($customer);
    }
}
