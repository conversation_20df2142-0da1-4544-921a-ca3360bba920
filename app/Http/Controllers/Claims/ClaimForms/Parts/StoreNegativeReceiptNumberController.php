<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Jobs\Eso\CreateWarehouseExpenseAndIncreaseHeaderState;
use App\Jobs\Eso\GetPendingWarehouseDocumentJob;
use App\Models\Centre;
use App\Models\Claim;
use App\Services\Claims\ClaimsService;
use App\Services\EsoApi\ClaimsWarehouseDocumentsService;
use App\Services\EsoApi\WarehouseDocumentsService;
use App\Services\VermontApiService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;


class StoreNegativeReceiptNumberController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param VermontApiService $vermontApiService
     * @param ClaimsService $claimsService
     * @param Claim $claim
     * @return array|object
     * @throws \Exception
     */
    public function __invoke(
        Request           $request,
        VermontApiService $vermontApiService,
        ClaimsService     $claimsService,
        Claim             $claim
    )
    {
        $this->authorize('storeNegativeReceiptNumber', $claim);
        $validated = (object)$request->validate(['negative_receipt_number' => 'required']);
        $claim->load(
            'purchaseDocument',
            'claimArticle:id,article_id,quantity',
            'claimArticle.article:id,goodsid',
            'centre'
        );
        $warehouseDocumentsService = new WarehouseDocumentsService($claim);
        $claimsWarehouseDocumentsService = new ClaimsWarehouseDocumentsService($claim);
        $claimCentre = $claim->centre;
        $warehouseCentre = Centre::find(Centre::Warehouse_3R00);
        $user = auth()->user();

        try {
            //get and validate negative receipt
            $negativeReceipt = $vermontApiService->getFormattedReceipt($validated->negative_receipt_number, false);
            if (isset($negativeReceipt, $negativeReceipt['articles'])) {
                $article = collect($negativeReceipt['articles'])
                    ->where('quantity', '<', 0)
                    ->firstWhere(
                        'goodsid',
                        $claim->claimArticle->article->goodsid
                    );

                if (empty($article)) {
                    return redirect()->back()->withErrors(__('closure.articleOnNegativeReceiptNotFound'));
                }
            } else {
                return redirect()->back()->withErrors(__('closure.negativeReceiptNotFound'));
            }


            //mark negative receipt as claim
            $claimsWarehouseDocumentsService->markNegativeReceiptAsClaim($validated->negative_receipt_number,$negativeReceipt);

            //create expense
            $articles = $warehouseDocumentsService->formatArticleForDispatch(
                $claim->claimArticle->article->goodsid,
                $claim->claimArticle->quantity
            );

            $expense = $warehouseDocumentsService->createExpense(
                $articles,
                $claimCentre,
                $warehouseCentre,
                $user,
                $claim->claim_code
            );

            $warehouseDocumentsService->increaseHeaderState($expense);


        } catch
        (\Throwable $e) {

            if ($e->getCode() == 408) {
                $claim->load('activeWarehouseExpense');
                Bus::chain([
                    new CreateWarehouseExpenseAndIncreaseHeaderState($claim, $user),
                    function () use ($claimsService, $claim, $validated, $user) {
                        $claimsService->storeNegativeReceiptNumber(
                            $claim,
                            $validated->negative_receipt_number,
                            $user->id
                        );
                    },
                ])->dispatch();
                return redirect()->back();
            } else {
                return back()->withErrors(json_decode($e->getMessage()));
            }
        }

        $claimsService->storeNegativeReceiptNumber($claim, $validated->negative_receipt_number, $user->id);
        return redirect()->back()->with('status-success', __('closure.negativeReceiptWasSaved'));
    }
}
