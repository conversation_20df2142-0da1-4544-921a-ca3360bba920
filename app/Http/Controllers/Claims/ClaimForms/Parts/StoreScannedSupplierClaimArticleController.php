<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\StoreScannedSupplierClaimArticleRequest;
use App\Models\Claim;
use App\Models\SupplierClaimArticle;
use App\Traits\Responses;
use Illuminate\Database\Eloquent\Builder;

class StoreScannedSupplierClaimArticleController extends Controller
{
    use Responses;

    /**
     * Handle the incoming request.
     */
    public function __invoke(
        StoreScannedSupplierClaimArticleRequest $request,
        Claim $claim
    ) {
        $this->authorize('claimFormsEditing', $claim);
        $supplierClaimArticle = $claim->supplierclaimArticles()->where('ean', $request->ean)->first();

        $claim->supplierclaimArticles()->updateExistingPivot($supplierClaimArticle->id, [
            'quantity' => $supplierClaimArticle->pivot->quantity + 1,
        ]);

        $supplierClaimArticle->pivot->quantity = $supplierClaimArticle->pivot->quantity + 1;

        $log = [
            'goodsid' => $supplierClaimArticle->goodsid,
            'result' => $supplierClaimArticle->pivot->quantity,
        ];

        $claim->createHistoryLog('updated', 'articleWasScanned', $log);
        return $this->response($supplierClaimArticle);
    }
}
