<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\Diagram;
use App\Services\CentresService;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\FileService;
use Illuminate\Http\Request;
use Milon\Barcode\DNS1D;

class SupplierClaimDispatchNoteController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(CentresService $centresService, FileService $fileService, Claim $claim, string $from)
    {
        $this->authorize('showSupplierClaimDispatchNote', $claim);

        $claim->load([
            'supplierClaimArticles',
            'supplierClaimArticle.diagram',
            'type',
            'dispatchNoteWarehouseExpense'
        ]);

        $navigation = new ClaimsNavigationService($claim);
        $warehouseExpense = $claim->dispatchNoteWarehouseExpense?->eso_document_number;

        if ($from == 'warehouse') {
            $backUrl = route('warehouse.claims.show', $claim->id);
        } elseif ($from == 'claimForms') {
            $backUrl = $navigation->showClaim();
        }

        $exampleArticle = $claim->supplierClaimArticles->first();


        return view('claimForm.claimFormsParts.supplierClaimDispatchNote', [
            'claim' => $claim,
            'navigation' => $navigation,
            'warehouseExpense' => $warehouseExpense,
            'exampleArticle' => $exampleArticle,
            'diagram' => $diagram ?? null,
            'backUrl' => $backUrl ?? null,
        ]);
    }
}
