<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Services\EsoApi\ClaimsWarehouseDocumentsService;
use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Throwable;

class UpdateB2bB2cWarehouseDocumentController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Claim $claim)
    {
        $claim->load('b2bB2cWarehouseDocument');
        $this->authorize('updateB2bB2cReturn', [$claim,$claim->b2bB2cWarehouseDocument]);
        $claimsWarehouseDocumentsService = new ClaimsWarehouseDocumentsService($claim);

        try {
            $warehouseDocument = $claimsWarehouseDocumentsService->getB2bB2cWarehouseReturn($claim->b2bB2cWarehouseDocument->claim_number);
            if ($claim->b2bB2cWarehouseDocument->document_status !== $warehouseDocument->stav_vratky ){
                $claim->b2bB2cWarehouseDocument->update(['document_status' => $warehouseDocument->stav_vratky]);
                $claim->createEsoLog('updated','warehouseB2bDocumentUpdated',"stav_vydajky: $warehouseDocument->stav_vratky");
                return back()->with('status-success',__('warehouse.b2bB2cReturnUpdated'));
            }else{
                return back()->with('status-warning',__('receiving.returnStillNotProcessed'));
            }
        } catch (Throwable $e) {
            return back()->withErrors(__('eso.unexpectedErrorOccurredPleaseTryAgain'));
        }
    }
}
