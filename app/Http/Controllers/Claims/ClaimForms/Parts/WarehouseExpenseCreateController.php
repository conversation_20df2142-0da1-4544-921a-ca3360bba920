<?php

namespace App\Http\Controllers\Claims\ClaimForms\Parts;

use App\Http\Controllers\Controller;
use App\Jobs\CheckIfArticleIsInListOfTrackedArticles;
use App\Jobs\Eso\CreateWarehouseExpenseAndIncreaseHeaderState;
use App\Jobs\Eso\GetPendingWarehouseDocumentJob;
use App\Mail\TypeOfDamageWasNoSpecifiedMail;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\DamageType;
use App\Services\Claims\ClaimsService;
use App\Services\EsoApi\WarehouseDocumentsService;
use App\Services\NotificationsService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Bus;

class WarehouseExpenseCreateController extends Controller
{
    /**
     * Handle the incoming request.
     * @param Request $request
     * @param ClaimsService $claimsService
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function __invoke(
        Request       $request,
        ClaimsService $claimsService,
        Claim         $claim
    ): RedirectResponse
    {

        $this->authorize('claimWarehouseExpenseCreate', $claim);
        $warehouseDocumentsService = new WarehouseDocumentsService($claim);
        $claimCentre = $claim->centre;
        $warehouseCentre = Centre::find(Centre::Warehouse_3R00);
        $user = auth()->user();

        $supplierClaim = in_array($claim->claim_type_id, [
            ClaimType::CLAIM_TYPE_SUPPLIER,
            ClaimType::CLAIM_TYPE_SUPPLIER_WAREHOUSE
        ]);

        if ($supplierClaim) {
            $articles = [];
            foreach ($claim->supplierClaimArticles as $supplierClaimArticle) {
                $formattedArticle = $warehouseDocumentsService->formatArticleForDispatch(
                    $supplierClaimArticle->goodsid,
                    $supplierClaimArticle->pivot->quantity
                );
                $articles = array_merge($articles, $formattedArticle);
            }
        } else {
            $articles = $warehouseDocumentsService->formatArticleForDispatch(
                $claim->claimArticle->article->goodsid,
                $claim->claimArticle->quantity
            );
        }

        try {
            $expense = $warehouseDocumentsService->createExpense(
                $articles,
                $claimCentre,
                $warehouseCentre,
                $user,
                $claim->claim_code
            );

            $warehouseDocumentsService->increaseHeaderState($expense);

        } catch (\Throwable $e) {
            if ($e->getCode() == 408) {
                $claim->load('activeWarehouseExpense');
                Bus::chain([
                    new CreateWarehouseExpenseAndIncreaseHeaderState($claim, $user),
                    function () use ($claimsService, $claim, $user) {
                        $claimsService->createWarehouseExpense($claim, $user->id);

                        if (!$claim->is_supplier_claim) {
                            CheckIfArticleIsInListOfTrackedArticles::dispatch($claim);
                        }

                        if (!$claim->is_supplier_claim && $claim->claimArticle->damage_type_id === DamageType::OTHER) {
                            $notificationService = new NotificationsService();
                            $notificationService->sendMails(TypeOfDamageWasNoSpecifiedMail::class, $claim);
                        }
                    },
                ])->dispatch();

                return back();
            } else {
                return back()->withErrors(json_decode($e->getMessage()));
            }
        }


        $claimsService->createWarehouseExpense($claim, auth()->id());

        if (!$claim->is_supplier_claim) {
            CheckIfArticleIsInListOfTrackedArticles::dispatch($claim);
        }

        if (!$claim->is_supplier_claim && $claim->claimArticle->damage_type_id === DamageType::OTHER) {
            $notificationService = new NotificationsService();
            $notificationService->sendMails(TypeOfDamageWasNoSpecifiedMail::class, $claim);
        }

        return redirect()->back()->with('status-success', __('closure.expenseWasCreated'));
    }
}
