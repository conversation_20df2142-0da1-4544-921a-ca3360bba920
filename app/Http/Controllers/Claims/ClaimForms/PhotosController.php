<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\StorePhotoRequest;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\Photo;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\PhotosService;
use App\Services\FileService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Log;
use Pion\Laravel\ChunkUpload\Exceptions\UploadMissingFileException;
use Pion\Laravel\ChunkUpload\Receiver\FileReceiver;

class PhotosController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Claim $claim
     * @param PhotosService $photosService
     * @return View
     */
    public function index(Claim $claim, PhotosService $photosService)
    {
        $this->authorize('showPhotos', $claim);
        $navigation = new ClaimsNavigationService($claim);

        if ($claim->claim_type_id == ClaimType::CLAIM_TYPE_SUPPLIER && $claim->claimArticle->article->surveyCentres()->exists()) {
            $photos = $photosService->getSurveyArticlePhotos($claim->claimArticle->article_id);
        } else {
            $photos = $claim->photos->toArray();
        }

        return view('claimForm.photos', [
            'navigation' => $navigation,
            'articlePhotos' => array_values($photos),
            'claim' => $claim,
            'upload' => false
        ]);
    }

    /**
     * Store a newly created resource in storage.
     * @param StorePhotoRequest $request
     * @param FileService $fileService
     * @param FileReceiver $receiver
     * @param PhotosService $photoService
     * @param Claim $claim
     * @return object
     * @throws UploadMissingFileException
     */
    public function store(
        StorePhotoRequest $request,
        FileService       $fileService,
        FileReceiver      $receiver,
        Claim             $claim
    )
    {

        if (!auth()->user()->can('createOrUpdatePhotos', $claim)) {
            return $this->baseErrorJsonResponse(__('general.unauthorized'));
        }

        if ($receiver->isUploaded() === false) {
            throw new UploadMissingFileException();
        }

        $save = $receiver->receive();
        if ($save->isFinished()) {

            $file = $save->getFile();

            //Size validation
            if ($fileService->validateFileSize($file)) {
                unlink($save->getFile()->getPathname());

                return $this->baseErrorJsonResponse(__('photos.fileSizeExceeded', ['size' => config('media.mb_size_limit_for_video')]));
            }

            //file validation
            if ($fileService->validateFileType($file->getClientOriginalExtension())) {
                unlink($save->getFile()->getPathname());
                Log::info('Unsuported file type uploaded: ' . $file->getClientOriginalExtension());
                return $this->baseErrorJsonResponse(__('photos.invalidFileType', ['ext' => $file->getClientOriginalExtension()]));
            }

            $file = $fileService->storeUploadedFile($file, "/$claim->claim_code/article");

            $claim->createHistoryLog(
                'newPhoto',
                'fileUploaded',
                $file,
            );


            return $claim->photos()->create([
                'name' => $file->name,
                'file_path' => $file->file_path,
                'thumbnail_path' => $file->thumbnail_path ?? null,
                'preview_path' => $file->preview_path ?? null,
                'extension' => $file?->extension,
                'size' => $file->size,
            ]);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @param Claim $claim
     * @param PhotosService $photosService
     * @return View
     */
    public function create(Claim $claim, PhotosService $photosService)
    {
        $this->authorize('showPhotos', $claim);
        $navigation = new ClaimsNavigationService($claim);

        if ($claim->claim_type_id == ClaimType::CLAIM_TYPE_SUPPLIER && $claim->claimArticle->article->surveyCentres()->exists()) {
            $photos = $photosService->getSurveyArticlePhotos($claim->claimArticle->article_id);
        } else {
            $photos = $claim->photos->toArray();
        }

        return view('claimForm.photos', [
            'navigation' => $navigation,
            'articlePhotos' => array_values($photos),
            'claim' => $claim,
            'upload' => true
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param FileService $fileService
     * @param Claim $claim
     * @param Photo $photo
     * @return bool
     * @throws AuthorizationException
     */
    public function destroy(FileService $fileService, Claim $claim, Photo $photo)
    {
        $this->authorize('deletePhoto', $claim);
        if (isset($photo->thumbnail_path)) {
            $fileService->deletePhoto($photo->thumbnail_path);
        }

        if (isset($photo->preview_path)) {
            $fileService->deletePhoto($photo->preview_path);
        }

        $fileService->deletePhoto($photo->file_path);

        $claim->createHistoryLog('deleted', 'fileDeleted', $photo->toArray());

        return $photo->delete();
    }
}
