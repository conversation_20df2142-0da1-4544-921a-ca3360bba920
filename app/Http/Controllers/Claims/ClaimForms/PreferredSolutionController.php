<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\PreferredSolutionRequest;
use App\Models\Claim;
use App\Services\CentresService;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\SolutionsService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class PreferredSolutionController extends Controller
{

    /**
     * Show the form for creating a new resource.
     *
     * @param CentresService $centresService
     * @param Claim $claim
     * @return View
     * @throws AuthorizationException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function create(CentresService $centresService, SolutionsService $solutionsService, Claim $claim): View
    {
        $this->authorize('viewClaimForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $centre = $centresService->getCentre($claim->claim_centre_id);
        $claim->load('purchaseDocument:id,payment_type', 'customer:id');

        if ($claim->purchaseDocument?->payment_type == 'Payback') {
            $solutions = $solutionsService->getAllPaybackSolutions();
        } elseif (!$claim->customer?->id) {
            $solutions = $solutionsService->getAllSolutionsWithoutCustomerInformation();
        } else {
            $solutions = $solutionsService->getAllPreferredSolutions(isset($claim->customer_id));
        }


        return view('claimForm/createPreferredSolution', [
            'navigation' => $navigation,
            'claim' => $claim,
            'centre' => $centre,
            'solutions' => $solutions
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\ClaimForms\PreferredSolutionRequest $request
     * @param SolutionsService $solutionsService
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function store(
        PreferredSolutionRequest $request,
        SolutionsService $solutionsService,
        Claim $claim
    ): RedirectResponse {
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('claimFormsEditing', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->preferredSolutionRoute());
        }

        $solutionsService->storePreferredSolution($claim, (object)$request->validated());

        if (!$navigation->formIsFilled()) {
            request()->session()->flash('status-warning', __('general.pleaseFillFormCorrectly'));
            return redirect($navigation->preferredSolutionRoute());
        } else {
            return redirect($navigation->nextRoute());
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param CentresService $centresService
     * @param Claim $claim
     * @return View
     * @throws AuthorizationException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function edit(
        CentresService $centresService,
        SolutionsService $solutionsService,
        Claim $claim
    ): View {
        $this->authorize('viewClaimForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $centre = $centresService->getCentre($claim->claim_centre_id);
        $claim->load('purchaseDocument:id,payment_type', 'customer:id,bank_account', 'preferredSolutionNote');

        if ($claim->purchaseDocument?->payment_type == 'Payback') {
            $solutions = $solutionsService->getAllPaybackSolutions();
        } elseif (!$claim->customer?->id) {
            $solutions = $solutionsService->getAllSolutionsWithoutCustomerInformation();
        } else {
            $solutions = $solutionsService->getAllPreferredSolutions(isset($claim->customer_id));
        }

        return view('claimForm/editPreferredSolution', [
            'navigation' => $navigation,
            'claim' => $claim,
            'centre' => $centre,
            'solutions' => $solutions,
            'preferredSolutionsIds' => $claim->preferredSolutions->pluck('id'),
            'preferredSolutionNote' => $claim->preferredSolutionNote?->note
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param PreferredSolutionRequest $request
     * @param SolutionsService $solutionsService
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function update(
        PreferredSolutionRequest $request,
        SolutionsService $solutionsService,
        Claim $claim
    ): RedirectResponse {
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('claimFormsEditing', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->preferredSolutionRoute());
        }

        $solutionsService->updatePreferredSolution($claim, (object)$request->validated());
        if (!$navigation->formIsFilled()) {
            request()->session()->flash('status-warning', __('general.pleaseFillFormCorrectly'));
            return redirect($navigation->preferredSolutionRoute());
        } else {
            return redirect($navigation->nextRoute());
        }
    }

}
