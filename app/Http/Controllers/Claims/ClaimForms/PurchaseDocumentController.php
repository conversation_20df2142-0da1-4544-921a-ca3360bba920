<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\PurchaseDocumentRequest;
use App\Models\Claim;
use App\Models\PurchaseDocument;
use App\Models\PurchaseDocumentType;
use App\Services\CentresService;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\PurchaseDocumentsService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class PurchaseDocumentController extends Controller
{

    /**
     * Show the form for creating a new resource.
     *
     * @param CentresService $centresService
     * @param Claim $claim
     * @return View
     * @throws AuthorizationException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function create(CentresService $centresService, Claim $claim): View
    {
        $this->authorize('viewClaimForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $centreCurrency = $centresService->getCentreCurrency($claim->claim_centre_id)->code;
        $purchaseDocumentTypes = PurchaseDocumentType::all();

        return view('claimForm/createPurchaseDocument', [
            'navigation' => $navigation,
            'claim' => $claim,
            'centreCurrency' => $centreCurrency,
            'purchaseDocumentTypes' => $purchaseDocumentTypes,

        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param PurchaseDocumentRequest $request
     * @param PurchaseDocumentsService $purchaseDocumentsService
     * @param Claim $claim
     * @return RedirectResponse|Response
     */
    public function store(
        PurchaseDocumentRequest $request,
        PurchaseDocumentsService $purchaseDocumentsService,
        Claim $claim
    ): RedirectResponse|Response {
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('claimFormsEditing', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->purchaseDocumentRoute());
        }

        $purchaseDocumentsService->storePurchaseDocument($claim, (object)$request->validated());
        return redirect($navigation->nextRoute());
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param CentresService $centresService
     * @param Claim $claim
     * @param PurchaseDocument $purchaseDocument
     * @return View
     * @throws AuthorizationException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function edit(CentresService $centresService, Claim $claim, PurchaseDocument $purchaseDocument): View
    {
        $this->authorize('viewClaimForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $centreCurrency = $centresService->getCentreCurrency($claim->claim_centre_id)->code;
        $purchaseDocumentTypes = PurchaseDocumentType::all();


        if ($claim->customer_id) {
            $customersActionMethod = 'put';
            $customersActionUrl = route(
                'claims.customers.update',
                ['claim' => $claim->id, 'customer' => $claim->customer_id]
            );
        } else {
            $customersActionMethod = 'post';
            $customersActionUrl = route('claims.customers.store', ['claim' => $claim->id]);
        }

        if ($claim->article_id) {
            $articlesActionMethod = 'put';
            $articlesActionUrl = route(
                'claims.articles.update',
                ['claim' => $claim->id, 'article' => $claim->article_id]
            );
        } else {
            $articlesActionMethod = 'post';
            $articlesActionUrl = route('claims.articles.store', ['claim' => $claim->id]);
        }

        return view('claimForm/editPurchaseDocument', [
            'navigation' => $navigation,
            'claim' => $claim,
            'purchaseDocumentTypes' => $purchaseDocumentTypes,
            'centreCurrency' => $centreCurrency,
            'customersActionUrl' => $customersActionUrl,
            'customersActionMethod' => $customersActionMethod,
            'articlesActionUrl' => $articlesActionUrl,
            'articlesActionMethod' => $articlesActionMethod,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param PurchaseDocumentRequest $request
     * @param PurchaseDocumentsService $purchaseDocumentsService
     * @param Claim $claim
     * @param PurchaseDocument $purchaseDocument
     * @return RedirectResponse
     */
    public function update(
        PurchaseDocumentRequest $request,
        PurchaseDocumentsService $purchaseDocumentsService,
        claim $claim,
        PurchaseDocument $purchaseDocument
    ): RedirectResponse {
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('claimFormsEditing', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->purchaseDocumentRoute());
        }

        $purchaseDocumentsService->updatePurchaseDocument($claim, $purchaseDocument, (object)$request->validated());
        return redirect($navigation->nextRoute());
    }

}
