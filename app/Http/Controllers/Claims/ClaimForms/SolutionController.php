<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Enums\Decisions\ClaimsDecisionsEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\SolutionRequest;
use App\Models\Claim;
use App\Models\Solution;
use App\Services\CentresService;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\ClaimsService;
use App\Services\Claims\SolutionsService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class SolutionController extends Controller
{

    /**
     * Show the form for creating a new resource.
     *
     * @param CentresService $centresService
     * @param ClaimsService $claimsService
     * @param SolutionsService $solutionsService
     * @param Claim $claim
     * @return View
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function create(
        CentresService   $centresService,
        ClaimsService    $claimsService,
        SolutionsService $solutionsService,
        Claim            $claim
    ): View
    {
        $claim->load(
            'customer:id,bank_account,email,mobile_number',
            'status:id,name',
            'decisionFormSignedLog.actionUser:id,name'
        );
        $this->authorize('showSolution', $claim);

        if ($claim->decision === ClaimsDecisionsEnum::REJECTED->value) {
            $solutions = $solutionsService->getSolutionForRejectedClaim();
        } elseif ($claim->purchaseDocument->payment_type == 'Payback') {
            $solutions = $solutionsService->getAllPaybackSolutions();
        } else {
            $solutions = $solutionsService->getAllSolutions(isset($claim->customer->id));
        }

        return view('claimForm/createSolution', [
            'navigation' => new ClaimsNavigationService($claim),
            'claim' => $claim,
            'centre' => $centresService->getCentre($claim->claim_centre_id),
            'solutions' => $solutions,
            'decisionProtocolSignedNote' => $claim->decisionFormSignedLog,
            'customerNotificationsNotes' => $claimsService->getCustomerNotificationsLog($claim)
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param SolutionRequest $request
     * @param SolutionsService $solutionsService
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function store(SolutionRequest $request, SolutionsService $solutionsService, Claim $claim)
    {
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('createOrUpdateSolution', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->solutionRoute());
        }

        if ($claim->decision == ClaimsDecisionsEnum::REJECTED->value) {
            $data = [
                'solution_id' => Solution::SOLUTION_RETURN_ARTICLE_TO_CUSTOMER,
                'solved_immediately' => $request->solved_immediately ?? null,
            ];

            $solutionsService->storeSolution($claim, (object)$data);
        } else {
            $solutionsService->storeSolution($claim, (object)$request->validated());
        }

        $navigation->updateState($claim->refresh());

        return redirect($navigation->solutionRoute());
    }

    /**
     * Show the form for creating a new resource.
     *
     * @param CentresService $centresService
     * @param ClaimsService $claimsService
     * @param Claim $claim
     * @return View
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function edit(
        CentresService   $centresService,
        ClaimsService    $claimsService,
        SolutionsService $solutionService,
        Claim            $claim
    ): View
    {
        $claim->load(
            'customer:id,bank_account,email,mobile_number',
            'status:id,name',
            'solution:id,name',
            'decisionFormSignedLog',
            'lastSolutionLog.actionUser:id,name'
        );
        $this->authorize('showSolution', $claim);

        if ($claim->purchaseDocument->payment_type == 'Payback') {
            $solutions = $solutionService->getAllPaybackSolutions();
        } elseif ($claim->decision == ClaimsDecisionsEnum::REJECTED->value) {
            $solutions = $solutionService->getSolutionForRejectedClaim();
        } else {
            $solutions = $solutionService->getAllSolutions(isset($claim->customer->id));
        }

        return view('claimForm/editSolution', [
            'navigation' => new ClaimsNavigationService($claim),
            'claim' => $claim,
            'centre' => $centresService->getCentre($claim->claim_centre_id),
            'solutions' => $solutions,
            'decisionProtocolSignedLog' => $claim->decisionFormSignedLog,
            'customerNotificationsLog' => $claimsService->getCustomerNotificationsLog($claim),
            'solutionLog' => $claim->lastSolutionLog
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param SolutionRequest $request
     * @param SolutionsService $solutionsService
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function update(SolutionRequest $request, SolutionsService $solutionsService, Claim $claim): RedirectResponse
    {
        $claim->load('customer:id,email');
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('createOrUpdateSolution', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->solutionRoute());
        }

        if ($claim->decision == ClaimsDecisionsEnum::REJECTED->value) {
            $data = [
                'solution_id' => Solution::SOLUTION_RETURN_ARTICLE_TO_CUSTOMER,
                'solved_immediately' => $request->solved_immediately ?? null,
            ];

            $solutionsService->storeSolution($claim, (object)$data);
        } else {
            $solutionsService->storeSolution($claim, (object)$request->validated());
        }

        $navigation->updateState($claim->refresh());

        return redirect($navigation->solutionRoute());
    }

}
