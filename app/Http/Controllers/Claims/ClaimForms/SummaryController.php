<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Http\Controllers\Controller;

use App\Http\Requests\ClaimForms\StoreSummaryRequest;
use App\Jobs\CheckDamageTypeValidityJob;
use App\Mail\CustomersMails\ClaimWasRegisteredMail;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\PublicProtocol;
use App\Models\Status;
use App\Notifications\CustomerNotifications\ClaimWasRegisteredNotification;
use App\Services\CentresService;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\ClaimsService;
use ErrorException;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class SummaryController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param StoreSummaryRequest $request
     * @param ClaimsService $claimsService
     * @param Claim $claim
     * @return RedirectResponse|Response
     * @throws ErrorException
     */
    public function store(
        StoreSummaryRequest $request,
        ClaimsService       $claimsService,
        Claim               $claim
    ): Response|RedirectResponse
    {
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('storeSummaryForm', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->SummaryRoute());
        }

        if (!isset($claim->customer->email) && $request->notify_customer_via_email) {
            request()->session()->flash('status-warning', __('emails.customersEmails.emailWasNotFilledMailWasNotSent'));
        } elseif (isset($claim->customer->email, $request->notify_customer_via_email)) {
            PublicProtocol::create([
                'claim_id' => $claim->id,
                'hash' => Hash::make(Str::random(50))
            ]);

            if (config('app.app_server') != 'production') {
                $lang = (new CentresService())->getCentreLanguage($claim->claim_centre_id);


//                Mail::to($claim->customer->email)->locale($lang)->send(
//                    new ClaimWasRegisteredMail($claim)
//                );
            }
        }

        CheckDamageTypeValidityJob::dispatch($claim);
        $claim->fill(['claim_status_id' => Status::CLAIM_STATUS_SIGNED])->save();
        $claim->createHistoryLog('signed', 'protocolWasSigned');

        return redirect($navigation->nextRoute());
    }

    /**
     * Display the specified resource.
     *
     * @param CentresService $centresService
     * @param Claim $claim
     * @return View
     */
    public function show(
        CentresService $centresService,
        Claim          $claim
    ): View
    {
        $this->authorize('viewSummary', $claim);
        $claim->load(
            'purchaseDocument',
            'status',
            'customer',
            'claimArticle.article',
            'claimArticle.currency',
            'solution',
            'type',
            'preferredSolutionNote',
            'claimArticle.damageType',
            'claimArticle.damageSubType',
            'claimArticle.damagePlace',
        );

        $diagram = $claim->claim_type_id == ClaimType::CLAIM_TYPE_SUPPLIER
            ? "surveys/$claim->survey_id/diagram.png"
            : "$claim->claim_code/diagram.png";


        return view('claimForm/summary', [
            'navigation' => new ClaimsNavigationService($claim),
            'claim' => $claim,
            'centre' => $centresService->getCentre($claim->claim_centre_id),
            'diagram' => $diagram,
            'preferredSolutionNote' => $claim->preferredSolutionNote
        ]);
    }
}
