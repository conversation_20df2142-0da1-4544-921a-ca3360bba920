<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Http\Controllers\Controller;
use App\Jobs\Eso\ClaimRestockProcessJob;
use App\Models\Centre;
use App\Models\Claim;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\ClaimsService;
use App\Services\EsoApi\WarehouseDocumentsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;

class SupplierClaimWarehouseReceivingController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(
        Request $request,
        ClaimsService $claimsService,
        Claim $claim
    ) {
        $this->authorize('receiveWarehouseClaim', $claim);
        $warehouseDocumentsService = new WarehouseDocumentsService($claim);
        $selectedWarehouse = Centre::find(Centre::Warehouse_3S12);
        $user = auth()->user();

        try {
            $warehouseDocumentsService->increaseHeaderState($claim->dispatchNoteWarehouseExpense);
            $warehouseDocumentsService->updateExpense(
                $claim->dispatchNoteWarehouseExpense,
                $selectedWarehouse,
                $claim->claim_code,
                $user
            );
            $warehouseDocumentsService->createWarehouseReceipt(
                $claim->dispatchNoteWarehouseExpense,
                $selectedWarehouse,
                $user
            );
        } catch (\Throwable $e) {


            if ($e->getCode() == 408) {
                Bus::chain([
                    new ClaimRestockProcessJob(
                        claim: $claim,
                        additionalDataForEso: $claim->claim_code,
                        actionUser: $user,
                        destinationWarehouse: $selectedWarehouse,
                    ),
                    function () use ($claimsService, $claim, $selectedWarehouse, $user) {
                        $claimsService->receivedInWarehouse(
                            claim: $claim,
                            selectedWarehouse: $selectedWarehouse,
                            actionUserId: $user->id,
                        );
                    }
                ])->dispatch();
                return redirect()->back();
            } else {
                return redirect()->back()->withErrors(json_decode($e->getMessage()));
            }
        }

        $claimsService->receivedInWarehouse($claim, $selectedWarehouse, $user->id);

        return redirect(route('warehouse.claims.index'))->with(
            'status-success',
            __("receiving.sentTo$selectedWarehouse->code")
        );
    }

    /**
     * Display the specified resource.
     */
    public function show(Claim $claim)
    {
        $this->authorize('showWarehouseReceivingForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $supplierClaimWarehouse = Centre::find(Centre::Warehouse_3S12);
        $claim->load('supplierClaimArticle.diagram', 'supplierClaimArticle.photos', 'supplierClaimArticles');

        return view('claimForm.supplierClaimWarehouseReceiving', [
            'navigation' => $navigation,
            'claim' => $claim,
            'supplierClaimWarehouse' => $supplierClaimWarehouse,
            'photos' => $claim->supplierClaimArticle->photos->toArray(),
        ]);
    }
}
