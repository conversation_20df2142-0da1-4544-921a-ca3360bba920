<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\StoreSupplierClaimArticles;
use App\Models\Claim;
use App\Models\Status;
use App\Models\SupplierClaimArticle;
use App\Services\Claims\ClaimsNavigationService;
use Illuminate\Http\Request;

class SupplierClaimsArticles extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Claim $claim)
    {
        $this->authorize('viewClaimForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $claim->load('supplierClaimArticles:id,goodsid,ean');
        $supplierClaimArticleCodes = SupplierClaimArticle::select('article_code')
            ->where('status_id', Status::SUPPLIER_CLAIM_ARTICLE_ACTIVE)
            ->groupBy('article_code')
            ->get()
            ->pluck('article_code')
            ->map(function ($articleCode, $key) {
                $explodedArticleCode = explode('-', $articleCode);
                return "$explodedArticleCode[0]-$explodedArticleCode[1]-$explodedArticleCode[3]";
            })
            ->unique();

        if (!$claim->supplierClaimArticles->isEmpty()) {
            $selectedArticle = $claim->supplierClaimArticles->first();
            $explodedArticleGoodsId = explode('-', $selectedArticle->goodsid);
            $selectedArticle = "$explodedArticleGoodsId[0]-$explodedArticleGoodsId[1]-$explodedArticleGoodsId[3]";
        }


        return view('claimForm.supplierClaimArticles', [
            'navigation' => $navigation,
            'claim' => $claim,
            'selectedArticle' => $selectedArticle ?? null,
            'supplierClaimArticles' => $claim->supplierClaimArticles,
            'supplierClaimArticleCodes' => $supplierClaimArticleCodes,
        ]);
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSupplierClaimArticles $request, Claim $claim)
    {
        $this->authorize('claimFormsEditing', $claim);

        $explodedArticleCode = explode('-', $request->article_code);
        $articleCode = "$explodedArticleCode[0]-$explodedArticleCode[1]-__-$explodedArticleCode[2]";
        $supplierClaimArticle = SupplierClaimArticle::where('article_code', 'like', "$articleCode%")
            ->with('selectedArticles')
            ->first();

        if ($claim->supplier_claim_article_id) {
            $claim->supplierClaimArticles()->detach();
        }

        $claim->fill(['supplier_claim_article_id' => $supplierClaimArticle->id])->save();
        $claim->createHistoryLog('updated', 'articleFormUpdated', $request->article_code);


        $articlesForAttach = $supplierClaimArticle->selectedArticles->pluck('id')->toArray();
        $claim->supplierClaimArticles()->sync($articlesForAttach);

        return back()->with('status-success', __('article.articleWasSavedPleaseStartScanningDamagedArticles'));
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
