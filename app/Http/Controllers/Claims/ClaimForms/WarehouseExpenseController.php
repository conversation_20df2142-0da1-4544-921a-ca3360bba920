<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\WarehouseExpenseRequest;
use App\Models\Article;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\Status;
use App\Models\SupplierClaimArticle;
use App\Models\WarehouseDocument;
use App\Models\WarehouseDocumentType;
use App\Services\Claims\ArticlesService;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\EsoApi\ClaimsWarehouseDocumentsService;
use App\Services\VermontApiService;
use Throwable;

class WarehouseExpenseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Claim $claim)
    {
        $this->authorize('viewClaimForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        return view('claimForm.createWarehouseExpense', [
            'navigation' => $navigation,
            'claim' => $claim,
            'selectable' => $claim->claim_type_id != ClaimType::CLAIM_TYPE_SUPPLIER_WAREHOUSE,
            'searchExpenseUrl' => route('claims.search.warehouseDocument', $claim->id)
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(WarehouseExpenseRequest $request, Claim $claim)
    {
        $this->authorize('claimFormsEditing', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $claimsWarehouseDocumentsService = new ClaimsWarehouseDocumentsService($claim);

        try {
            $warehouseExpense = $claimsWarehouseDocumentsService->getClaimsWarehouseExpense($request->document_number);
        } catch (Throwable $e) {

            return back()->withErrors(json_decode($e->getMessage()));
        }

        if (isset($request->selected_article)) {
            $articlesService = new ArticlesService();
            $apiService = new VermontApiService();
            $apiArticle = $apiService->getFormattedArticle('Goods_ID', $request->selected_article['goods_id']);
            $articlesService->createOrUpdateClaimArticle($claim, $apiArticle);
        };

        $toCentre = Centre::where('code', $warehouseExpense->kod_skladu)->first();

        if (!isset($toCentre)) {
            return redirect()->back()->withErrors(__('warehouse.centreNotFoundInAllowedWarehousesList'));
        }

        $claim->warehouseDocuments()->create([
            'document_type_id' => WarehouseDocumentType::DISPATCH_NOTE_WAREHOUSE_EXPENSE,
            'eso_idhdok' => $warehouseExpense->idhdok,
            'eso_document_number' => $warehouseExpense->cis_dok,
            'to_centre' => $toCentre->id,
            'header_state' => 0,
            'is_processed' => 1,
            'eso_transaction_status_id' => Status::ESO_TRANSACTION_FINISHED
        ]);

        $claim->refresh();
        $claim->createHistoryLog(
            'created',
            'warehouseExpenseFormFilled',
            $request->document_number
        );

        return redirect($navigation->nextRoute())
            ->with('status-success', __('claims.messages.warehouseExpenseSaved'));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Claim $claim, WarehouseDocument $warehouseDocument)
    {
        $this->authorize('viewClaimForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        return view('claimForm.editWarehouseExpense', [
            'navigation' => $navigation,
            'claim' => $claim,
            'warehouseDocument' => $warehouseDocument,
            'selectable' => $claim->claim_type_id != ClaimType::CLAIM_TYPE_SUPPLIER_WAREHOUSE,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(
        WarehouseExpenseRequest $request,
        Claim                   $claim,
        WarehouseDocument       $warehouseDocument
    )
    {
        $this->authorize('claimFormsEditing', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $claimsWarehouseDocumentsService = new ClaimsWarehouseDocumentsService($claim);
        try {
            $warehouseExpense = $claimsWarehouseDocumentsService->getClaimsWarehouseExpense($request->document_number);
        } catch (Throwable $e) {

            return redirect()->back()->withErrors(json_decode($e->getMessage()));
        }

        if (isset($request->selected_article)) {
            $articlesService = new ArticlesService();
            $apiService = new VermontApiService();
            $apiArticle = $apiService->getFormattedArticle('Goods_ID', $request->selected_article['goods_id']);
            $articlesService->createOrUpdateClaimArticle($claim, $apiArticle);
            $claim->refresh();
        }


        $toCentre = Centre::where('code', $warehouseExpense->kod_skladu)->first();
        $warehouseDocument->update([
            'document_type_id' => WarehouseDocumentType::DISPATCH_NOTE_WAREHOUSE_EXPENSE,
            'claim_id' => $claim->id,
            'eso_idhdok' => $warehouseExpense->idhdok,
            'eso_document_number' => $warehouseExpense->cis_dok,
            'to_centre' => $toCentre->id,
            'header_state' => 1,
            'is_processed' => 1,
            'eso_transaction_status_id' => Status::ESO_TRANSACTION_FINISHED
        ]);

        $claim->createHistoryLog(
            'updated',
            'warehouseExpenseFormUpdated',
            $request->document_number
        );

        return redirect($navigation->nextRoute())->with(
            'status-success',
            __('claims.messages.warehouseExpenseUpdated')
        );
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
