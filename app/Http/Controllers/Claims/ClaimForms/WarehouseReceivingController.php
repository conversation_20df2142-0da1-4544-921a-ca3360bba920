<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\WarehouseClaimRestockRequest;
use App\Jobs\CheckDamageTypeValidityJob;
use App\Jobs\Eso\ClaimRestockProcessJob;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\Status;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\ClaimsService;
use App\Services\EsoApi\WarehouseDocumentsService;
use Illuminate\Support\Facades\Bus;

class WarehouseReceivingController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(
        WarehouseClaimRestockRequest $request,
        ClaimsService                $claimsService,
        Claim                        $claim
    )
    {
        $this->authorize('receiveWarehouseClaim', $claim);
        $warehouseDocumentsService = new WarehouseDocumentsService($claim);
        $selectedWarehouse = Centre::find($request->warehouse_id);
        $batchId = $request->batch_id;
        $user = auth()->user();
        $articles = $warehouseDocumentsService->formatArticleForDispatch(
            $claim->claimArticle->article->goodsid,
            $claim->claimArticle->quantity
        );

        CheckDamageTypeValidityJob::dispatch($claim);

        try {
            if ($selectedWarehouse->id != Centre::Warehouse_3R00) {
                $warehouseCentre3R00 = Centre::find(Centre::Warehouse_3R00);
                $expense = $warehouseDocumentsService->createExpense(
                    $articles,
                    $warehouseCentre3R00,
                    $selectedWarehouse,
                    $user,
                    $claim->claim_code
                );
                $warehouseDocumentsService->increaseHeaderState($expense);
                $warehouseDocumentsService->createWarehouseReceipt($expense, $selectedWarehouse, $user);
            }
        } catch (\Throwable $e) {

            if ($e->getCode() == 408) {
                Bus::chain([
                    new ClaimRestockProcessJob(
                        claim: $claim,
                        additionalDataForEso: $claim->claim_code,
                        actionUser: $user,
                        destinationWarehouse: $selectedWarehouse,

                    ),
                    function () use ($claimsService, $claim, $selectedWarehouse, $user, $batchId) {
                        $claimsService->receivedInWarehouse(
                            claim: $claim,
                            selectedWarehouse: $selectedWarehouse,
                            actionUserId: $user->id,
                            batchId: $batchId,
                        );
                    }
                ])->dispatch();
                return redirect()->back();
            } else {
                return back()->withErrors(json_decode($e->getMessage()));
            }
        }

        $claimsService->receivedInWarehouse($claim, $selectedWarehouse, $user->id, $request->batch_id);

        return redirect(route('warehouse.claims.index'))
            ->with('status-success', __("receiving.sentTo$selectedWarehouse->code"));
    }

    /**
     * Display the specified resource.
     */
    public function show(ClaimsService $claimsService, Claim $claim)
    {
        $this->authorize('showWarehouseReceivingForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $availableWarehouses = $claimsService->getAvailableWarehouses($claim);
        $photos = $claim->photos;
        $photos->toArray();

        $photosRequired = $claim->warehouseClaimsPhotosRequired
            && $photos->count() == 0
            && $claim->claim_status_id == Status::CLAIM_STATUS_CREATED;

        return view('claimForm.warehouseReceiving', [
            'navigation' => $navigation,
            'claim' => $claim,
            'availableWarehouses' => $availableWarehouses,
            'photos' => $photos,
            'photosRequired' => $photosRequired
        ]);
    }
}
