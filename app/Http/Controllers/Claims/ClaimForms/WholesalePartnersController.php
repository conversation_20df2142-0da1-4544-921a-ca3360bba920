<?php

namespace App\Http\Controllers\Claims\ClaimForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\WholesaleCustomerRequest;
use App\Models\Claim;
use App\Models\WholesalePartner;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\WholesalePartnersService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;

class WholesalePartnersController extends Controller
{

    /**
     * Show the form for creating a new resource.
     *
     * @param WholesalePartnersService $wholesalePartnersService
     * @param Claim $claim
     * @return View
     * @throws AuthorizationException
     */
    public function create(WholesalePartnersService $wholesalePartnersService, Claim $claim): View
    {
        $this->authorize('viewClaimForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $wholesalePartners = $wholesalePartnersService->getAllWsPartners();

        return view('claimForm/createWholesalePartner', [
            'navigation' => $navigation,
            'claim' => $claim,
            'wholesalePartners' => $wholesalePartners,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param WholesaleCustomerRequest $request
     * @param WholesalePartnersService $wholesalePartnersService
     * @param Claim $claim
     * @return Response|RedirectResponse
     */
    public function store(
        WholesaleCustomerRequest $request,
        WholesalePartnersService $wholesalePartnersService,
        Claim $claim
    ): Response|RedirectResponse {
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('claimFormsEditing', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->wholeSalePartnerRoute());
        }

        $wholesalePartnersService->storeWsPartnerForm($claim, (object)$request->validated());

        return redirect($navigation->nextRoute());
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param WholesalePartnersService $wholesalePartnersService
     * @param Claim $claim
     * @param WholesalePartner $wholesalePartner
     * @return View
     * @throws AuthorizationException
     */
    public function edit(
        WholesalePartnersService $wholesalePartnersService,
        Claim $claim,
        WholesalePartner $wholesalePartner
    ): View {
        $this->authorize('viewClaimForm', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $wholesalePartners = $wholesalePartnersService->getAllWsPartners();
        return view('claimForm/editWholesalePartner', [
            'navigation' => $navigation,
            'claim' => $claim,
            'wholesalePartner' => $wholesalePartner,
            'wholesalePartners' => $wholesalePartners,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param WholesaleCustomerRequest $request
     * @param WholesalePartnersService $wholesalePartnersService
     * @param Claim $claim
     * @return Response|RedirectResponse
     */
    public function update(
        WholesaleCustomerRequest $request,
        WholesalePartnersService $wholesalePartnersService,
        Claim $claim
    ): Response|RedirectResponse {
        $navigation = new ClaimsNavigationService($claim);

        if (!auth()->user()->can('claimFormsEditing', $claim)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->wholeSalePartnerRoute());
        }

        $wholesalePartnersService->updateWsPartnerForm($claim, (object)$request->validated());

        return redirect($navigation->nextRoute());
    }
}
