<?php

namespace App\Http\Controllers\Claims;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\ClaimHistoryLog;
use App\Services\CentresService;
use App\Services\Claims\ClaimsNavigationService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\View\View;

class ClaimLogsController extends Controller
{
    /**
     * Claim logs view.
     * @param CentresService $centresService
     * @param string $from
     * @param Claim $claim
     * @return View
     */
    public function __invoke(CentresService $centresService, string $from, Claim $claim): View
    {
        $this->authorize('viewLogs', $claim);
        $navigation = new ClaimsNavigationService($claim);
        $backUrl = $from == 'warehouse' ? route('warehouse.claims.show', $claim->id) : $navigation->showClaim();
        $logs = ClaimHistoryLog::where('claim_id', $claim->id)
            ->with('actionUser:id,name')
            ->oldest()
            ->paginate(20);

        return view('claimLogs', [
            'claim' => $claim,
            'logs' => $logs,
            'redirectUrl' => $backUrl
        ]);
    }
}
