<?php

namespace App\Http\Controllers\Claims;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Services\StatusesService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;

class ClaimsAdministration extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param StatusesService $statusesService
     * @param ClaimType $claimType
     * @param Claim $claim
     * @return View
     */
    public function __invoke(
        Request $request,
        StatusesService $statusesService,
        ClaimType $claimType,
        Claim $claim
    ): View {
        if (!Gate::allows('claimsAdministration')) {
            abort(403);
        }

        return view('claimsAdministration', [
            'claim' => $claim,
            'statuses' => $statusesService->allClaimStatuses(),
            'claimTypes' => $claimType->getClaimTypesForRetail(),
        ]);
    }
}
