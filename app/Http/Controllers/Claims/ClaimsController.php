<?php

namespace App\Http\Controllers\Claims;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\StoreClaimRequest;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\PublicProtocol;
use App\Models\Solution;
use App\Models\Status;
use App\Services\CentresService;
use App\Services\Claims\ArticlesService;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\ClaimsService;
use App\Services\FileService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\Rule;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class ClaimsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @param ClaimType $claimType
     * @param ClaimsService $claimsService
     * @param ArticlesService $articlesService
     * @param CentresService $centresService
     * @return View
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */

    public function index(
        Request $request,
        ClaimType $claimType,
        ClaimsService $claimsService,
        ArticlesService $articlesService,
        CentresService $centresService
    ): View {
        $statuses = Status::where('type', 'claim')->get();
        $userCentres = $centresService->loggedUserCentres();
        $solutions = Solution::all();
        $claimTypes = $claimType->getClaimTypesForRetail();
        $trademarks = $articlesService->getAllArticlesTrademarks();
        $Centres = auth()->user()->hasPermissionTo('view all claims')
            ? $centresService->allRetailCentres()
            : $userCentres;

        $filter = ($request->filter) ?: ((session()->get('claimsFilter')) ? session()->get('claimsFilter') : []);

        if ($filter) {
            session()->put('claimsFilter', $filter);
        }
        $claims = $claimsService->filteredClaims($filter, $userCentres)->paginate(20);


        return view('claimsIndex', [
            'filter' => $filter,
            'userCentres' => $userCentres,
            'claims' => $claims,
            'centres' => $Centres,
            'solutions' => $solutions,
            'trademarks' => $trademarks,
            'statuses' => $statuses,
            'claimTypes' => $claimTypes
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @param CentresService $centresService
     * @param ClaimTypesService $claimTypesService
     * @return View
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function create(CentresService $centresService): View
    {
        $user = auth()->user();
        $claimType = new ClaimType();

        if (!isset($user->centre) ||!$user->hasAnyPermission('create claims', 'ws partner claims create')) {
            abort(403);
        }

        match (true) {
            $user->hasPermissionTo('claims administration') => [
                $claimTypes = $claimType->getClaimTypesForRetail(),
                $userCentre = $centresService->loggedUserCentres(),
                $Centres = $centresService->allRetailCentres(),
            ],

            $user->hasPermissionTo('ws partner claims create') => [
                $claimTypes = $claimType->getClaimTypesForWholesalePartners(),
                $userCentre = Centre::find(1), // docasne 0S00
                $Centres = null,
            ],
            $user->hasPermissionTo('create claims') => [
                $claimTypes = $claimType->getClaimTypesForCentres(),
                $userCentre = $centresService->loggedUserCentres(),
                $Centres = $centresService->allRetailCentres(),
            ],
        };

        return view('claimForm.createClaim', [
            'centres' => $Centres,
            'userCentre' => $userCentre,
            'claimTypes' => $claimTypes
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\ClaimForms\StoreClaimRequest $request
     * @param ClaimsService $claimsService
     * @param CentresService $centresService
     * @return RedirectResponse
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function store(
        StoreClaimRequest $request,
        ClaimsService $claimsService,
        CentresService $centresService
    ): RedirectResponse {
        Gate::authorize('create-claim', $request->input('claimTypeId'));

        //iba docasne pre wholesale partnera centrum 0S00
        if ($request->input('claimTypeId') == ClaimType::CLAIM_TYPE_WHOLESALE_PARTNER) {
            $centre = Centre::find(1);
        } else {
            $centre = $centresService->getCentre($request->input('centre'));
        }
        $claim = $claimsService->createClaim($centre, $request->input('claimTypeId'), auth()->user());

        //redirect to first available route for selected claim type
        $navigation = new ClaimsNavigationService($claim);
        $redirectTo = array_values($navigation->navigation())[0]['href'];
        return redirect($redirectTo);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param claim $claim
     * @return RedirectResponse
     * @throws AuthorizationException
     */
    public function update(
        Request $request,
        claim $claim
    ): RedirectResponse {
        if (!Gate::allows('claimsAdministration')) {
            abort(403);
        } elseif ($request->claim_type_id) {
            $this->authorize('claimFormsEditing', $claim);
        }

       $request->validate([
            'claim_status_id' => 'required|numeric',
            'claim_type_id' => [
                'numeric',
                Rule::requiredIf($claim->claim_status_id == Status::CLAIM_STATUS_CREATED)
            ],
        ]);

        $oldClaim = clone $claim;
        $claim->fill([
            'claim_status_id' => $request->claim_status_id,
            'claim_type_id' => $request->claim_type_id ?? $claim->claim_type_id,
            'eshop_order' => $request->claim_type_id === ClaimType::CLAIM_TYPE_CUSTOMER_ESHOP ? 1 : 0
        ])->save();

        if ($claim->claim_status_id != $oldClaim->claim_status_id) {
            $claim->createHistoryLog('administration', 'claimStatusWasUpdated', $claim->claim_status_id);
        }
        if ($claim->claim_type_id != $oldClaim->claim_type_id) {
            $claim->createHistoryLog('administration', 'claimTypeWasUpdated', $claim->claim_type_id);
        }

        request()->session()->flash('status-success', __('administration.claims.claimWasUpdated'));
        $navigation = new ClaimsNavigationService($claim);
        return redirect($navigation->firstRoute());
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function destroy(claim $claim): RedirectResponse
    {

        if (!Gate::allows('claimsAdministration')) {
            abort(403);
        }
        $fileService = new FileService();
        PublicProtocol::where('claim_id', $claim->id)->delete();

        foreach ($claim->photos as $photo) {
            $fileService->deletePhoto($photo->thumbnail_path);
            $fileService->deletePhoto($photo->preview_path);
            $fileService->deletePhoto($photo->file_path);
            $claim->createHistoryLog('deleted', 'photoWasDeleted', $photo->toArray());
            $photo->delete();
        }


        if ($claim->delete()) {
            $claim->createHistoryLog('deleted', 'claimWasDeleted');
            request()->session()->flash('status-success', __('administration.claims.claimWasDeleted'));
            return redirect(route('claims.index'));
        } else {
            request()->session()->flash('status-danger', __('administration.deleteFail'));
            return back();
        }
    }
}
