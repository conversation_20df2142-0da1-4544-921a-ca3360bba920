<?php

namespace App\Http\Controllers\Claims;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Services\CentresService;
use App\Services\Claims\ClaimsNavigationService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\View\View;

class EsoLogsController extends Controller
{
    /**
     * eso logs view.
     *
     * @param CentresService $centresService
     * @param string $from
     * @param Claim $claim
     * @return View
     */
    public function __invoke(CentresService $centresService, string $from, Claim $claim): View
    {
        $this->authorize('viewLogs', $claim);

        $navigation = new ClaimsNavigationService($claim);
        $backUrl = $from == 'warehouse' ? route('warehouse.claims.show', $claim->id) : $navigation->showClaim();
        $logs = $claim->esoHistoryLogs()
            ->with('actionUser:id,name')
            ->oldest('id')
            ->paginate(20);

        return view('esoLogs', [
            'claim' => $claim,
            'logs' => $logs,
            'redirectUrl' => $backUrl
        ]);
    }
}
