<?php

namespace App\Http\Controllers\Claims;

use App\Exports\ClaimsExport;
use App\Http\Controllers\Controller;
use App\Services\CentresService;
use App\Services\Claims\ClaimsService;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class ExportFilteredClaimsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, CentresService $centresService, ClaimsService $claimsService)
    {
        $user = auth()->user();
        $this->authorize('claims administration', $user);
        $date = date("Y.m.d");
        $claims = $claimsService->filteredClaims(session()->get('claimsFilter'), null)->get();
        $limit = 10000;
        if ($claims->count() > $limit) {
            session()->flash('status-danger', __('general.export_limit_exceeded', ['limit' => $limit, 'count' => $claims->count()]));

            return response([
                'success' => false,
                'message' => 'Limit for export exceeded'
            ], 422);
        }

        return Excel::download(new ClaimsExport($claims), "claims-$date.xlsx");
    }
}
