<?php

namespace App\Http\Controllers\Claims;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\GetArticleRequest;
use App\Services\VermontApiService;

class GetAllArticles extends Controller
{
    /**
     * get article from Api.
     *
     * @param GetArticleRequest $request
     * @return object|null
     */
    public function __invoke(GetArticleRequest $request): ?object
    {
        $api = new VermontApiService();
        $apiArticles = $api->getAllArticles($request->key, $request->value);
        return $apiArticles ?? null;
    }
}
