<?php

namespace App\Http\Controllers\Claims;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\GetArticleRequest;
use App\Models\Claim;
use App\Models\SupplierClaimArticle;
use App\Services\VermontApiService;
use Illuminate\Database\Eloquent\Builder;

class GetArticleController extends Controller
{
    /**
     * get article from Api.
     *
     * @param GetArticleRequest $request
     * @param Claim $claim
     * @return object|null
     */
    public function __invoke(GetArticleRequest $request, Claim $claim): ?object
    {
        $api = new VermontApiService;
        $article = $api->getFormattedArticle($request->key, $request->value);
        if ($article) {
            $supplierClaimArticle = SupplierClaimArticle::whereHas(
                'selectedArticles',
                function (Builder $q) use ($article) {
                    $q->where('goodsid', $article->goodsid);
                }
            )->with('selectedArticles','damageType','damageSubType','damagePlace')->first();
        }

        return response([
            'article' => $article,
            'supplierClaimArticle' => $supplierClaimArticle ?? null,
        ]);
    }
}
