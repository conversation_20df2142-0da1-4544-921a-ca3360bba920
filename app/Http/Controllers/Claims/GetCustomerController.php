<?php

namespace App\Http\Controllers\Claims;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\GetCustomerRequest;
use App\Services\VermontApiService;


class GetCustomerController extends Controller
{
    /**
     * get customer from Vermont club Api .
     *
     * @param GetCustomerRequest $request
     * @return object|null
     */
    public function __invoke(GetCustomerRequest $request): ?object
    {
        $api = new VermontApiService;
        return $api->getFormattedCustomer($request->code);
    }

}
