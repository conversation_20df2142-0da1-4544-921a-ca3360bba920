<?php

namespace App\Http\Controllers\Claims;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\GetReceiptRequest;
use App\Services\VermontApiService;


class GetReceiptController extends Controller
{
    /**
     * get receipt from Vermont club Api .
     *
     * @param GetReceiptRequest $request
     * @return object|null
     */
    public function __invoke(GetReceiptRequest $request): ?object
    {
        $api = new VermontApiService();
        return $api->getFormattedReceipt($request->code);
    }
}
