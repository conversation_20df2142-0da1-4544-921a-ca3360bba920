<?php

namespace App\Http\Controllers;

use App\Models\Claim;
use App\Services\Claims\ClaimsNavigationService;
use Illuminate\Http\Request;

class ClaimsSignPostEmailLinksController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Claim $claim)
    {
        $navigation = new ClaimsNavigationService($claim);
        return redirect($navigation->showClaim());
    }
}
