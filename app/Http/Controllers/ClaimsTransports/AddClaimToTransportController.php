<?php

namespace App\Http\Controllers\ClaimsTransports;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\ClaimsTransport;
use App\Services\ClaimsTransports\ClaimsTransportsService;
use Illuminate\Http\Request;

class AddClaimToTransportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, ClaimsTransportsService $claimsTransportService, ClaimsTransport $claimsTransport)
    {

        $request->validate(['claim_id' => 'required|numeric']);

        $claim = Claim::find($request->claim_id);

        if (!$claim) {
            return $this->baseErrorJsonResponse(__('general.claimNotFound'), 422);
        }

        try {
            $claimsTransportService->addClaimToTransport($claimsTransport, $claim);
            $claimsTransportService->resetIsPhysicallyCheckedAttribute($claimsTransport);
        } catch (\Throwable $throwable) {
            return $this->baseErrorJsonResponse($throwable->getMessage(), 422);
        }


        return $this->baseJsonResponse();
    }
}
