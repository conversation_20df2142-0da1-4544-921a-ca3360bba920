<?php

namespace App\Http\Controllers\ClaimsTransports;

use App\Enums\ClaimsTransports\CarrierStatusesEnum;
use App\Enums\ClaimsTransports\ClaimsTransportsLogActionsEnum;
use App\Enums\ClaimsTransports\ClaimsTransportsStatusesEnum;
use App\Enums\Logs\LogActionTypes;
use App\Http\Controllers\Controller;
use App\Models\ClaimsTransport;
use App\Services\ClaimsTransports\ClaimsTransportsService;
use App\Services\ClaimsTransports\TransportHubService;
use App\Services\Logs\LogsService;

class CancelOrderedClaimsTransportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(ClaimsTransportsService $claimsTransportsService, ClaimsTransport $claimsTransport)
    {
        $this->authorize('cancel-transport', $claimsTransport);

        //State
        $transportHubService = new TransportHubService();
        $logsService = new LogsService();

        $claimsTransport->load('centre', 'claims', 'packages');
        $sentPackages = $claimsTransport->packages->where('status', CarrierStatusesEnum::ORDERED->value);

        $err = false;
        $errMessages = [];

        //API  call
        if ($sentPackages->isNotEmpty()) {

            try {
                $res = $transportHubService->cancelTransport($claimsTransport, $sentPackages->pluck('tracking_no')->toArray());

                $packageLogs = [];

                foreach ($res->details as $detail) {
                    if ($detail->isSuccessful === true) {
                        $package = $sentPackages->firstWhere('tracking_no', $detail->shippingLabel);
                        $package->fill(['status' => CarrierStatusesEnum::CANCELED->value]);
                        $packageLogs['items'][] = $logsService->getModelChanges($package);
                        $package->save();
                    } else {
                        $err = true;
                        $errMessages[] = "$detail->shippingLabel:  $detail->message";
                    }
                }

                $claimsTransport->logs()->create([
                    'action' => ClaimsTransportsLogActionsEnum::TRANSPORT_HUB_CANCEL_ORDER->value,
                    'creator_id' => auth()->id(),
                    'creator_type' => auth()->user()->getMorphClass(),
                    'changes' => json_encode($packageLogs),
                ]);


            } catch (\Throwable $exception) {
                \Log::error("Action:cancel-order, Transport: $claimsTransport->id, Details: {$exception->getMessage()} ");
                return back()->withErrors(__('general.anUnexpectedErrorOccurredPleaseTryAgainLater'));
            }
        }

        //If not all packages have been canceled, return the error
        if ($err) {
            return redirect(route('claimsTransports.summaries.show', $claimsTransport->id))->withErrors(__('claimsTransports.errors.transportCannotBeCancelled'));
        }


        //Remove claims from transport
        foreach ($claimsTransport->claims as $claim) {
            $claimsTransportsService->removeClaimFromTransport($claimsTransport, $claim,true);
        }

        //Delete transport
        $claimsTransport->fill(['status' => ClaimsTransportsStatusesEnum::CANCELED->value]);
        $changes = $logsService->getModelChanges($claimsTransport);
        $claimsTransport->save();

        if (isset($packageLogs)) {
            $changes['items'] = $packageLogs;
        }

        $claimsTransport->logs()->create([
            'action' => ClaimsTransportsLogActionsEnum::DELETED->value,
            'creator_id' => auth()->user()->id,
            'creator_type' => auth()->user()->getMorphClass(),
            'changes' => json_encode($changes),
        ]);


        return redirect(route('claimsTransports.index'))
            ->with('status-success', __('claimsTransports.messages.actions', ['action' => trans_choice('general.canceled', 2)]));
    }
}
