<?php

namespace App\Http\Controllers\ClaimsTransports;

use App\Http\Controllers\Controller;
use App\Models\ClaimsTransport;
use Illuminate\Http\Request;

class ClaimsTransportApiLogs extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, ClaimsTransport $claimsTransport)
    {
        $this->authorize('view-logs', $claimsTransport);

        return view('claimsTransports.apiLogs', [
            'claimsTransport' => $claimsTransport,
            'logs' => $claimsTransport->apiLogs()->paginate(20),
        ]);
    }
}
