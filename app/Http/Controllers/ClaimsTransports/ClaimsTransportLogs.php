<?php

namespace App\Http\Controllers\ClaimsTransports;

use App\Http\Controllers\Controller;
use App\Models\ClaimsTransport;
use Illuminate\Http\Request;

class ClaimsTransportLogs extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, ClaimsTransport $claimsTransport)
    {
        $this->authorize('view-logs', $claimsTransport);

        return view('claimsTransports.logs', [
            'claimsTransport' => $claimsTransport,
            'logs' => $claimsTransport->logs()->paginate(20),
        ]);
    }
}
