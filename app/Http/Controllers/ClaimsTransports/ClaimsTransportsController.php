<?php

namespace App\Http\Controllers\ClaimsTransports;

use App\Enums\ClaimsTransports\CarrierTypesEnum;
use App\Enums\ClaimsTransports\ClaimsTransportsLogActionsEnum;
use App\Enums\ClaimsTransports\ClaimsTransportsStatusesEnum;
use App\Enums\ClaimsTransports\ClaimsTransportTypesEnum;
use App\Enums\Logs\LogActionTypes;
use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\ClaimsTransport;
use App\Services\CentresService;
use App\Services\ClaimsTransports\ClaimsTransportsNavigationService;
use App\Services\ClaimsTransports\ClaimsTransportsService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class ClaimsTransportsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = auth()->user();

        $centresService = new CentresService();
        $userCentres = $centresService->loggedUserCentres();

        //Filter options
        $claimsTransportTypes = ClaimsTransportTypesEnum::translatedValues();
        $carrierTypes = CarrierTypesEnum::translatedValues();
        $claimsTransportStatuses = ClaimsTransportsStatusesEnum::translatedValues();

        $creators = ClaimsTransport::select('creator_id')
            ->distinct()
            ->with('creator')
            ->get()
            ->pluck('creator')
            ->sortBy(function ($creator) {
                return $creator->name;
            })
            ->values();


        if (auth()->user()->hasAnyPermission('claims transports administration', 'view all claims transports')) {
            $centres = (new CentresService())->allRetailCentres();
        }

        $filter = ($request->filter) ?: ((session()->get('claims-transports-filter')) ? session()->get('claims-transports-filter') : []);
        $claimsTransports = ClaimsTransport::with('packages', 'claims:id,claim_type_id,claim_article_id', 'claims.claimArticle:id,quantity', 'claims.supplierClaimArticles:id')
            ->when(!$user->hasPermissionTo('claims transports administration') && !$user->hasPermissionTo('view all claims transports'), function (Builder $query) use ($userCentres) {
                $query->whereIn('centre_id', $userCentres?->pluck('id')?->toArray() ?? []);
            })
            ->filter(filter: $filter, saveToSession: 'claims-transports-filter', defaultDateFormat: getDateFormatsByLang())
            ->latest('id')
            ->paginate(10);


        return view('claimsTransports.index', [
            'claimsTransports' => $claimsTransports,
            'claimsTransportTypes' => $claimsTransportTypes,
            'filter' => $filter,
            'carrierTypes' => $carrierTypes,
            'centres' => $centres ?? null,
            'claimsTransportStatuses' => $claimsTransportStatuses,
            'creators' => $creators,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
//        $this->authorize('create-claims-transport', ClaimsTransport::class);
//
//        return view('claimsTransports.create', [
//            'claimsTransportTypes' => ClaimsTransportTypesEnum::translatedValues(),
//            'availableCentres' => (new CentresService())->getAvailableCentresForCurrentUser(auth()->user()),
//        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, ClaimsTransportsService $claimsTransportService)
    {
        $this->authorize('create-claims-transport', ClaimsTransport::class);
        $centre = auth()->user()->centre;
        $claimsTransport = $claimsTransportService->create(ClaimsTransportTypesEnum::INTERNAL_AND_CUSTOMER->value, $centre, auth()->user());

        return redirect(route('claimsTransports.edit', $claimsTransport->id))
            ->with('status-success', __('claimsTransports.messages.actions', ['action' => trans_choice('general.created', 2)]));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $this->authorize('view-claims-transport', ClaimsTransport::class);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ClaimsTransport $claimsTransport, ClaimsTransportsService $claimsTransportService)
    {
        $this->authorize('edit-claims-transport', $claimsTransport);
        $claimsTransportNavigation = (new ClaimsTransportsNavigationService())->navigation($claimsTransport);

        $claimsTransport->load('centre', 'creator', 'claims.claimArticle:id,quantity,article_id', 'claims.claimArticle.article:id,goodsid', 'claims.supplierClaimArticles');

        $requiredClaimsStatus = $claimsTransportService->getRequiredClaimsStatus($claimsTransport->transport_type);

        $availableClaimsForTransport = Claim::where('claim_centre_id', $claimsTransport->centre_id)
            ->with('claimArticle:id,quantity,article_id', 'claimArticle.article:id,goodsid', 'supplierClaimArticles')
            ->when($claimsTransport->transport_type === ClaimsTransportTypesEnum::ECOM_CUSTOMER->value, function ($query) {
                $query->whereHas('claimArticle.article');
            })
            ->whereDoesntHave('transports')
            ->where('claim_status_id', $requiredClaimsStatus)
            ->where('claim_centre_id', $claimsTransport->centre_id)
            ->whereIn('claim_type_id', $claimsTransportService->convertTransferTypeToClaimTypes($claimsTransport->transport_type))
            ->where('is_active', 1)
            ->get();

        $availableClaimsForTransport = $claimsTransportService->formatClaimsCollectionAttributesForTransport($availableClaimsForTransport);
        $transportedClaims = $claimsTransportService->formatClaimsCollectionAttributesForTransport($claimsTransport->claims);


        return view('claimsTransports.edit', [
            'claimsTransport' => $claimsTransport,
            'claimsTransportNavigation' => $claimsTransportNavigation,
            'availableClaimsForTransport' => $availableClaimsForTransport,
            'transportedClaims' => $transportedClaims,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $this->authorize('edit-claims-transport', ClaimsTransport::class);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ClaimsTransportsService $claimsTransportsService, ClaimsTransport $claimsTransport)
    {
        $this->authorize('delete-claims-transport', $claimsTransport);

        $claimsTransport->load('centre', 'claims', 'packages');

        //Remove claims from transport
        foreach ($claimsTransport->claims as $claim) {
            $claimsTransportsService->removeClaimFromTransport($claimsTransport, $claim);
        }

        $claimsTransport->logs()->create([
            'action' => ClaimsTransportsLogActionsEnum::DELETED->value,
            'creator_id' => auth()->user()->id,
            'creator_type' => auth()->user()->getMorphClass(),
        ]);


        //Delete transport
        $claimsTransport->delete();


        return redirect(route('claimsTransports.index'))
            ->with('status-success', __('claimsTransports.messages.actions', ['action' => trans_choice('general.deleted', 2)]));

    }
}
