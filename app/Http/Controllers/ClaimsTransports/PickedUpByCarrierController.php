<?php

namespace App\Http\Controllers\ClaimsTransports;

use App\Enums\ClaimsTransports\CarrierStatusesEnum;
use App\Http\Controllers\Controller;
use App\Models\ClaimsTransport;
use App\Models\ClaimsTransportsPackage;
use App\Services\ClaimsTransports\ClaimsTransportsService;
use App\Services\Logs\LogsService;
use Illuminate\Http\Request;

class PickedUpByCarrierController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, ClaimsTransport $claimsTransport, ClaimsTransportsPackage $claimsTransportsPackage)
    {
        $this->authorize('can-be-transferred-to-carrier', [$claimsTransport, $claimsTransportsPackage]);

        $claimsTransportsPackage->fill(['status' => CarrierStatusesEnum::SENT->value]);
        $logsService = new LogsService();
        $changes = $logsService->getModelChanges($claimsTransportsPackage);
        $claimsTransportsPackage->save();

        $claimsTransport->load('packages', 'claims');

        if ($claimsTransport->packages->firstWhere('status', '<=', CarrierStatusesEnum::ORDERED->value) === null) {
            $claimsTransportsService = new ClaimsTransportsService();
            $claimsTransportsService->sentToWarehouse($claimsTransport, $changes);
        }

        return redirect(route('claimsTransports.summaries.show', $claimsTransport->id))
            ->with('status-success', __('claimsTransports.messages.actions', ['action' => trans_choice('general.updated', 2)]));
    }
}
