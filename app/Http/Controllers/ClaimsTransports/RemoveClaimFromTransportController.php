<?php

namespace App\Http\Controllers\ClaimsTransports;

use App\Http\Controllers\Controller;
use App\Models\ClaimsTransport;
use App\Services\ClaimsTransports\ClaimsTransportsService;
use Illuminate\Http\Request;

class RemoveClaimFromTransportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request,ClaimsTransportsService $claimsTransportService, ClaimsTransport $claimsTransport)
    {
        $request->validate(['claim_id' => 'required|numeric']);

        $claim = $claimsTransport->claims()->where('claims.id', $request->claim_id)->first();

        if (!$claim) {
            return $this->baseErrorJsonResponse(__('general.claimNotFound'), 422);
        }

        try {
            $claimsTransportService->removeClaimFromTransport($claimsTransport, $claim);
            $claimsTransportService->resetIsPhysicallyCheckedAttribute($claimsTransport);
        } catch (\Throwable $throwable){
            return $this->baseErrorJsonResponse($throwable->getMessage(), 422);
        }

        return $this->baseJsonResponse();
    }
}
