<?php

namespace App\Http\Controllers\ClaimsTransports;

use App\Enums\ClaimsTransports\ClaimsTransportsLogActionsEnum;
use App\Enums\Logs\LogActionTypes;
use App\Http\Controllers\Controller;
use App\Models\ClaimsTransport;
use App\Models\ClaimsTransportsPackage;
use App\Services\ClaimsTransports\TransportHubService;
use App\Services\Logs\LogsService;
use Illuminate\Http\Request;

class ReorderToExternalTransportController extends Controller
{
    /**
     * Handle the incoming request.
     *
     */
    public function __invoke(Request $request, ClaimsTransport $claimsTransport, ClaimsTransportsPackage $claimsTransportsPackage)
    {
        $this->authorize('reorder-transport', [$claimsTransport, $claimsTransportsPackage]);

        $transportHubService = new TransportHubService();
        $logsService = new LogsService();

        try {
            $res = $transportHubService->reorderTransport($claimsTransport, $claimsTransportsPackage);

            if ($res->success === true && $res->details[0]->isSuccessful === true) {

                $claimsTransportsPackage->fill(['carrier_type' => $res->details[0]->newCarrierTitle]);
                $changes['packages'][$claimsTransportsPackage->tracking_no] = $logsService->getModelChanges($claimsTransportsPackage);
                $claimsTransportsPackage->save();

                $claimsTransport->logs()->create([
                    'action' => ClaimsTransportsLogActionsEnum::REORDERED_TO_EXTERNAL->value,
                    'creator_id' => auth()->id(),
                    'creator_type' => auth()->user()->getMorphClass(),
                    'changes' => json_encode($changes),
                ]);

                return redirect(route('claimsTransports.summaries.show', $claimsTransport->id))
                    ->with('status-success', __('claimsTransports.messages.actions', ['action' => trans_choice('general.updated', 2)]));
            }

        } catch (\Throwable $exception) {
            return back()->withErrors(__('general.anUnexpectedErrorOccurredPleaseTryAgainLater'));
        }
    }
}
