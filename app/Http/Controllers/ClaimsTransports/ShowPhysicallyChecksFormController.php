<?php

namespace App\Http\Controllers\ClaimsTransports;

use App\Http\Controllers\Controller;
use App\Models\ClaimsTransport;
use App\Services\ClaimsTransports\ClaimsTransportsNavigationService;
use Illuminate\Http\Request;

class ShowPhysicallyChecksFormController extends Controller
{
    public function __invoke(Request $request, ClaimsTransport $claimsTransport)
    {
        $this->authorize('physicallyCheck', $claimsTransport);

        $claimsTransport->load('centre', 'creator', 'claims.claimArticle:id,quantity,article_id', 'claims.claimArticle.article:id,goodsid');


        return view('claimsTransports.physicallyChecksFrom', [
            'claimsTransport' => $claimsTransport,
            'claimsTransportNavigation' => (new ClaimsTransportsNavigationService())->navigation($claimsTransport)
        ]);
    }
}
