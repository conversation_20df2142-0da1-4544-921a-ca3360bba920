<?php

namespace App\Http\Controllers\ClaimsTransports;

use App\Enums\ClaimsTransports\ClaimsTransportsStatusesEnum;
use App\Http\Controllers\Controller;
use App\Models\ClaimsTransport;
use App\Services\ClaimsTransports\ClaimsTransportsNavigationService;
use App\Services\ClaimsTransports\ClaimsTransportsService;
use Illuminate\Http\Request;

class ShowSummariesController extends Controller
{
    public function __invoke(ClaimsTransport $claimsTransport, ClaimsTransportsService $claimsTransportsService)
    {
        $this->authorize('view-claims-transport-summary', $claimsTransport);


        $claimsTransport->load('centre', 'creator');


        $claims = $claimsTransport->status === ClaimsTransportsStatusesEnum::CANCELED->value
            ? $claimsTransport->softDeletedClaims()->get()
            : $claimsTransport->claims()->get();


        $transportedClaims = $claimsTransportsService->formatClaimsCollectionAttributesForTransport($claims);


        return view('claimsTransports.summariesFrom', [
            'claimsTransport' => $claimsTransport,
            'transportedClaims' => $transportedClaims,
            'claimsTransportNavigation' => (new ClaimsTransportsNavigationService())->navigation($claimsTransport)

        ]);
    }
}
