<?php

namespace App\Http\Controllers\ClaimsTransports;

use App\Enums\ClaimsTransports\ClaimsTransportsStatusesEnum;
use App\Http\Controllers\Controller;
use App\Models\ClaimsTransport;
use App\Services\ClaimsTransports\ClaimsTransportsNavigationService;
use Illuminate\Http\Request;

class SignpostController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, ClaimsTransport $claimsTransport)
    {
        $nonEditableStatuses = [ClaimsTransportsStatusesEnum::ORDERED, ClaimsTransportsStatusesEnum::SENT];

        if (in_array($claimsTransport->status, $nonEditableStatuses) || $claimsTransport->is_physically_checked === 1) {
            return redirect(route('claimsTransports.summaries.show', $claimsTransport->id));
        } else {
            return redirect(route('claimsTransports.edit', $claimsTransport->id));
        }
    }
}
