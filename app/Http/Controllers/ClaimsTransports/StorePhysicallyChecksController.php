<?php

namespace App\Http\Controllers\ClaimsTransports;

use App\Enums\ClaimsTransports\ClaimsTransportsLogActionsEnum;
use App\Enums\Logs\LogActionTypes;
use App\Http\Controllers\Controller;
use App\Models\ClaimsTransport;
use App\Services\Logs\LogsService;
use Illuminate\Http\Request;

class StorePhysicallyChecksController extends Controller
{
    public function __invoke(Request $request, ClaimsTransport $claimsTransport)
    {
        $this->authorize('edit-claims-transport', $claimsTransport);

        $request->validate([
            'count' => 'required|numeric',
        ]);


        if ($claimsTransport->claims()->count() !== (int)$request->count) {
            return $this->baseErrorJsonResponse(__('claimsTransports.errors.NumberOfClaimsDoesNotMatchWithEnteredNumber'));
        }

        $logsService = new LogsService();

        $claimsTransport->fill(['is_physically_checked' => true]);

        $changes = $logsService->getModelChanges($claimsTransport);
        $claimsTransport->logs()->create([
            'action' => ClaimsTransportsLogActionsEnum::PHYSICALLY_CHECKED->value,
            'creator_id' => auth()->id(),
            'creator_type' => auth()->user()->getMorphClass(),
            'changes' => json_encode($changes),
        ]);

        $claimsTransport->save();

        redirect(route('claimsTransports.summaries.show', $claimsTransport->id));

    }
}
