<?php

namespace App\Http\Controllers\ClaimsTransports;

use App\Enums\ClaimsTransports\ClaimsTransportsLogActionsEnum;
use App\Enums\ClaimsTransports\ClaimsTransportsStatusesEnum;
use App\Enums\Logs\LogActionTypes;
use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\ClaimHistoryLog;
use App\Models\ClaimsTransport;
use App\Models\Status;
use App\Services\ClaimsTransports\TransportHubService;
use App\Services\Logs\LogsService;
use Illuminate\Http\Request;

class StoreSummariesController extends Controller
{

    public function __invoke(Request $request, ClaimsTransport $claimsTransport)
    {
        $this->authorize('edit-claims-transport', $claimsTransport);

        $request->validate(['note' => 'string|nullable']);

        //TRANSPORT HUB
        $transportHubService = new TransportHubService();

        try {
            $transportHubService->orderTransport($claimsTransport);
        } catch (\Throwable $throwable) {
            $errMessage = $throwable->getMessage();
            \Log::error("Error from Transport hub API. Action:create-order, Transport: $claimsTransport->id, Details: $errMessage ");

            $claimsTransport->logs()->create([
                'action' => LogActionTypes::ERROR->value,
                'creator_id' => auth()->user()->id,
                'creator_type' => auth()->user()->getMorphClass(),
                'note' => $errMessage
            ]);

            return back()->withErrors(__('general.anUnexpectedErrorOccurredPleaseTryAgainLater'));
        }

        $claimsTransport->fill([
            'status' => ClaimsTransportsStatusesEnum::WAITING_FOR_PROCESSING->value,
            'note' => $request->input('note'),
        ]);

        $logsService = new LogsService();
        $changes = $logsService->getModelChanges($claimsTransport);

        $claimsTransport->logs()->create([
            'action' => ClaimsTransportsLogActionsEnum::STATUS_UPDATED->value,
            'creator_id' => auth()->user()->id,
            'creator_type' => auth()->user()->getMorphClass(),
            'changes' => is_null($changes) ? $changes : json_encode($changes),
        ]);

        $claimsTransport->save();

        $claimsLogs = [];
        $claimsIds = [];

        foreach ($claimsTransport->claims as $claim) {
            $claimsIds[] = $claim->id;

            $timestamp = now();
            $claimsLogs[] = [
                'claim_id' => $claim->id,
                'claim_responsible_person_id' => $claim->claim_responsible_person_id,
                'action_user_id' => auth()->id(),
                'status_id' => $claim->claim_status_id,
                'action_type' => 'created',
                'action' => 'transportOrdered',
                'note' => "Transport: $claimsTransport->transport_id",
                'created_at' => $timestamp,
                'updated_at' => $timestamp,
            ];
        }

        Claim::whereIn('id', $claimsIds)->update(['claim_status_id' => Status::CLAIM_STATUS_IN_TRANSPORT]);
        ClaimHistoryLog::insert($claimsLogs);

        return redirect(route('claimsTransports.summaries.show', $claimsTransport->id))
            ->with('status-success', __('claimsTransports.messages.actions', ['action' => trans_choice('claimsTransports.sent', 1)]));

    }
}
