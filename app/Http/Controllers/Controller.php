<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;


class Controller extends BaseController
{
    use AuthorizesRequests;
    use DispatchesJobs;
    use ValidatesRequests;

    public function baseResponseStructure(mixed $data = null, $message = 'Request was processed'): array
    {
        $response = [
            'success' => true,
            'message' => $message,
        ];

        if (isset($data)) {
            $response['data'] = $data;
        }

        return $response;
    }

    public function baseErrorResponseStructure($message = 'Request failed', array $errors = []): array
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        return $response;
    }

    public function baseJsonResponse(mixed $data = null, $message = 'Request was processed', $code = 200)
    {
        $response = [
            'success' => true,
            'message' => $message,
        ];

        if (isset($data)) {
            $response['data'] = $data;
        }

        return response()->json($response, $code);
    }

    public function baseResponse(mixed $data = null, $message = 'Request was processed', $code = 200)
    {

        $response = [
            'success' => true,
            'message' => $message,
        ];

        if (isset($data)) {
            $response['data'] = $data;
        }

        return response($response, $code);
    }

    public function baseErrorJsonResponse($message = 'Request failed', $code = 500, array $errors = [])
    {

        $response = [
            'success' => false,
            'message' => $message,
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $code);
    }

    public function baseErrorResponse($message = 'Request failed', $code = 500, array $errors = [])
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        return response($response, $code);
    }

}
