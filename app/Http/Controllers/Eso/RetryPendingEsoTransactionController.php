<?php

namespace App\Http\Controllers\Eso;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\Status;
use App\Models\WarehouseDocument;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class RetryPendingEsoTransactionController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $request->validate(['claim_id' => 'required|numeric|exists:claims,id']);

        $warehouseDocument = WarehouseDocument::where([
            ['warehouse_documentable_id', $request->claim_id],
            ['warehouse_documentable_type', Claim::class],
            ['eso_transaction_status_id', Status::ESO_TRANSACTION_FAILED_ON_TIMEOUT]
        ])->first();

        $claim = Claim::find($request->claim_id);
        $claim->createEsoLog('esoTransaction', 'manuallyRepeatingProcess', $warehouseDocument?->failed_job_uuid, auth()->id());

        $warehouseDocument->fill(['eso_transaction_status_id' => Status::ESO_TRANSACTION_PENDING])->save();

        Artisan::call("queue:retry $warehouseDocument->failed_job_uuid");

        return redirect()->back();
    }
}
