<?php

namespace App\Http\Controllers;

use App\Mail\SendEmailToSupport;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class SendEmailToSupportController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function __invoke(Request $request): RedirectResponse
    {
        if (!auth()->user()?->centre) {
            request()->session()->flash('status-danger', __('general.sendingFailedBecauseCentreWasNotDetected'));
            return redirect()->back();
        }

        $validated = (object)$request->validate([
            'department_email' => 'required|email',
            'text' => 'required|string'
        ]);

        $user = auth()->user();
        $centre = $user?->centre;
        $url = url()->previous();

        Mail::to($request->department_email)
            ->locale('sk')
            ->send(
                new SendEmailToSupport(
                    user: $user,
                    department: $validated->department_email,
                    text: $validated->text,
                    centre: $centre,
                    url: $url
                )
            );

        $request->session()->flash('status-success', __('support.emailForSupportWasSent'));

        //TODO: vyriesit logovanie

        return redirect(route('claims.index'));
    }
}
