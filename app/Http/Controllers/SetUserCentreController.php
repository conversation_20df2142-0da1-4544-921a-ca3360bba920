<?php

namespace App\Http\Controllers;

use App\Models\Centre;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class SetUserCentreController extends Controller
{
    /**
     * Change user's centre
     * @param $centreCode
     * @return RedirectResponse
     */
    public function __invoke($centreCode): RedirectResponse
    {
        if (auth()->user()->hasAnyPermission('change centre')) {
            $centre = Centre::where('code', $centreCode)->get();

            request()->session()->put('userCentres', $centre);
            session()->put('userCentreNotFound', false);
            Cache::forget('uncheckedSurveysCount');
        }

        return redirect()->back();
    }
}
