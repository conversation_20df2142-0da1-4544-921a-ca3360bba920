<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class SignpostController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function __invoke(Request $request): RedirectResponse
    {
        $user = auth()->user();

        if ($user->hasRole('administrator')) {
            $redirect = route('claims.index');
        } elseif ($user->hasAnyRole('brand manager', 'warehouse worker')) {
            $redirect = route('warehouse.claims.index');
        } else {
            $redirect = route('claims.index');
        }

        return redirect($redirect);
    }
}
