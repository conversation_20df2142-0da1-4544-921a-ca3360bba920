<?php

namespace App\Http\Controllers\Survey;

use App\Http\Controllers\Controller;
use App\Models\Centre;
use App\Models\Survey;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ShowCentreSurveyController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Survey $survey, Centre $centre)
    {
        if ($request->user()->cannot('showSurvey', [$survey, $centre])) {
            return redirect(route('surveys.index'))->withErrors(__('survey.you_dont_have_permission_to_view_this_survey_please_check_centre'));
        }


        $this->authorize('showSurvey', [$survey, $centre]);
        $survey->load('exampleArticle:id,goodsid,name,size,ean,color,season,code,order_group_id', 'damageType', 'damageSubType');
        $orderGroup = getOrderGroup('id', $survey->exampleArticle?->order_group_id);
        $surveyCentres = $survey->surveyCentres()
            ->where('centre_id', $centre->id)
            ->with('articles:id,goodsid', 'centre:id,code')
            ->get();

        $surveyCentreArticles = collect();
        foreach ($surveyCentres as $surveyCentre) {
            foreach ($surveyCentre->articles as $article) {
                $article->damaged_count = $article->pivot->damaged_count ?? 0;
                $article->articles_count = $article->pivot->articles_count;
                $article->centres = array($surveyCentre->centre->code);
                $surveyCentreArticles->push($article);
            }
        }


        return view('surveys.centreSurveyShow', [
            'survey' => $survey,
            'diagram' => $orderGroup->diagram,
            'photos' => $survey->photos,
            'centre' => $centre,
            'surveyCentreArticles' => $surveyCentreArticles
        ]);
    }
}
