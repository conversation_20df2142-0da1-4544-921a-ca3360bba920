<?php

namespace App\Http\Controllers\Survey;

use App\Http\Controllers\Controller;
use App\Mail\Survey\AllSurveysAreFilledMail;
use App\Models\Batch;
use App\Models\Survey;
use App\Services\CentresService;
use App\Services\NotificationsService;
use App\Services\Survey\SurveyService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;


class ShowWarehouseSurveyController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(
        Request $request,
        CentresService $centresService,
        SurveyService $surveyService,
        Survey $survey
    ) {
        if ($request->user()->cannot('showWarehouseSurvey', $survey)) {
            abort(404);
        }

        $survey->load('exampleArticle:id,goodsid,name,size,ean,color,season,code,order_group_id');
        $warehouseCentres = $centresService->allWarehouses()->pluck('id')->toArray();
        $orderGroup = getOrderGroup('id', $survey->exampleArticle?->order_group_id);

        $surveyCentres = $survey->surveyCentres()
            ->whereIn('centre_id', $warehouseCentres)
            ->with('articles:id,goodsid', 'centre:id,code')
            ->get();

        $surveyArticles = collect();
        foreach ($surveyCentres as $surveyCentre) {
            foreach ($surveyCentre->articles as $article) {
                if ($centresService->warehouseHasBatches($surveyCentre->centre->id)) {
                    $articleBatches = Batch::whereHas('claims', function (Builder $q) use ($article) {
                        $q->whereHas('claimArticle.article', function (Builder $q) use ($article) {
                            $q->where('goodsid', $article->goodsid);
                        });
                    })->get();

                    $article->batches = $articleBatches->pluck('id')->toArray();
                }

                $article->damaged_count = $article->pivot->damaged_count;
                $article->articles_count = $article->pivot->articles_count;
                $article->centre = $surveyCentre->centre;
                $surveyArticles->push($article);
            }
        }

        if ($surveyService->allSurveysAreFilled($survey)) {
            $notificationsService = new NotificationsService();
            $notificationsService->sendMails(AllSurveysAreFilledMail::class, $survey);
        }


        return view('surveys.warehouseSurveyShow', [
            'survey' => $survey,
            'diagram' => $orderGroup->diagram,
            'photos' => $survey->photos,
            'surveyArticles' => $surveyArticles
        ]);
    }
}
