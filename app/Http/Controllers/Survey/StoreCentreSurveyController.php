<?php

namespace App\Http\Controllers\Survey;

use App\Http\Controllers\Controller;
use App\Http\Requests\Survey\FillSurveyRequest;
use App\Mail\Survey\AllSurveysAreFilledMail;
use App\Models\Centre;
use App\Models\Status;
use App\Models\Survey;
use App\Services\NotificationsService;
use App\Services\Survey\SurveyService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\RedirectResponse;

class StoreCentreSurveyController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(
        FillSurveyRequest $request,
        SurveyService     $surveyService,
        Survey            $survey,
        Centre            $centre
    ): RedirectResponse
    {
        $this->authorize('showSurvey', [$survey, $centre]);
        $surveyService->FillCentreSurvey($survey, $centre, $request->validated('articles'));


        $surveyService->createSurveyHistoryLog(
            $survey,
            'updated',
            'surveyCompleted',
            $centre->code . ': ' . createStringFromIterable((object)$request->validated('articles'))
        );


        if ($surveyService->allSurveysAreFilled($survey)) {
            $notificationsService = new NotificationsService();
            $notificationsService->sendMails(AllSurveysAreFilledMail::class, $survey);
        }

        return redirect(route('surveys.index'))->with('status-success', __(__('survey.surveyWasFilled')));
    }
}
