<?php

namespace App\Http\Controllers\Survey;

use App\Http\Controllers\Controller;
use App\Http\Requests\Survey\FillSurveyRequest;
use App\Models\Centre;
use App\Models\Survey;
use App\Services\Survey\SurveyService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class StoreWarehouseSurveyController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(
        FillSurveyRequest $request,
        SurveyService $surveyService,
        Survey $survey,
        Centre $centre
    ): RedirectResponse {
        $this->authorize('fillWarehouseSurvey', $survey);
        $surveyService->FillWarehouseSurvey($survey, $request->validated('articles'));
        $surveyService->createSurveyHistoryLog(
            $survey,
            'updated',
            'surveyCompleted',
            createStringFromIterable((object)$request->validated('articles'))
        );
        return redirect(route('surveys.index'))->with('status-success', __(__('survey.surveyWasFilled')));
    }
}
