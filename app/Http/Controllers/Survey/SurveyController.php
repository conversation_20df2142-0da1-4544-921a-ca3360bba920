<?php

namespace App\Http\Controllers\Survey;

use App\Http\Controllers\Controller;
use App\Models\Centre;
use App\Models\Status;
use App\Models\Survey;
use App\Services\Survey\SurveyNavigationService;
use App\Services\Survey\SurveyService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class SurveyController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @param SurveyService $surveyService
     * @return View
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function index(Request $request, SurveyService $surveyService): View
    {
        $filter = ($request->filter) ?: ((session()->get('surveyFilter')) ? session()->get('surveyFilter') : []);
        $user = auth()->user();

        if ($filter) {
            session()->put('surveyFilter', $filter);
        }

        if ($user->hasAnyPermission('supplier claims administration', 'view all surveys')) {
            $surveys = $surveyService->getAllSurveys($filter);
        } elseif ($user->hasPermissionTo('view warehouse surveys')) {
            $surveys = $surveyService->getWarehouseSurveys($filter);
        } else {
            $surveys = $surveyService->getCentreSurveys($filter);
        }


        $statuses = Status::whereIn('id', [
            Status::SURVEY_CREATED,
            Status::SURVEY_IN_PROGRESS,
            Status::SURVEY_CLOSED,
        ])->get();


        return view('surveys.index', [
            'surveys' => $surveys,
            'centres' => Centre::all(),
            'filter' => $filter,
            'statuses' => $statuses
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @param SurveyService $surveyService
     * @return RedirectResponse
     */
    public function store(Request $request, SurveyService $surveyService): RedirectResponse
    {
        $this->authorize('create surveys');
        $survey = $surveyService->storeSurvey();
        $navigation = new SurveyNavigationService($survey);
        $redirectTo = array_values($navigation->navigation())[0]['href'];
        return redirect($redirectTo);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Survey $survey
     * @return RedirectResponse
     */
    public function destroy(Survey $survey): RedirectResponse
    {
        $this->authorize('deleteSurvey', $survey);

        $survey->surveyCentres()->delete();
        $survey->historyLogs()->delete();
        $survey->delete();

        return redirect(route('surveys.index'))->with('status-success', __('survey.surveyWasDeleted'));
    }
}
