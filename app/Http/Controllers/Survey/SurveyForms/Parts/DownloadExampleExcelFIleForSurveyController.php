<?php

namespace App\Http\Controllers\Survey\SurveyForms\Parts;

use App\Http\Controllers\Controller;
use App\Services\FileService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class DownloadExampleExcelFIleForSurveyController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $this->authorize('supplier claims administration');

        $fileService = new FileService();
        return Storage::disk($fileService->getFilesDisk())->download('/surveys/exampleFile.xlsx', 'example.xlsx');
    }
}
