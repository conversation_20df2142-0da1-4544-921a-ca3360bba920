<?php

namespace App\Http\Controllers\Survey\SurveyForms\Parts;

use App\Http\Controllers\Controller;
use App\Models\Survey;
use App\Services\FileService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;

class DownloadImportedFileController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param Survey $survey
     * @return StreamedResponse
     */
    public function __invoke(Request $request, Survey $survey): StreamedResponse
    {
        $fileService = new FileService();
        return Storage::disk($fileService->getFilesDisk())->download(
            $survey->importedFile->path,
            $survey->importedFile->name
        );
    }
}
