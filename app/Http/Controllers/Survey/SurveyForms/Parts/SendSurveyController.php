<?php

namespace App\Http\Controllers\Survey\SurveyForms\Parts;

use App\Http\Controllers\Controller;
use App\Mail\Survey\SurveyCreatedMail;
use App\Mail\Survey\SurveyResultMail;
use App\Models\Centre;
use App\Models\Status;
use App\Models\Survey;
use App\Notifications\InternalNotifications\SurveyNotifications\SurveyCreated;
use App\Services\CentresService;
use App\Services\NotificationsService;
use App\Services\Survey\SurveyService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;

class SendSurveyController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param SurveyService $surveyService
     * @param Survey $survey
     * @return RedirectResponse
     */
    public function __invoke(Request $request, SurveyService $surveyService, Survey $survey): RedirectResponse
    {
        $this->authorize('createOrUpdateSurveyForms', $survey);
        $survey->load('surveyCentres:id,survey_id,centre_id', 'surveyCentres.centre', 'surveyCentres.articles','articles.article');
        $centreService = new CentresService();
        $notificationsService = new NotificationsService();
        $allWarehouseIds = $centreService->allWarehouses()->pluck('id')->toArray();
        $warehouseNotified = false;
        $notifiedCentres = [];
        foreach ($survey->surveyCentres as $surveyCentre) {
            if (in_array($surveyCentre->centre_id, $allWarehouseIds)) {
                if (!$warehouseNotified) {
                    $warehouseCentre3R00 = Centre::find(Centre::Warehouse_3R00);
                    $warehouseCentre3R00->notify(
                        new SurveyCreated($survey, $surveyCentre->centre, $surveyCentre->articles,true)
                    );
//                    Mail::to(config('emailAddresses.claims_logistics'))
//                        ->locale('sk')
//                        ->send(new SurveyCreatedMail($survey, $surveyCentre->articles, true));
                    $warehouseNotified = true;
                    $notifiedCentres[] = config('emailAddresses.claims_logistics');
                }
            } elseif (!$surveyCentre->centre->email) {
                request()->session()->flash(
                    'status-warning',
                    __('survey.listOfCentresIncludedCentresWithoutEmailAddress')
                );
            } else {
                $surveyCentre->centre->notify(
                    new SurveyCreated($survey, $surveyCentre->centre, $surveyCentre->articles)
                );
                $notifiedCentres[] = $surveyCentre->centre->code;
            }
        }
        request()->session()->flash('status-success', __('emails.centresWasNotified'));

        $survey->surveyCentres()->update(['status_id' => Status::SURVEY_ARTICLE_UNCHECKED]);

        Cache::forget('uncheckedSurveysCount');

        $surveyService->createSurveyHistoryLog($survey, 'updated', 'statusUpdated', $survey->status_id);
        $surveyService->createSurveyHistoryLog(
            $survey,
            'updated',
            'centresNotified',
            createStringFromIterable((object)$notifiedCentres, true)
        );

        $notificationsService->sendMails(SurveyCreatedMail::class , $survey, $survey->articles->pluck('article')->unique('goodsid'));

        $survey->fill([
            'status_id' => Status::SURVEY_IN_PROGRESS,
        ])->save();

        return redirect(route('surveys.summaries.show', $survey->id));
    }
}
