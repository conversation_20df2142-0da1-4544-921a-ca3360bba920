<?php

namespace App\Http\Controllers\Survey\SurveyForms\Parts;

use App\Http\Controllers\Controller;
use App\Http\Requests\Survey\SurveyDueByRequest;
use App\Models\Survey;
use App\Services\Survey\SurveyService;

class StoreDueByController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(SurveyDueByRequest $request, SurveyService $surveyService, Survey $survey)
    {
        $this->authorize('createOrUpdateDueBy', $survey);
        $surveyService->updateDueBy($survey, (object)$request->validated());
        $surveyService->createSurveyHistoryLog($survey, 'updated', 'dueByUpdated', $request->due_by);
        return redirect(route('surveys.imports.show', $survey->id))->with('status-success', __('survey.dueByUpdated'));
    }
}
