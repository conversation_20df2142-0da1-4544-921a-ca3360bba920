<?php

namespace App\Http\Controllers\Survey\SurveyForms\Parts;

use App\Http\Controllers\Controller;
use App\Http\Requests\Survey\StoreSurveyFileRequest;
use App\Imports\SurveyArticlesImport;
use App\Models\File;
use App\Models\Survey;
use App\Services\FileService;
use App\Services\Survey\SurveyService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Response;
use Maatwebsite\Excel\Facades\Excel;
use Pion\Laravel\ChunkUpload\Exceptions\UploadMissingFileException;
use Pion\Laravel\ChunkUpload\Receiver\FileReceiver;

class SurveyExcelImportController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param StoreSurveyFileRequest $request
     * @param FileService $fileService
     * @param FileReceiver $receiver
     * @param Survey $survey
     * @return Response
     * @throws UploadMissingFileException
     */
    public function store(
        StoreSurveyFileRequest $request,
        FileService $fileService,
        FileReceiver $receiver,
        SurveyService $surveyService,
        Survey $survey
    ): object {
        if (!auth()->user()->can('createOrUpdateSurveyForms', $survey)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect()->back();
        }

        if ($receiver->isUploaded() === false) {
            throw new UploadMissingFileException();
        }

        $save = $receiver->receive();
        if ($save->isFinished()) {
            Excel::import(new SurveyArticlesImport($survey), $save->getFile());
            $file = $fileService->uploadFile($save->getFile(), "/surveys/$survey->id");
            $importedFile = $survey->importedFile()->create($file);

            $surveyService->createSurveyHistoryLog(
                $survey,
                'updated',
                'newImportedFile',
                createStringFromIterable($importedFile->toArray())
            );

            $importedFile->load('creator');
            return $importedFile;
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param SurveyService $surveyService
     * @param Survey $survey
     * @param File $file
     * @return bool
     */
    public function destroy(SurveyService $surveyService, Survey $survey, File $file): bool
    {
        $this->authorize('deleteImportedFile', $survey);

        foreach ($survey->surveyCentres as $surveyCentre) {
            $surveyCentre->articles()->detach();
        }

        $survey->surveyCentres()->delete();

        $surveyService->createSurveyHistoryLog(
            $survey,
            'updated',
            'importedFileDeleted',
            createStringFromIterable((object)$file->toArray())
        );

        return $file->delete();
    }
}
