<?php

namespace App\Http\Controllers\Survey\SurveyForms\Parts;

use App\Http\Controllers\Controller;
use App\Http\Requests\Survey\SurveySummaryArticleStateUpdateRequest;
use App\Mail\Survey\AllSurveysAreFilledMail;
use App\Models\Article;
use App\Models\Centre;
use App\Models\Status;
use App\Models\Survey;
use App\Models\SurveyCentreArticle;
use App\Services\NotificationsService;
use App\Services\Survey\SurveyService;
use App\Traits\Responses;
use Illuminate\Http\Request;


class SurveySummaryArticleStateController extends Controller
{
    use Responses;

    /**
     * Display the specified resource.
     */
    public function show(Request $request, SurveyService $surveyService, Survey $survey, Article $article)
    {
        $this->authorize('isAccessible', $survey);

        $filter = ($request->filter) ?: ((session()->get('surveySummaryArticleDetailsFilter')) ? session()->get(
            'surveySummaryArticleDetailsFilter'
        ) : []);

        if ($filter) {
            session()->put('surveySummaryArticleDetailsFilter', $filter);
        }

        $articleCentres = $surveyService->getSurveyArticleDetails($survey, $article, $filter);


        $statuses = Status::whereIn('id', [
            Status::SURVEY_ARTICLE_UNCHECKED,
            Status::SURVEY_ARTICLE_CHECKED,
        ])->get();


        return view('surveys.parts.summariesArticle', [
            'survey' => $survey,
            'article' => $article,
            'centres' => Centre::all(),
            'statuses' => $statuses,
            'filter' => $filter,
            'articleCentres' => $articleCentres,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(
        SurveySummaryArticleStateUpdateRequest $request,
        SurveyService $surveyService,
        Survey $survey,
        Article $article,
    ) {
        $this->authorize('SurveySummaryArticleStateUpdate', $survey);
        $validatedRequest = (object)$request->validated();

        $surveyCentreArticle = SurveyCentreArticle::where([
            ['article_id', $article->id],
            ['survey_centre_id', $validatedRequest->survey_centre_id]
        ])->first();

        $surveyCentreArticle->fill([
            'damaged_count' => $validatedRequest->damaged_count,
            'articles_count' => $validatedRequest->articles_count,
            'status_id' => isset($validatedRequest->damaged_count) ? Status::SURVEY_ARTICLE_CHECKED : Status::SURVEY_ARTICLE_UNCHECKED,
        ])->save();

        $surveyService->createSurveyHistoryLog(
            $survey,
            'updated',
            'articleWasUpdated',
            createStringFromIterable(
                (object)$surveyCentreArticle->only(['survey_centre_id', 'article_id', 'articles_count', 'damaged_count']
                )
            )
        );

        return $this->response($surveyCentreArticle, __('survey.damagedArticlesCountWasUpdated'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(
        Request $request,
        SurveyService $surveyService,
        Survey $survey,
        Article $article,
    ) {
        $request->validate(['survey_centre_id' => 'required|exists:survey_centres,id']);

        $surveyArticles = $survey->articles()->get();
        $surveyCentreArticles = $surveyArticles->where('survey_centre_id', $request->survey_centre_id);
        $surveyArticle = $surveyCentreArticles->firstWhere('article_id', $article->id);

        if (empty($surveyArticle)) {
            return redirect()->back()->withErrors(__('general.unexpectedErrorPleaseContactAdministrator'));
        }

        $surveyArticle->delete();

        if (($surveyCentreArticles->count() - 1) == 0) {
            $surveyCentre = $survey->surveyCentres()
                ->with('centre:id,code')
                ->where('id', $request->survey_centre_id)
                ->first();

            session()->flash(
                'info',
                __(
                    'survey.centreWasRemovedFromSurveyBecauseWeDoNotRegisterAnyOtherArticlesUnderCentre',
                    ['centre' => $surveyCentre->centre->code]
                )
            );
            $surveyCentre->delete();
        }

        if (($surveyArticles->where('article_id', $article->id)->count() - 1) == 0) {
            return redirect(route('surveys.summaries.show', $survey->id))->with(
                'status-success',
                __('survey.articleWasDeleted')
            );
        } else {
            return redirect()->back()->with('status-success', __('survey.articleWasDeleted'));
        }
    }
}
