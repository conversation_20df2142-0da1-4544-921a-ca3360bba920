<?php

namespace App\Http\Controllers\Survey\SurveyForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\Survey\SurveyArticleRequest;
use App\Models\Article;
use App\Models\Survey;
use App\Services\Survey\SurveyNavigationService;
use App\Services\Survey\SurveyService;
use App\Services\VermontApiService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\View\View;

class SurveyArticleController extends Controller
{
    /**
     * Show the form for creating a new resource.
     *
     * @param Survey $survey
     * @return View|RedirectResponse
     * @throws AuthorizationException
     */
    public function create(Survey $survey): View|RedirectResponse
    {
        $this->authorize('isAccessible', $survey);
        $navigation = new SurveyNavigationService($survey);
        $formMethod = 'POST';
        $formActionUrl = route('surveys.articles.store', $survey->id);

        return view('surveys/surveysForms/articles', [
            'navigation' => $navigation,
            'survey' => $survey,
            'formActionUrl' => $formActionUrl,
            'formMethod' => $formMethod
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param SurveyArticleRequest $request
     * @param VermontApiService $vermontApiService
     * @param SurveyService $surveyService
     * @param Survey $survey
     * @return RedirectResponse
     */
    public function store(
        SurveyArticleRequest $request,
        VermontApiService $vermontApiService,
        SurveyService $surveyService,
        Survey $survey
    ): RedirectResponse {
        $navigation = new SurveyNavigationService($survey);

        if (!auth()->user()->can('createOrUpdateSurveyForms', $survey)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->articleRoute());
        }
        $apiArticles = $vermontApiService->getAllArticles('Goods_ID', "$request->goodsid%");
        if (empty($apiArticles)) {
            return redirect()->back()->with('status-danger', __('general.goodsIdNotFound'));
        }

        $surveyService->storeOrUpdateSurveyArticle($survey, $apiArticles);
        $surveyService->createSurveyHistoryLog($survey, 'updated', 'articleWasFilled', $request->goodsid);

        return redirect($navigation->nextRoute());
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param Survey $survey
     * @param Article $article
     * @return View
     * @throws AuthorizationException
     */
    public function edit(Survey $survey, Article $article): View
    {
        $this->authorize('isAccessible', $survey);
        $navigation = new SurveyNavigationService($survey);
        $formActionUrl = route('surveys.articles.update', ['survey' => $survey->id, 'article' => $article]);
        $formMethod = 'PUT';
        $articleOrderGroup = getOrderGroup('id', $article->order_group_id);


        return view('surveys/surveysForms/articles', [
            'navigation' => $navigation,
            'survey' => $survey,
            'article' => $article,
            'articleOrderGroup' => $articleOrderGroup,
            'formActionUrl' => $formActionUrl,
            'formMethod' => $formMethod

        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param SurveyArticleRequest $request
     * @param VermontApiService $vermontApiService
     * @param SurveyService $surveyService
     * @param Survey $survey
     * @return RedirectResponse
     */
    public function update(
        SurveyArticleRequest $request,
        VermontApiService $vermontApiService,
        SurveyService $surveyService,
        Survey $survey
    ): RedirectResponse {
        $navigation = new SurveyNavigationService($survey);
        $oldSurveyArticle = clone $survey->exampleArticle;

        if (!auth()->user()->can('createOrUpdateSurveyForms', $survey)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->articleRoute());
        }

        $apiArticle = $vermontApiService->getAllArticles('Goods_ID', $request->goodsid);
        if (!$apiArticle) {
            return redirect()->back()->with('status-danger', __('general.goodsIdNotFound'));
        }

        $survey = $surveyService->storeOrUpdateSurveyArticle($survey, $apiArticle);
        $exampleArticle = $survey->load('exampleArticle');

        if ($exampleArticle->id != $oldSurveyArticle->id) {
            $request->session()->flash('status-success', __('claims.messages.articleWasUpdated'));
            $surveyService->createSurveyHistoryLog($survey, 'updated', 'articleWasUpdated', $request->goodsid);
        }

        return redirect($navigation->nextRoute());
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return Response
     */
    public function destroy($id)
    {
        //
    }
}
