<?php

namespace App\Http\Controllers\Survey\SurveyForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\Survey\SurveyArticleDamageRequest;
use App\Models\DamagePlace;
use App\Models\DamageType;
use App\Models\Diagram;
use App\Models\Survey;
use App\Services\Survey\SurveyNavigationService;
use App\Services\Survey\SurveyService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;

class SurveyDamageController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @param Survey $survey
     * @return View
     */
    public function create(Survey $survey): View
    {
        $this->authorize('isAccessible', $survey);
        $navigation = new SurveyNavigationService($survey);
        $formActionUrl = route('surveys.damages.store', ['survey' => $survey->id,]);
        $formMethod = 'POST';
        $diagram = Diagram::where('id', $survey->diagram_id)->first() ?? null;

        $damageTypes = DamageType::byOrderGroup($survey->exampleArticle->order_group_id)
            ->subTypes()
            ->withOther()
            ->onlyActive()
            ->withParent()
            ->formatCollectionForSelectInput();


        //$damagePlaces = DamagePlace::ByOrderGroup($survey->exampleArticle->order_group_id)->get();

        return view('surveys/surveysForms/damages', [
            'navigation' => $navigation,
            'diagram' => $diagram,
            'survey' => $survey,
            'formActionUrl' => $formActionUrl,
            'formMethod' => $formMethod,
            'damageTypes' => $damageTypes,
            //'damagePlaces'=>$damagePlaces,
        ]);
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param SurveyArticleDamageRequest $request
     * @param SurveyService $surveyService
     * @param Survey $survey
     * @return RedirectResponse
     */
    public function store(
        SurveyArticleDamageRequest $request,
        SurveyService              $surveyService,
        Survey                     $survey
    ): RedirectResponse
    {
        $navigation = new SurveyNavigationService($survey);

        if (!auth()->user()->can('createOrUpdateSurveyForms', $survey)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->damageRoute());
        }

        $survey = $surveyService->updateArticleDamage($survey, (object)$request->validated());

        $surveyService->createSurveyHistoryLog(
            $survey,
            'updated',
            'damageWasFilled',
            createStringFromIterable(
                (object)[
                    'diagram_coordinates' => $survey->diagram_coordinates,
                    'damage_type_id' => $survey->damage_type_id,
                    'damage_subtype_id' => $survey->damage_subtype_id ?? null,
                    //'damage_place_id' => $survey->damage_place_id?? null,
                    'damage_description' => $survey->damage_description,

                ]
            )
        );

        return redirect($navigation->nextRoute());
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param Survey $survey
     * @return View
     * @throws AuthorizationException
     */
    public function edit(Survey $survey): View
    {
        $this->authorize('isAccessible', $survey);
        $navigation = new SurveyNavigationService($survey);
        $formActionUrl = route(
            'surveys.damages.update',
            ['survey' => $survey->id, 'article' => $survey->example_article_id]
        );
        $formMethod = 'PUT';
        $diagram = Diagram::where('id', $survey->diagram_id)->first() ?? null;


        if (auth()->user()->cannot('createOrUpdateSurveyArticleDamage', $survey)) {
            $survey->load('damageSubType.parent');
        } else {
            $damageTypes = DamageType::byOrderGroup($survey->exampleArticle->order_group_id)
                ->subTypes()
                ->withOther()
                ->onlyActive()
                ->withParent()
                ->formatCollectionForSelectInput();
        }


        //$damagePlaces = DamagePlace::ByOrderGroup($survey->exampleArticle->order_group_id)->get();

        return view('surveys/surveysForms/damages', [
            'navigation' => $navigation,
            'diagram' => $diagram,
            'survey' => $survey,
            'formActionUrl' => $formActionUrl,
            'formMethod' => $formMethod,
            'damageTypes' => $damageTypes ?? [],
            //'damagePlaces'=>$damagePlaces,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param SurveyArticleDamageRequest $request
     * @param SurveyService $surveyService
     * @param Survey $survey
     * @return RedirectResponse
     */
    public function update(
        SurveyArticleDamageRequest $request,
        SurveyService              $surveyService,
        Survey                     $survey
    ): RedirectResponse
    {
        $navigation = new SurveyNavigationService($survey);
        if (!auth()->user()->can('createOrUpdateSurveyArticleDamage', $survey)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return redirect($navigation->damageRoute());
        }

        $oldCoordinated = $survey->diagram_coordinates;
        $oldDescription = $survey->damage_description;
        $survey = $surveyService->updateArticleDamage($survey, (object)$request->validated());

        if ($oldCoordinated != json_encode(
                $survey->diagram_coordinates
            ) || $oldDescription != $survey->damage_description) {
            $request->session()->flash('status-success', __('logs.damageFormUpdated'));

            $surveyService->createSurveyHistoryLog(
                $survey,
                'updated',
                'damageWasUpdated',
                createStringFromIterable(
                    (object)[
                        'diagram_coordinates' => $survey->diagram_coordinates,
                        'damage_type_id' => $survey->damage_type_id,
                        'damage_subtype_id' => $survey->damage_subtype_id ?? null,
                        //   'damage_place_id' => $survey->damage_place_id ?? null,
                        'damage_description' => $survey->damage_description,

                    ]
                )
            );
        }

        return redirect($navigation->nextRoute());
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return Response
     */
    public function destroy($id)
    {
        //
    }
}
