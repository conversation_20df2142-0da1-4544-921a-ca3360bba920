<?php

namespace App\Http\Controllers\Survey\SurveyForms;

use App\Http\Controllers\Controller;
use App\Models\Survey;
use App\Services\Survey\SurveyNavigationService;

class SurveyImportController extends Controller
{

    /**
     * Handle the incoming request.
     */
    public function __invoke(Survey $survey)
    {
        $this->authorize('isAccessible', $survey);
        $survey->load('importedFile.creator');
        $navigation = new SurveyNavigationService($survey);
        $file = $survey->importedFile;

        return view('surveys.surveysForms.imports', [
            'navigation' => $navigation,
            'file' => $file,
            'survey' => $survey,
        ]);
    }
}
