<?php

namespace App\Http\Controllers\Survey\SurveyForms;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\StorePhotoRequest;
use App\Models\Photo;
use App\Models\Survey;
use App\Services\FileService;
use App\Services\Survey\SurveyNavigationService;
use App\Services\Survey\SurveyService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Pion\Laravel\ChunkUpload\Exceptions\UploadMissingFileException;
use Pion\Laravel\ChunkUpload\Receiver\FileReceiver;

class SurveyPhotosController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Survey $survey): View
    {
        $this->authorize('isAccessible', $survey);
        $navigation = new SurveyNavigationService($survey);
        $photos = $survey->photos->toArray();

        return view('surveys.surveysForms.photos', [
            'navigation' => $navigation,
            'photos' => array_values($photos),
            'survey' => $survey,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StorePhotoRequest $request
     * @param FileService $fileService
     * @param FileReceiver $receiver
     * @param SurveyService $surveyService
     * @param Survey $survey
     * @return Photo|Model|JsonResponse|void|null
     * @throws UploadMissingFileException
     */
    public function store(
        StorePhotoRequest $request,
        FileService $fileService,
        FileReceiver $receiver,
        SurveyService $surveyService,
        Survey $survey
    ) {
        if (!auth()->user()->can('createOrUpdateSurveyForms', $survey)) {
            return request()->session()->flash('status-danger', __('general.unauthorized'));
        }

        if ($receiver->isUploaded() === false) {
            throw new UploadMissingFileException();
        }

        $save = $receiver->receive();
        if ($save->isFinished()) {


            $file = $save->getFile();

            //Size validation
            if ($fileService->validateFileSize($file)) {
                unlink($save->getFile()->getPathname());

                return $this->baseErrorJsonResponse(__('photos.file_size_exceeded', ['size' => config('media.mb_size_limit_for_video')]));
            }

            //file validation
            if ($fileService->validateFileType($file->getClientOriginalExtension())) {
                unlink($save->getFile()->getPathname());
                Log::info('Unsuported file type uploaded: ' . $file->getClientOriginalExtension());

                return $this->baseErrorJsonResponse(__('photos.unsupported_file_type', ['ext' => $file->getClientOriginalExtension()]));
            }

            $file =$fileService->storeUploadedFile($file, "/surveys/$survey->id");


            $file = $survey->photos()->create([

                'name' => $file->name,
                'file_path' => $file->file_path,
                'thumbnail_path' => $file->thumbnail_path ?? null,
                'preview_path' => $file->preview_pat ?? null,
                'size' => $file->size,
                'extension' => $file->extension,
            ]);

            $surveyService->createSurveyHistoryLog(
                $survey,
                'updated',
                'fileUploaded',
                json_encode($file->toArray()),
            );
            return $file;
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param Survey $survey
     * @return View
     * @throws AuthorizationException
     */
    public function show(Survey $survey): View
    {
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param FileService $fileService
     * @param Survey $survey
     * @param Photo $photo
     * @return bool
     * @throws AuthorizationException
     */
    public function destroy(FileService $fileService,SurveyService $surveyService, Survey $survey, Photo $photo): bool
    {
        $this->authorize('createOrUpdateSurveyForms', $survey);

        $surveyService->createSurveyHistoryLog(
            $survey,
            'updated',
            'fileDeleted',
            json_encode($photo->toArray()),
        );

        $fileService->deletePhoto($photo->thumbnail_path);

        if (isset($photo->thumbnail_path)) {
            $fileService->deletePhoto($photo->thumbnail_path);
        }

        if (isset($photo->preview_path)) {
            $fileService->deletePhoto($photo->preview_path);
        }


        return $photo->delete();
    }
}
