<?php

namespace App\Http\Controllers\Survey\SurveyForms;

use App\Enums\Decisions\SupplierClaimDecisionsEnum;
use App\Http\Controllers\Controller;
use App\Jobs\SupplierClaims\CreateSupplierClaimsAfterConfirmedSurvey;
use App\Jobs\Survey\AddArticlesToSupplierClaimsList;
use App\Jobs\Survey\MarkSupplierClaimCheck;
use App\Mail\Survey\SurveyResultMail;
use App\Models\Decision;
use App\Models\Status;
use App\Models\Survey;
use App\Services\NotificationsService;
use App\Services\Survey\SurveyNavigationService;
use App\Services\Survey\SurveyService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Enum;
use Illuminate\View\View;


class SurveySummaryController extends Controller
{

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @param SurveyService $surveyService
     * @param Survey $survey
     * @return RedirectResponse
     */
    public function store(
        Request              $request,
        SurveyService        $surveyService,
        NotificationsService $notificationsService,
        Survey               $survey
    )
    {
        if (!auth()->user()->can('closeSurvey', $survey)) {
            request()->session()->flash('status-danger', __('general.unauthorized'));
            return back();
        }
        $validated = $request->validate(['decision' => ['required', new Enum(SupplierClaimDecisionsEnum::class)]]);
        $survey = $surveyService->closeSurvey($survey, (int)$validated['decision']);
        $surveyService->createSurveyHistoryLog(
            $survey,
            'updated',
            'surveyWasClosed',
            $validated['decision']
        );

        if ($survey->decision != SupplierClaimDecisionsEnum::REJECT->value) {
            Bus::chain([
                new AddArticlesToSupplierClaimsList($survey, auth()->id()),
                new MarkSupplierClaimCheck($survey),
                new CreateSupplierClaimsAfterConfirmedSurvey($survey, auth()->user()),
            ])->dispatch();
        }

        $notificationsService->sendMails(SurveyResultMail::class, $survey);

        return redirect(route('surveys.index'))->with('status-success', __('survey.surveyWasClosed'));
    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @param SurveyService $surveyService
     * @param Survey $survey
     * @return View|RedirectResponse
     */
    public function show(Request $request, SurveyService $surveyService, Survey $survey)
    {
        if ($request->user()->cannot('viewSummaryForm', $survey)) {
            $navigation = new SurveyNavigationService($survey);
            return redirect($navigation->showSurvey())->withErrors(__('survey.pleaseCompleteAllSurveyFormsBeforeProceeding'));
        }

        $this->authorize('viewSummaryForm', $survey);

        $survey->load('creator:id,name', 'status:id,name');
        $surveyDecisions = SupplierClaimDecisionsEnum::cases();
        $checkedArticleStatusId = Status::SURVEY_ARTICLE_CHECKED;
        $emailAddresses = config('emailAddresses');
        $departmentsForNotifications = [
            $emailAddresses['retail_management'],
            $emailAddresses['marketing'],
            $emailAddresses['itd-goods']
        ];

        $surveyArticlesWithStatistics = DB::table('survey_centres')
            ->select(
                DB::raw(
                    "sum(survey_centre_article.articles_count) AS articles_count,
                sum(case when survey_centre_article.damaged_count > 0 then survey_centre_article.damaged_count else 0 end) AS damaged_articles_count,
                sum(case when survey_centre_article.status_id = $checkedArticleStatusId then survey_centre_article.articles_count  else 0 end) AS checked_articles_count
              "
                ),
                'articles.id',
                'articles.goodsid',

            )
            ->where('survey_id', $survey->id)
            ->join('survey_centre_article', 'survey_centres.id', '=', 'survey_centre_article.survey_centre_id')
            ->join('articles', 'articles.id', '=', 'survey_centre_article.article_id')
            ->groupBy('articles.id', 'articles.goodsid')
            ->get();


        $navigation = new SurveyNavigationService($survey);

        return view('surveys/surveysForms/summaries', [
            'navigation' => $navigation,
            'survey' => $survey,
            'surveyDecisions' => $surveyDecisions,
            'surveyArticlesWithStatistics' => $surveyArticlesWithStatistics,
            'departmentsForNotifications' => $departmentsForNotifications
        ]);
    }

}
