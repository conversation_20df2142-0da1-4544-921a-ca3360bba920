<?php

namespace App\Http\Controllers;

use App\Models\Survey;
use App\Models\SurveyHistoryLog;
use App\Services\Survey\SurveyNavigationService;
use Illuminate\Http\Request;

class SurveyLogsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Survey $survey)
    {
        $this->authorize('supplier claims administration');
        $navigation = new SurveyNavigationService($survey);
        $logs = SurveyHistoryLog::where('survey_id', $survey->id)
            ->with('actionUser:id,name')
            ->oldest()
            ->paginate(20);

        return view('surveys.SurveyLogs', [
            'survey' => $survey,
            'logs' => $logs,
            'redirectUrl' => $navigation->showSurvey()
        ]);
    }
}
