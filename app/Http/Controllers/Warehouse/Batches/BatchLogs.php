<?php

namespace App\Http\Controllers\Warehouse\Batches;

use App\Http\Controllers\Controller;
use App\Models\Batch;
use App\Models\ClaimsTransport;
use Illuminate\Http\Request;

class BatchLogs extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Batch $batch)
    {

        return view('warehouse.batches.logs', [
            'batch' => $batch,
            'logs' => $batch->logs()->with('creator')->paginate(20),
        ]);
    }
}
