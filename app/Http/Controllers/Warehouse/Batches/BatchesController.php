<?php

namespace App\Http\Controllers\Warehouse\Batches;

use App\Enums\Warehouse\BatchLogActionsEnum;
use App\Enums\Warehouse\BatchStatusesEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\StoreBatchRequest;
use App\Models\Batch;
use App\Models\Trademark;
use App\Models\User;
use App\Models\WarehouseBatchType;
use App\Services\Logs\LogsService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;

class BatchesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return View
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function index(Request $request): View
    {
        $this->authorize('viewWarehouse', User::class);
        $trademarks = Trademark::all();
        $batchTypes = WarehouseBatchType::all();
        $statuses = BatchStatusesEnum::translatedValues();

        $filter = ($request->filter) ?: ((session()->get('warehouseBatchesFilter')) ? session()->get(
            'warehouseBatchesFilter'
        ) : []);
        if ($filter) {
            session()->put('warehouseBatchesFilter', $filter);
        }

        $query = Batch::query();
        $batches = filterRequest($query, $filter)
            ->withCount('claims')
            ->with('creator', 'trademark', 'batchType')
            ->latest()
            ->paginate(25);
        $creators = $batches->pluck('creator')->unique();

        return view('warehouse.batches.index', [
            'filter' => $filter,
            'creators' => $creators,
            'batchTypes' => $batchTypes,
            'trademarks' => $trademarks,
            'batches' => $batches,
            'statuses' => $statuses,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreBatchRequest $request
     * @return RedirectResponse
     */
    public function store(StoreBatchRequest $request): RedirectResponse
    {
        $this->authorize('create batches', User::class);

        $validated = (object)$request->validated();

        $batch = Batch::create([
            'trademark_id' => $validated->trademark_id ?? null,
            'warehouse_batch_type_id' => $validated->warehouse_batch_type_id,
            'creator_id' => auth()->id(),
        ]);

        $logsService = new LogsService();
        $changes = $logsService->getModelChanges($batch);

        $batch->logs()->create([
            'action' => BatchLogActionsEnum::CREATED->value,
            'creator_id' => auth()->id(),
            'changes' => json_encode($changes),
        ]);

        return redirect(route('warehouse.batches.index'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return View
     * @throws AuthorizationException
     */
    public function create(): View
    {
        $this->authorize('createBatches', User::class);

        return view('warehouse.batches.create', [
            'trademarks' => Trademark::all(),
            'types' => WarehouseBatchType::all(),
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param Batch $batch
     * @return View
     * @throws AuthorizationException
     */
    public function show(Batch $batch): View
    {
        $this->authorize('viewWarehouse', User::class);

        $batch->loadCount('claims');

        $claims = $batch->claims()
            ->with('warehouseStatus', 'centre', 'claimArticle.article', 'lastWarehouseReceipt')
            ->paginate(20);

        return view('warehouse.batches.show', [
            'batch' => $batch,
            'claims' => $claims
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
