<?php

namespace App\Http\Controllers\Warehouse\Batches;

use App\Http\Controllers\Controller;
use App\Models\Batch;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\View\View;

class PrintBatchLabelController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param Batch $batch
     * @return View
     */
    public function __invoke(Request $request, Batch $batch): View
    {
        $this->authorize('view warehouse', User::class);
        App::setLocale('sk');

        $backUrl = $request?->from == 'show'
            ? route('warehouse.batches.show', ['batch' => $batch->id])
            : route('warehouse.batches.index');

        
        return view('warehouse.batches.printLabel', [
            'batch' => $batch,
            'backUrl' => $backUrl
        ]);
    }
}
