<?php

namespace App\Http\Controllers\Warehouse\Batches;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class RemoveBatchesFilterController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        session()->remove('warehouseBatchesFilter');

        return redirect(route('warehouse.batches.index'));
    }
}
