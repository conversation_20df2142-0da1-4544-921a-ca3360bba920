<?php

namespace App\Http\Controllers\Warehouse\Batches;

use App\Enums\Logs\LogActionTypes;
use App\Enums\Warehouse\BatchLogActionsEnum;
use App\Enums\Warehouse\BatchStatusesEnum;
use App\Http\Controllers\Controller;
use App\Models\Batch;
use App\Models\User;
use App\Services\Logs\LogsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\View\View;

class SendBatchToCleanersController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param Batch $batch
     */
    public function __invoke(Request $request, LogsService $logsService, Batch $batch)
    {
        $this->authorize('sendToCleaners', $batch);

        $batch->fill(['status' => BatchStatusesEnum::SENT_TO_CLEANERS->value]);

        $changes = $logsService->getModelChanges($batch);

        $claimsIds = $batch->claims->pluck('id')->toArray();

        $changes['claims_sent_to_cleaners'] = $claimsIds;

        $batch->claimsSentToCleaners()->attach($claimsIds);

        $batch->logs()->create([
            'action' => BatchLogActionsEnum::SENT_TO_CLEANERS->value,
            'creator_id' => auth()->id(),
            'changes' => json_encode($changes),
        ]);

        $batch->save();

        return redirect(route('warehouse.batches.show', ['batch' => $batch->id]))
            ->with('status-success', __('batches.batchWasSentToCleaners'));
    }
}
