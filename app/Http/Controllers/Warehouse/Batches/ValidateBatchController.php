<?php

namespace App\Http\Controllers\Warehouse\Batches;

use App\Enums\Warehouse\BatchStatusesEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\ClaimReceivingRequest;
use App\Http\Requests\Warehouse\ValidateBatchRequest;
use App\Models\Batch;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\User;
use App\Models\WarehouseBatchType;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Validation\Rule;
use Illuminate\View\View;

class ValidateBatchController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param ValidateBatchRequest $request
     * @return JsonResponse
     */
    public function __invoke(ValidateBatchRequest $request): JsonResponse
    {
        $requireConfirmation = false;
        $messages = [];

        $claim = Claim::find($request->claim_id);

        if ($claim->batch_id) {
            $claimBatch = Batch::find($claim->batch_id);

            if ($claimBatch->status === BatchStatusesEnum::SENT_TO_CLEANERS->value) {
                $requireConfirmation = true;
                $messages[] = __('batches.claimAlreadySentToCleaners');
            }

        }

        if ($request->batch_id) {
            $batch = Batch::find($request->batch_id);

            if ($batch->warehouse_batch_type_id === WarehouseBatchType::TYPE_CLEANERS_ID) {
                $requireConfirmation = in_array($batch->status, [BatchStatusesEnum::SENT_TO_CLEANERS->value, BatchStatusesEnum::RECEIVED_FROM_CLEANERS->value]);
                $messages[] = __('batches.batchAlreadySentToCleaners');
            }
        }


        return $this->baseJsonResponse([
            'requireConfirmation' => $requireConfirmation,
            'messages' => $messages
        ]);


    }
}
