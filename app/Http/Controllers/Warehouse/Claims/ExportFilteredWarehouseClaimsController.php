<?php

namespace App\Http\Controllers\Warehouse\Claims;

use App\Exports\WarehouseClaimsExport;
use App\Http\Controllers\Controller;
use App\Services\CentresService;
use App\Services\Claims\ClaimsService;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class ExportFilteredWarehouseClaimsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(
        Request        $request,
        CentresService $centresService,
        ClaimsService  $claimsService
    )
    {
        $user = auth()->user();
        $this->authorize('view warehouse', $user);
        $date = date("Y.m.d");
        $claimsCount = $claimsService->filteredWarehouseClaims(session()->get('warehouseClaimsFilter'))->count();
        $limit = 10000;
        if ($claimsCount > $limit) {
            session()->flash('status-danger', __('general.export_limit_exceeded', ['limit' => $limit, 'count' => $claimsCount]));

            return response([
                'success' => false,
                'message' => 'Limit for export exceeded'
            ], 422);
        }

        unset($claims);
        $filter = session()->get('warehouseClaimsFilter') ?? [];
        return Excel::download(new WarehouseClaimsExport($filter), "warehouse-claims-$date.xlsx");
    }
}
