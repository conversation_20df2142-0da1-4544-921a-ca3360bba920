<?php

namespace App\Http\Controllers\Warehouse\Claims;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use Illuminate\Http\Request;

class RemoveSupplierClaimCheck extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Claim $claim)
    {
        $claim->load('claimArticle');
        $claim->update(['is_required_supplier_claim_check' => 0]);
        $claim->createHistoryLog(
            'supplierClaimCheck',
            'articleWasChecked'
        );
        return back();
    }
}
