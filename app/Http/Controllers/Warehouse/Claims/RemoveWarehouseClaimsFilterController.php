<?php

namespace App\Http\Controllers\Warehouse\Claims;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class RemoveWarehouseClaimsFilterController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        session()->remove('warehouseClaimsFilter');

        return redirect(route('warehouse.claims.index'));
    }
}
