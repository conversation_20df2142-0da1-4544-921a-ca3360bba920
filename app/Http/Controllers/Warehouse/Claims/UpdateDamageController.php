<?php

namespace App\Http\Controllers\Warehouse\Claims;

use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\UpdateClaimDamageRequest;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\DamageType;
use App\Services\GoogleTranslateApiService;
use App\Traits\Responses;
use Illuminate\Http\Request;

class UpdateDamageController extends Controller
{
    use Responses;

    /**
     * Handle the incoming request.
     */
    public function __invoke(UpdateClaimDamageRequest $request, Claim $claim): object
    {
        $this->authorize('editWarehouseClaims', $claim);

        $claimArticleBeforeUpdate = clone $claim->claimArticle;

        $googleTranslateApi = new GoogleTranslateApiService();

        if ($claim->centre->country_id != Centre::SK_CENTRE_COUNTRY_ID && isset($request->damage_description)) {
            $translatedDamageDescriptionSk = $googleTranslateApi->translate($request->damage_description, 'sk');
            $translatedDamageDescriptionEn = $googleTranslateApi->translate($request->damage_description);
        }

        $damageType = DamageType::find($request->damage_type_id);


        if (!isset($damageType->parent_id)) {
            $mainTypeId = $damageType->id;
            $subTypeId = null;
        } else {
            $mainTypeId = $damageType->parent_id;
            $subTypeId = $damageType->id;
        }

        $claim->claimArticle->fill([
            'damage_type_id' => $mainTypeId,
            'damage_subtype_id' => $subTypeId,
            'damage_description' => $request->damage_description,
            'damage_description_eng' => $translatedDamageDescriptionEn ?? null,
            'damage_description_sk' => $translatedDamageDescriptionSk ?? null,
        ])->save();

        $claim->claimArticle->load('damageType', 'damageSubType');


        if (!isSame(
            $claimArticleBeforeUpdate->only('damage_description', 'damage_type_id', 'damage_subtype_id'),
            $claim->claimArticle->only('damage_description', 'damage_type_id', 'damage_subtype_id')
        )) {
            $claim->createHistoryLog('updated', 'damageWasUpdated', $claim->claimArticle->toArray());
        }

        return $this->response($claim->claimArticle, __('damage.damageDescriptionUpdated'));
    }
}
