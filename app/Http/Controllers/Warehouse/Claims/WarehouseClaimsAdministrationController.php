<?php

namespace App\Http\Controllers\Warehouse\Claims;

use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\WarehouseClaimsAdministrationRequest;
use App\Models\Article;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\User;
use App\Services\CentresService;
use App\Services\StatusesService;
use App\Services\VermontApiService;
use App\Services\Warehouse\BatchesService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;


class WarehouseClaimsAdministrationController extends Controller
{
    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @param StatusesService $statusesService
     * @param Claim $claim
     * @return View
     */
    public function show(
        Request         $request,
        StatusesService $statusesService,
        CentresService  $centresService,
        Claim           $claim
    )
    {
        $this->authorize('warehouse administration', User::class);
        $claim->load(
            'claimArticle',
            'claimArticle.article',
        );

        $statuses = $statusesService->allWarehouseStatuses();
        $centres = $centresService->allWarehouses()->except(Centre::Warehouse_3S05);

        return view('warehouse.claims.administration', [
            'claim' => $claim,
            'statuses' => $statuses,
            'centres' => $centres
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param WarehouseClaimsAdministrationRequest $request
     * @param VermontApiService $vermontApiService
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function update(
        WarehouseClaimsAdministrationRequest $request,
        VermontApiService                    $vermontApiService,
        Claim                                $claim
    )
    {
        $this->authorize('warehouse administration', User::class);

        //TODO: administracia vyzaduje rozsirit a refaktorovat

        if (!$claim->is_supplier_claim && isset($request->goodsid)) {

            $apiArticle = $vermontApiService->getFormattedArticle('Goods_ID', $request->goodsid);
            if (!$apiArticle) {
                return redirect()->back()->with('status-danger', __('general.goodsIdNotFound'));
            }

            if ($claim->batch_id !== $request->batch_id) {
                (new BatchesService())->saveBatchChanges($claim, $request->batch_id);
            }

            $ArticleOrderGroup = getOrderGroup('name', $apiArticle->order_group);
            $article = Article::firstOrCreate(['goodsid' => $apiArticle->goodsid], [
                'goodsid' => $apiArticle->goodsid,
                'order_group_id' => $ArticleOrderGroup?->id,
                'size' => $apiArticle?->size,
                'color' => $apiArticle?->color,
                'season' => $apiArticle?->season,
                'name' => $apiArticle?->name,
                'code' => $apiArticle?->code,
                'ean' => $apiArticle?->ean,
                'supplier' => $apiArticle?->supplier,
                'supplier_name' => $apiArticle?->supplier_name,
                'producer' => $apiArticle?->producer,
                'producer_name' => $apiArticle?->producer_name,
                'nip' => $apiArticle?->nip,
                'nip_currency' => $apiArticle?->nip_currency,
                'trademark' => $apiArticle?->trademark,
            ]);

            $claim->claimArticle->fill(['article_id' => $article->id])->save();
        }
        $claim->fill([
            'is_active' => (int)$request->is_active,
            'warehouse_status_id' => $request->warehouse_status_id,
            'warehouse_centre_id' => $request->warehouse_centre_id,
            'batch_id' => $request->batch_id ?? null,
        ])->save();

        $log = [
            'is_active' => $claim->is_active,
            'warehouse_status_id' => $claim->warehouse_status_id,
            'warehouse_centre_id' => $claim->warehouse_centre_id,
            'batch_id' => $claim->batch_id,
            'goodsid' => $article->goodsid ?? null,
        ];

        $claim->createHistoryLog(
            'administration',
            'claimUpdated',
            $log

        );

        return redirect(route('warehouse.claims.administration.show', $claim->id))->with(
            'status-success',
            __('administration.claims.claimWasUpdated')
        );
    }
}
