<?php

namespace App\Http\Controllers\Warehouse\Claims;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClaimForms\StoreClaimRequest;
use App\Http\Requests\Warehouse\WarehouseClaimRestockRequest;
use App\Jobs\Eso\ClaimRestockProcessJob;
use App\Models\Article;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\DamagePlace;
use App\Models\DamageType;
use App\Models\Status;
use App\Models\SupplierClaimArticle;
use App\Models\User;
use App\Models\WarehouseDocumentType;
use App\Services\CentresService;
use App\Services\Claims\ArticlesService;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\Claims\ClaimsService;
use App\Services\EsoApi\ClaimsWarehouseDocumentsService;
use App\Services\EsoApi\WarehouseDocumentsService;
use App\Services\StatusesService;
use App\Traits\Responses;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Gate;
use Illuminate\View\View;


class WarehouseClaimsController extends Controller
{
    use Responses;


    /**
     * Display a listing of the resource.
     *
     */
    public function index(
        Request         $request,
        ClaimType       $claimType,
        ArticlesService $articlesService,
        ClaimsService   $claimsService,
        StatusesService $statusesService,
        CentresService  $centresService
    )
    {
        $this->authorize('viewWarehouse', User::class);

        $statuses = $statusesService->allWarehouseStatuses();
        $userCentres = $centresService->loggedUserCentres();
        $trademarks = $articlesService->getAllArticlesTrademarks();
        $suppliers = $articlesService->getAllArticlesSuppliers();
        $claimTypes = $claimType->getClaimTypesForWarehouse();
        $allClaimTypes = ClaimType::whereNotIn('id', [ClaimType::CLAIM_TYPE_CUSTOMER_ESHOP, ClaimType::CLAIM_TYPE_WHOLESALE_PARTNER])->get();
        $activeWarehouses = $centresService->allWarehouses()->except(Centre::Warehouse_3S05);
        $centres = $centresService->allCentres();

        $filter = ($request->filter) ?: ((session()->get('warehouseClaimsFilter')) ? session()->get(
            'warehouseClaimsFilter'
        ) : []);
        if ($filter) {
            session()->put('warehouseClaimsFilter', $filter);
        }

        $claims = $claimsService->filteredWarehouseClaims($filter)->paginate(20);

        return view('warehouse.claims.index', [
            'filter' => $filter,
            "userCentres" => $userCentres,
            'claims' => $claims,
            'claimTypes' => $claimTypes,
            'activeWarehouses' => $activeWarehouses,
            'trademarks' => $trademarks,
            'statuses' => $statuses,
            'suppliers' => $suppliers,
            'centres' => $centres,
            'allClaimTypes' => $allClaimTypes,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return View
     */
    public function create(): View
    {
        $this->authorize('create warehouse claims');
        $claimType = new ClaimType();
        $claimTypes = $claimType->getClaimTypesForWarehouse();
        $userCentre = Centre::where('id', Centre::Warehouse_3R00)->get();
        $Centres = null;


        return view('warehouse.claims.createClaim', [
            'centres' => $Centres,
            'userCentre' => $userCentre,
            'claimTypes' => $claimTypes
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreClaimRequest $request
     * @param ClaimsService $claimsService
     * @return RedirectResponse
     */
    public function store(
        StoreClaimRequest $request,
        ClaimsService     $claimsService,
    ): RedirectResponse
    {
        Gate::authorize('create-claim', $request->input('claimTypeId'));

        //docasne riesenie pretoze IP adresa detekuje centrum 3s20 vapenka
        $centre = Centre::find(Centre::Warehouse_3R00);
        $claim = $claimsService->createClaim($centre, $request->input('claimTypeId'), auth()->user());
        $navigation = new ClaimsNavigationService($claim);
        return redirect($navigation->firstRoute());
    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @param ClaimsService $claimsService
     * @param int $claim
     * @return View
     */
    public function show(
        Request       $request,
        ClaimsService $claimsService,
        int           $claim
    )
    {
        $this->authorize('viewWarehouse', User::class);
        $claim = Claim::whereNotNull('warehouse_received_at')->where('id', $claim)->firstOrFail();


//TODO::Rozdelit na dva controllery
        if ($claim->is_supplier_claim) {
            $claim->load(
                'supplierClaimArticles',
                'activeWarehouseExpense',
                'supplierClaimArticle.photos',
                'activeWarehouseReceipt.predecessor',
            );
            return view('warehouse.claims.showSupplierClaim', [
                'redirectBack' => route("warehouse.{$request->segment(2)}.index"),
                'claim' => $claim,
                'photos' => $claim->supplierClaimArticle->photos->toArray(),
                'availableWarehouses' => $claimsService->getAvailableWarehouses($claim),
                'upload' => $claim->warehouse_status_id != Status::CLAIM_STATUS_CLOSED
            ]);
        } else {
            $damageTypes = DamageType::byOrderGroup($claim->claimArticle->article->order_group_id)
                ->subTypes()
                ->withOther()
                ->onlyActive()
                ->withParent()
                ->formatCollectionForSelectInput();


            if ($claim->is_required_supplier_claim_check) {
                $supplierClaimArticle = SupplierClaimArticle::where(
                    'article_code',
                    sliceGoodsId($claim->claimArticle->article->goodsid, 0, 4)
                )->first();
            }

            if ($claim->warehouse_status_id == Status::WAREHOUSE_STATUS_SEND_TO_CRAZY_DAYS) {
                $cdsArticle = Article::where('goodsid', 'CD_S-0-OT-0-0')->first();
            }

            $claim->load(
                'claimArticle',
                'claimArticle.article',
                'claimArticle.diagram',
                'activeWarehouseExpense',
                'activeWarehouseReceipt.predecessor',
                'claimArticle.damageType',
                'claimArticle.damageSubType',
            );
            return view('warehouse.claims.show', [
                'redirectBack' => route("warehouse.{$request->segment(2)}.index"),
                'claim' => $claim,
                'supplierClaimArticle' => $supplierClaimArticle ?? null,
                'photos' => $claim->photos->toArray(),
                'availableWarehouses' => $claimsService->getAvailableWarehouses($claim),
                'upload' => $claim->warehouse_status_id != Status::CLAIM_STATUS_CLOSED,
                'cdsArticle' => $cdsArticle ?? null,
                'damageTypes' => $damageTypes,
                'activeClaimRestock' => $claim->claimsRestock()->where('status_id', Status::CLAIMS_RESTOCKING_CREATED)->exists()


            ]);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param WarehouseClaimRestockRequest $request
     * @param ClaimsService $claimsService
     * @param Claim $claim
     * @return RedirectResponse
     */
    public function update(
        WarehouseClaimRestockRequest $request,
        ClaimsService                $claimsService,
        Claim                        $claim
    )
    {
        $this->authorize('restockWarehouseClaim', $claim);
        $warehouseDocumentsService = new WarehouseDocumentsService($claim);
        $claimsWarehouseDocumentsService = new ClaimsWarehouseDocumentsService($claim);
        $selectedWarehouse = Centre::find($request->warehouse_id);
        $user = auth()->user();

        $warehouseDocumentType = $selectedWarehouse->id == Centre::Warehouse_3S20
            ? WarehouseDocumentType::WAREHOUSE_EXPENSE_3S20
            : WarehouseDocumentType::WAREHOUSE_EXPENSE;

        $claimsLastDestinationWarehouses = [
            Centre::Warehouse_3S20,
            Centre::Warehouse_3S11_CRAZY_DAYS,
            Centre::Warehouse_3P99_CRAZY_DAYS,
            Centre::Warehouse_2P99_CRAZY_DAYS,
        ];

        $closeClaim = in_array($selectedWarehouse->id, $claimsLastDestinationWarehouses);
        $batchId = $request->batch_id;

        if ($selectedWarehouse->id != $claim->warehouse_centre_id) {
            if ($claim->is_supplier_claim) {

                $supplierClaimArticles = $claim->supplierClaimArticles(true)->get();
                $articles = [];

                foreach ($supplierClaimArticles as $supplierClaimArticle) {
                    $formattedArticle = $warehouseDocumentsService->formatArticleForDispatch(
                        $supplierClaimArticle->goodsid,
                        $supplierClaimArticle->pivot->quantity
                    );
                    $articles = array_merge($articles, $formattedArticle);
                }

            } else {
                $articles = $warehouseDocumentsService->formatArticleForDispatch(
                    $claim->claimArticle->article->goodsid,
                    $claim->claimArticle->quantity
                );
            }


            $additionalData = $claimsWarehouseDocumentsService->is_wms_return($claim->claim_code)
                ? $claimsWarehouseDocumentsService->formatAdditionalDataStringForWmsReturns($claim->claim_code)
                : $claim->claim_code;


            try {
                $expense = $warehouseDocumentsService->createExpense(
                    $articles,
                    $claim->warehouseCentre,
                    $selectedWarehouse,
                    $user,
                    $additionalData,
                    $warehouseDocumentType
                );

                $warehouseDocumentsService->increaseHeaderState($expense);
                $warehouseDocumentsService->createWarehouseReceipt($expense, $selectedWarehouse, $user);
            } catch (\Throwable $e) {

                if ($e->getCode() == 408) {
                    Bus::chain([
                        new ClaimRestockProcessJob(
                            claim: $claim,
                            additionalDataForEso: $additionalData,
                            actionUser: $user,
                            destinationWarehouse: $selectedWarehouse,
                        ),
                        function () use ($claimsService, $claim, $selectedWarehouse, $user, $batchId, $closeClaim) {
                            $claimsService->restockClaim(
                                claim: $claim,
                                selectedWarehouse: $selectedWarehouse,
                                actionUserId: $user->id,
                                batchId: $batchId,
                                closeClaim: $closeClaim
                            );
                        }
                    ])->dispatch();

                    return redirect()->back();
                } else {
                    return redirect()->back()->withErrors(json_decode($e->getMessage()));
                }
            }
        }

        $claimsService->restockClaim(
            claim: $claim,
            selectedWarehouse: $selectedWarehouse,
            actionUserId: $user->id,
            batchId: $batchId,
            closeClaim: $closeClaim
        );

        return redirect(route('warehouse.claims.show', $claim->id))
            ->with('status-success', __('receiving.sentTo', ['warehouse' => $selectedWarehouse->code]));
    }
}
