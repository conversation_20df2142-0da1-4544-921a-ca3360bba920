<?php

namespace App\Http\Controllers\Warehouse\Claims;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Services\Claims\ClaimsNavigationService;
use App\Services\FileService;
use Milon\Barcode\DNS1D;

class WarehouseExpense3S20Controller extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(FileService $fileService, Claim $claim)
    {
        $this->authorize('show3s20DispatchNote', $claim);

        $claim->load([
            'claimArticle.article',
            'type',
            'dispatchNoteWarehouseExpense',
            'warehouseExpense3S20.toCentre',
            'warehouseExpense3S20.fromCentre',
            'warehouseExpense3S20.actionUser',
        ]);

        $barcodeGenerator = new DNS1D();
        $navigation = new ClaimsNavigationService($claim);
        $warehouseExpense = $claim->warehouseExpense3S20;

        if ($claim->claimArticle->article?->ean) {
            $barcode = $barcodeGenerator->getBarcodeSVG((string)$warehouseExpense->eso_document_number, 'C128');
        }

        return view('warehouse.claims.warehouseExpense3S20', [
            'claim' => $claim,
            'barcode' => $barcode ?? null,
            'navigation' => $navigation,
            'warehouseExpense' => $warehouseExpense,
            'backUrl' => $backUrl ?? null,
        ]);
    }
}
