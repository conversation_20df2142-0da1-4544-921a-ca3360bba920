<?php

namespace App\Http\Controllers\Warehouse\ClaimsRestocking;

use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\AddClaimToRestockingRequest;
use App\Jobs\Eso\ClaimRestockProcessJob;
use App\Jobs\Eso\GetPendingWarehouseDocumentJob;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\ClaimsRestock;
use App\Models\ClaimsRestockingType;
use App\Models\ClaimType;
use App\Models\WarehouseDocumentType;
use App\Services\Claims\ArticlesService;
use App\Services\Claims\ClaimsRestockingService;
use App\Services\Claims\ClaimsService;
use App\Services\EsoApi\ClaimsWarehouseDocumentsService;
use App\Services\VermontApiService;
use Illuminate\Support\Facades\Bus;
use Throwable;

class AddClaimToRestockingController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(AddClaimToRestockingRequest $request, ClaimsRestockingService $claimsRestockingService, ClaimsRestock $claimsRestock)
    {
        $this->authorize('updateClaimsRestock', $claimsRestock);

        try {
            match ($claimsRestock->type_id) {
                ClaimsRestockingType::B2B_B2C_RETURNS_TO_3S20 => $claimsRestockingService->createAndReceiveB2bB2cClaim($claimsRestock, $request->claim_code, auth()->user()),
                ClaimsRestockingType::WAREHOUSE_TRANSFERS => $claimsRestockingService->attachClaimToTransfer($claimsRestock, $request->claim_code),
            };
        } catch (Throwable $e) {
            //\Log::error($e->getMessage());
            return back()->withErrors($e->getMessage());
        }

        return redirect(route('warehouse.claimsRestocking.show', $claimsRestock->id))
            ->with('status-success', __('claimsRestocking.claimWasAddedToList'));
    }
}
