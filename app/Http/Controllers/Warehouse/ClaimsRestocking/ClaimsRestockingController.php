<?php

namespace App\Http\Controllers\Warehouse\ClaimsRestocking;

use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\ClaimsRestockingRequest;
use App\Models\Centre;
use App\Models\ClaimsRestock;
use App\Models\ClaimsRestockingType;
use App\Models\Status;
use App\Models\User;
use App\Services\CentresService;
use App\Services\Claims\ClaimsRestockingService;
use Illuminate\Http\Request;

class ClaimsRestockingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('viewWarehouse', User::class);
        $centreService = new CentresService();
        $warehouses = $centreService->allWarehouses();

        $filter = ($request->filter)
            ?: ((session()->get('warehouseClaimsRestockingFilter'))
                ? session()->get('warehouseClaimsRestockingFilter')
                : []);

        if ($filter) {
            session()->put('warehouseClaimsRestockingFilter', $filter);
        }

        $query = ClaimsRestock::query();
        $claimsRestocking = filterRequest($query, $filter)
            ->withCount('claims')
            ->with('creator', 'warehouse', 'destinationWarehouse', 'status', 'type')
            ->latest()
            ->paginate(25);

        foreach ($claimsRestocking as $claimsRestock) {
            $claimsRestock->articles_sum = (new ClaimsRestockingService())->articlesCount($claimsRestock);
        }

        $creators = $claimsRestocking->pluck('creator')->unique();

        return view('warehouse.claimsRestocking.index', [
            'filter' => $filter,
            'creators' => $creators,
            'claimsRestocking' => $claimsRestocking,
            'warehouses' => $warehouses
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create claims restocking', User::class);
        $centreService = new CentresService();
        $warehouses = $centreService->allWarehouses();
        $types = ClaimsRestockingType::all();

        return view('warehouse.claimsRestocking.create', [
            'warehouses' => $warehouses,
            'types' => $types
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ClaimsRestockingRequest $request, ClaimsRestockingService $claimsRestockingService)
    {
        $this->authorize('create claims restocking', User::class);

        $claimsRestock = $claimsRestockingService->createClaimsRestock((object)$request->validated());

        return redirect(route('warehouse.claimsRestocking.show', $claimsRestock->id))
            ->with('status-success', __('claimsRestocking.claimsRestockingWasCreated'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, ClaimsRestock $claimsRestock)
    {
        $this->authorize('view warehouse', User::class);
        $claimsRestock->load('creator', 'warehouse', 'warehouseExpense', 'warehouseReceipt', 'destinationWarehouse', 'status', 'type');
        $filterName = "warehouseClaimsRestockingFilter-$claimsRestock->id";
        $filter = ($request->filter) ?: ((session()->get($filterName))
            ? session()->get($filterName)
            : []);

        if ($filter) {
            session()->put($filterName, $filter);
        }

        $query = $claimsRestock->claims();
        $claims = filterRequest($query, $filter)
            ->select('claims.id', 'claims.claim_article_id', 'claims.claim_code', 'claims.batch_id')
            ->with([
                'claimArticle:id,article_id,quantity',
                'claimArticle.article:id,goodsid,ean',
                'supplierClaimArticle:id,article_code',
                'supplierClaimArticles:id',
                'warehouseDocuments' => function ($q) {
                    return $q->whereIn('eso_transaction_status_id', [Status::ESO_TRANSACTION_PENDING, Status::ESO_TRANSACTION_FAILED]);
                }
            ])
            ->paginate(25);

        return view('warehouse.claimsRestocking.show', [
            'claimsRestock' => $claimsRestock,
            'claims' => $claims,
            'filterName' => $filterName,
            'filter' => $filter ?? null
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ClaimsRestock $claimsRestock)
    {
        $this->authorize('updateClaimsRestock', $claimsRestock);
        $centreService = new CentresService();
        $warehouses = $centreService->allWarehouses();
        $types = ClaimsRestockingType::all();

        return view('warehouse.claimsRestocking.edit', [
            'warehouses' => $warehouses,
            'claimsRestock' => $claimsRestock,
            'types' => $types
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ClaimsRestockingRequest $request, ClaimsRestock $claimsRestock)
    {
        $this->authorize('updateClaimsRestock', $claimsRestock);

        $claimsRestock->fill([
            'status_id' => Status::CLAIMS_RESTOCKING_CREATED,
            'creator_id' => auth()->id(),
            'warehouse_centre_id' => $request->warehouse_centre_id,
            'destination_warehouse_centre_id' => $request->destination_warehouse_centre_id,
            'batch_id' => $request->batch_id,
            'note' => isset($request->note) ? $request->note : $claimsRestock->note
        ])->save();

        $claimsRestock->createHistoryLog('updated', 'updated', $claimsRestock->toArray());

        return redirect(route('warehouse.claimsRestocking.show', $claimsRestock->id))
            ->with('status-success', __('claimsRestocking.claimsRestockingWasUpdated'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ClaimsRestock $claimsRestock)
    {
        $this->authorize('UpdateClaimsRestock', $claimsRestock);
        $claimsRestock->generalHistoryLogs()->delete();
        $claimsRestock->esoHistoryLogs()->delete();
        $claimsRestock->delete();
        return redirect(route('warehouse.claimsRestocking.index'))->with('status-success', __('claimsRestocking.claimsRestockingWasDeleted'));
    }
}
