<?php

namespace App\Http\Controllers\Warehouse\ClaimsRestocking;

use App\Http\Controllers\Controller;
use App\Models\ClaimsRestock;
use Illuminate\Http\Request;

class ClaimsRestockingEsoLogsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, ClaimsRestock $claimsRestock)
    {
        $this->authorize('viewLogs', $claimsRestock);
        $logs = $claimsRestock->esoHistoryLogs()->with('actionUser')->paginate('50');

        return view('warehouse.claimsRestocking.esoLogs', [
            'claimsRestock' => $claimsRestock,
            'logs' => $logs,
        ]);
    }
}
