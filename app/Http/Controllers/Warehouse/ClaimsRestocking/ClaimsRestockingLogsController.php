<?php

namespace App\Http\Controllers\Warehouse\ClaimsRestocking;

use App\Http\Controllers\Controller;
use App\Models\ClaimsRestock;
use Illuminate\Http\Request;

class ClaimsRestockingLogsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, ClaimsRestock $claimsRestock)
    {
        $this->authorize('viewLogs', $claimsRestock);
        $logs = $claimsRestock->generalHistoryLogs()->with('actionUser')->paginate('50');

        return view('warehouse.claimsRestocking.logs', [
            'claimsRestock' => $claimsRestock,
            'logs' => $logs,
        ]);
    }
}
