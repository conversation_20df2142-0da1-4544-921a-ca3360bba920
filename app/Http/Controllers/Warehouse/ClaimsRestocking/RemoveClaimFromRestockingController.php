<?php

namespace App\Http\Controllers\Warehouse\ClaimsRestocking;

use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\RemoveClaimFromRestockingRequest;
use App\Models\Claim;
use App\Models\ClaimsRestock;
use Illuminate\Http\Request;

class RemoveClaimFromRestockingController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, ClaimsRestock $claimsRestock, Claim $claim)
    {
        $this->authorize('removeClaimFromRestock', [$claimsRestock,$claim]);

        if (!$claimsRestock->claims()->where('claims.id', $claim->id)->exists()) {
            return back()->withErrors(__('claimsRestocking.ClaimNotFoundInList'));
        }

        $claimsRestock->claims()->detach($claim->id);
        $claimsRestock->createHistoryLog('updated', 'claimWasRemovedFromList', $claim->claim_code);

        return redirect(route('warehouse.claimsRestocking.show', $claimsRestock->id))
            ->with('status-success', __('claimsRestocking.claimWasRemovedFromList'));
    }
}
