<?php

namespace App\Http\Controllers\Warehouse\ClaimsRestocking;

use App\Http\Controllers\Controller;
use App\Jobs\Eso\ClaimRestockProcessJob;
use App\Models\Claim;
use App\Models\ClaimsRestock;
use App\Models\Status;
use App\Services\Claims\ClaimsService;
use App\Services\EsoApi\WarehouseDocumentsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;

class RestockScannedClaimsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, ClaimsRestock $claimsRestock)
    {
        $this->authorize('createWarehouseExpense', $claimsRestock);
        $claimsRestock->load('destinationWarehouse');
        $claimsService = new ClaimsService();
        $warehouseDocumentsService = new WarehouseDocumentsService($claimsRestock);
        $user = auth()->user();

        $claims = $claimsRestock->claims()
            ->with(
                [
                    'claimArticle:id,article_id,quantity',
                    'claimArticle.article:id,goodsid',
                    'supplierClaimArticles:id,goodsid'
                ]
            )
            ->get();


        $articlesForDispatch = [];

        foreach ($claims as $claim) {
            if ($claim->is_supplier_claim) {

                foreach ($claim->supplierClaimArticles as $supplierClaimArticle) {
                    $articlesForMerge = $warehouseDocumentsService->formatArticleForDispatch(
                        $supplierClaimArticle->goodsid,
                        $supplierClaimArticle->pivot->quantity
                    );
                    $articlesForDispatch = array_merge($articlesForDispatch, $articlesForMerge);
                }
            } else {
                $articlesForMerge = $warehouseDocumentsService->formatArticleForDispatch(
                    $claim->claimArticle->article->goodsid,
                    $claim->claimArticle->quantity
                );
                $articlesForDispatch = array_merge($articlesForDispatch, $articlesForMerge);
            }
        }
        

        try {
            $expense = $warehouseDocumentsService->createExpense(
                articles: $articlesForDispatch,
                fromCentre: $claimsRestock->warehouse,
                toCentre: $claimsRestock->destinationWarehouse,
                actionUser: $user,
                additionalData: $claimsRestock->id
            );

            $warehouseDocumentsService->increaseHeaderState(expense: $expense);

            $warehouseDocumentsService->createWarehouseReceipt(
                expense: $expense,
                toCentre: $claimsRestock->destinationWarehouse,
                user: $user
            );
        } catch (\Throwable $e) {

            $error = is_json_string($e->getMessage())
                ? createStringFromIterable(json_decode($e->getMessage()))
                : $e->getMessage();

            \Log::error("hromadne preskladnenie #$claimsRestock->id: $error");

            if ($e->getCode() == 408) {
                Bus::chain([
                    new ClaimRestockProcessJob(
                        claim: $claim,
                        additionalDataForEso: $claim->claim_code,
                        actionUser: $user,
                        destinationWarehouse: $claimsRestock->destinationWarehouse,
                    ),
                    function () use ($claimsRestock, $user, $claimsService) {
                        $claimsRestock->update(['status_id' => Status::CLAIMS_RESTOCKING_CLOSED]);
                        $claimsRestock->createHistoryLog('closed', 'claimsRestockingClosed');
                        $claimsService->restockClaims($claimsRestock, $user);
                    }
                ])->dispatch();
                return redirect()->back();
            } else {
                return redirect()->back()->withErrors(json_decode($e->getMessage()));
            }
        }


        $claimsRestock->update(['status_id' => Status::CLAIMS_RESTOCKING_CLOSED]);
        $claimsRestock->createHistoryLog('closed', 'claimsRestockingClosed');
        $claimsService->restockClaims($claimsRestock, $user);

        return redirect()->back()->with('status-success', __('claimsRestocking.claimsWasRestocked'));
    }
}
