<?php

namespace App\Http\Controllers\Warehouse\Receiving;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class CreateReceivingFromController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $this->authorize('createWarehouseReceiving', User::class);

        return view('warehouse.receiving.create', [
            'isUnclosedClaim' => session('claim-not-closed'),
            'centre' => session('centre'),
        ]);
    }
}
