<?php

namespace App\Http\Controllers\Warehouse\Receiving;

use App\Http\Controllers\Controller;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\Status;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class SearchClaimController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function __invoke(Request $request): RedirectResponse
    {
        $this->authorize('createWarehouseReceiving', User::class);
        $validated = $request->validate(['claim_code' => 'required|exists:claims,claim_code']);
        $allowedStatuses = [
            Status::CLAIM_SEND_TO_WAREHOUSE,
            Status::CLAIM_STATUS_WAREHOUSE_EXPENSE_CREATED,
            Status::CLAIM_STATUS_WAITING_FOR_TRANSPORT_CONFIRMATION,
            Status::CLAIM_STATUS_IN_TRANSPORT,
            Status::CLAIM_STATUS_WAREHOUSE_RECEIVED
        ];

        $claim = Claim::where('claim_code', $validated['claim_code'])
            ->where('claim_status_id', '!=', Status::CLAIM_STATUS_CANCELED)
            ->where('claim_centre_id', '!=', Centre::Warehouse_3R00)
            ->first();

        if (empty($claim)) {
            return redirect(route('warehouse.receiving.create'))
                ->withInput()
                ->withErrors([__('receiving.claimNotFound')]);
        } elseif (!in_array($claim->claim_status_id, $allowedStatuses)) {
            $centre = Centre::find($claim->claim_centre_id);
            return redirect(route('warehouse.receiving.create'))
                ->withInput(['claim_code' => $request->claim_code])
                ->with([
                    'claim-not-closed' => true,
                    'centre' => $centre
                ]);
        }

        if ($claim->claim_type_id == ClaimType::CLAIM_TYPE_SUPPLIER) {
            return redirect(route('warehouse.supplierClaimReceiving.show', $claim->id));
        } else {
            return redirect(route('warehouse.receiving.show', $claim->id));
        }
    }
}
