<?php

namespace App\Http\Controllers\Warehouse\Receiving;

use App\Http\Controllers\Controller;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\DamagePlace;
use App\Models\DamageType;
use App\Models\Status;
use App\Models\TrackedArticle;
use App\Models\WarehouseDocumentType;
use App\Services\Claims\ArticlesService;
use App\Services\Claims\ClaimsService;
use App\Services\EsoApi\ClaimsWarehouseDocumentsService;
use App\Services\EsoApi\EsoService;
use Illuminate\Http\Request;
use TheSeer\Tokenizer\Exception;

class ShowReceivingFormController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(
        Request         $request,
        ClaimsService   $claimsService,
        ArticlesService $articlesService,
        Claim           $claim
    )
    {
        $this->authorize('showWarehouseClaimReceiving', $claim);
        $claim->load([
            'transports',
            'activeWarehouseExpense.successor',
            'activeWarehouseReceipt.predecessor',
            'claimArticle',
            'claimArticle.article',
            'claimArticle.damageType',
            'claimArticle.damageSubType',
        ]);


        if (isset($claim->activeWarehouseExpense)) {

            try {
                $esoService = new EsoService();
                $expense = $esoService
                    ->getWarehouseExpense($claim->dispatchNoteWarehouseExpense?->eso_document_number)
                    ->getResponse();
                $expense = $expense->data->vydejka[0];


                if (!isset($claim->activeWarehouseExpense->successor) && isset($expense->naslednik) && $expense->naslednik[0]->typ_dokladu != 'EKRE') {
                    $claimsWarehouseDocumentsService = new ClaimsWarehouseDocumentsService($claim);
                    $claimsWarehouseDocumentsService->searchDispatchNoteWarehouseExpenseSuccessor($expense->data->vydejka[0]);
                    $claim->activeWarehouseExpense->refresh();
                }

            } catch (\Throwable $throwable) {
                return redirect()->back()->withErrors(__('eso.unexpectedErrorOccurredPleaseTryAgain'));

            }

            $claimsWarehouseService = new ClaimsWarehouseDocumentsService($claim);
            $claimsWarehouseService->checkDifferencesInDispatchNote($claim, $claim->dispatchNoteWarehouseExpense, $expense);

        }


        $availableWarehouses = $claimsService->getAvailableWarehouses($claim);
        $photos = $claim->photos->toArray();
        $supplierClaimArticle = $articlesService->searchSupplierClaimArticle($claim->claimArticle->article_id);

        $articleCode = sliceGoodsId($claim->claimArticle->article->goodsid, 0, 3);
        $trackedArticle = TrackedArticle::where('article_code', $articleCode)->first();
        $articleIsInTrackedArticlesList = !empty($trackedArticle);

        $damageTypes = DamageType::byOrderGroup($claim->claimArticle->article->order_group_id)
            ->subTypes()
            ->withOther()
            ->onlyActive()
            ->withParent()
            ->formatCollectionForSelectInput();

        return view('warehouse.receiving.receivingForm', [
            'claim' => $claim,
            'photos' => $photos,
            'availableWarehouses' => $availableWarehouses,
            'supplierClaimArticle' => $supplierClaimArticle,
            'successorExist' => isset($claim->activeWarehouseExpense->successor),
            'articleIsInTrackedArticlesList' => $articleIsInTrackedArticlesList,
            'damageTypes' => $damageTypes,
        ]);
    }
}
