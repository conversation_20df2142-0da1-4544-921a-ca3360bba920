<?php

namespace App\Http\Controllers\Warehouse\Receiving;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\DamagePlace;
use App\Models\SupplierClaimArticle;
use App\Services\Claims\ArticlesService;
use App\Services\Claims\ClaimsService;
use App\Services\EsoApi\ClaimsWarehouseDocumentsService;
use App\Services\EsoApi\EsoService;
use Illuminate\Http\Request;

class ShowSupplierClaimReceivingFormController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(
        Request         $request,
        ClaimsService   $claimsService,
        ArticlesService $articlesService,
        Claim           $claim
    )
    {
        $this->authorize('showWarehouseClaimReceiving', $claim);
        $claim->load([
            'transports',
            'activeWarehouseExpense',
            'dispatchNoteWarehouseExpense',
            'activeWarehouseReceipt.predecessor',
            'supplierClaimArticles',
        ]);

        if (isset($claim->activeWarehouseExpense)) {
            try {
                $esoService = new EsoService();
                $expense = $esoService
                    ->getWarehouseExpense($claim->dispatchNoteWarehouseExpense?->eso_document_number)
                    ->getResponse();
                $expense = $expense->data->vydejka[0];

            } catch (\Throwable $throwable) {
                return redirect()->back()->withErrors(__('eso.unexpectedErrorOccurredPleaseTryAgain'));
            }

            $claimsWarehouseService = new ClaimsWarehouseDocumentsService($claim);
            $claimsWarehouseService->checkDifferencesInDispatchNote($claim, $claim->dispatchNoteWarehouseExpense, $expense);
        }

        $availableWarehouses = $claimsService->getAvailableWarehouses($claim);
        $claim->load('supplierClaimArticles');
        $supplierClaimArticle = $claim->supplierClaimArticles->first();

        $supplierClaimArticle = SupplierClaimArticle::with('photos', 'diagram')
            ->where('article_code', sliceGoodsId($supplierClaimArticle->goodsid, 0, 4))
            ->first();


        return view('warehouse.receiving.supplierClaimReceivingForm', [
            'claim' => $claim,
            'photos' => $supplierClaimArticle->photos->toArray(),
            'availableWarehouses' => $availableWarehouses,
            'supplierClaimArticle' => $supplierClaimArticle,
            'supplierClaimArticles' => $claim->supplierClaimArticles,
            'successorExist' => isset($claim->activeWarehouseExpense->successor),
        ]);
    }
}
