<?php

namespace App\Http\Controllers\Warehouse\Receiving;

use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\ClaimReceivingRequest;
use App\Jobs\Eso\ClaimRestockProcessJob;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\ClaimType;
use App\Models\Status;
use App\Notifications\InternalNotifications\WarehouseNotifications\ClaimWasClosedNotification;
use App\Services\Claims\ClaimsService;
use App\Services\EsoApi\WarehouseDocumentsService;
use Illuminate\Support\Facades\Bus;


class StoreReceivingForm extends Controller
{

    public function __invoke(
        ClaimReceivingRequest $request,
        ClaimsService         $claimsService,
        Claim                 $claim
    )
    {
        $this->authorize('createWarehouseClaimReceiving', $claim);
        $claim->load('centre', 'activeWarehouseExpense.successor', 'activeWarehouseReceipt');
        $user = auth()->user();
        $batchId = $request->batch_id;

        $unclosedClaimsStatuses = [
            Status::CLAIM_STATUS_WAREHOUSE_EXPENSE_CREATED,
        ];

        if (in_array($claim->claim_status_id, $unclosedClaimsStatuses)) {
            $claim->createHistoryLog('closedBySystem', 'claimWasClosedBySystem');
            $claim->centre->notify(new ClaimWasClosedNotification($claim));
        }

        $warehouseDocumentsService = new WarehouseDocumentsService($claim);
        $selectedWarehouse = Centre::find($request->warehouse_id);

        try {
            if ($claim->activeWarehouseExpense && !isset($claim->activeWarehouseExpense->successor)) {
                if ($selectedWarehouse->id != $claim->activeWarehouseExpense->to_centre_id) {
                    $expense = $warehouseDocumentsService->updateExpense(
                        $claim->activeWarehouseExpense,
                        $selectedWarehouse,
                        $claim->claim_code,
                        $user
                    );
                    $warehouseDocumentsService->createWarehouseReceipt($expense, $selectedWarehouse, $user);
                } else {
                    $warehouseDocumentsService->createWarehouseReceipt(
                        $claim->activeWarehouseExpense,
                        $selectedWarehouse,
                        $user
                    );
                }
            } elseif (!$claim->activeWarehouseExpense && $selectedWarehouse->id != Centre::Warehouse_3R00 || isset($claim->activeWarehouseExpense->successor) && $selectedWarehouse->id != Centre::Warehouse_3R00) {
                $warehouseCentre3R00 = Centre::find(Centre::Warehouse_3R00);
                if ($claim->claim_type_id == ClaimType::CLAIM_TYPE_SUPPLIER) {
                    $articles = [];
                    foreach ($claim->supplierClaimArticles as $supplierClaimArticle) {
                        $articlesForMerge = $warehouseDocumentsService->formatArticleForDispatch(
                            $supplierClaimArticle->goodsid,
                            $supplierClaimArticle->pivot->quantity
                        );
                        $articles = array_merge($articles, $articlesForMerge);
                    }
                } else {
                    $articles = $warehouseDocumentsService->formatArticleForDispatch(
                        $claim->claimArticle->article->goodsid,
                        $claim->claimArticle->quantity
                    );
                }

                $expense = $warehouseDocumentsService->createExpense(
                    $articles,
                    $warehouseCentre3R00,
                    $selectedWarehouse,
                    $user,
                    $claim->claim_code
                );
                $warehouseDocumentsService->increaseHeaderState($expense);
                $warehouseDocumentsService->createWarehouseReceipt($expense, $selectedWarehouse, $user);
            }
        } catch (\Throwable $e) {

            if ($e->getCode() == 408) { //eso nevratilo odpoved
                Bus::chain([
                    new ClaimRestockProcessJob(
                        claim: $claim,
                        additionalDataForEso: $claim->claim_code,
                        actionUser: $user,
                        destinationWarehouse: $selectedWarehouse,

                    ),
                    function () use ($claimsService, $claim, $selectedWarehouse, $batchId, $user) {
                        $claimsService->receivedInWarehouse(
                            claim: $claim,
                            selectedWarehouse: $selectedWarehouse,
                            actionUserId: $user->id,
                            batchId: $batchId,
                        );
                    }
                ])->dispatch();
                return redirect()->back();
            } else {
                return redirect()->back()->withErrors(json_decode($e->getMessage()));
            }
        }

        $claimsService->receivedInWarehouse(
            claim: $claim,
            selectedWarehouse: $selectedWarehouse,
            actionUserId: $user->id,
            batchId: $batchId,
        );

        return redirect(route('warehouse.claims.index'))->with(
            'status-success',
            __('receiving.sentTo', ['warehouse' => $selectedWarehouse->code])
        );
    }
}
