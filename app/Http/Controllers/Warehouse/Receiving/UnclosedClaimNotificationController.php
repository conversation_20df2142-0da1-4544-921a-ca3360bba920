<?php

namespace App\Http\Controllers\Warehouse\Receiving;

use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\StoreUnclosedClaimNotificationRequest;
use App\Models\Centre;
use App\Models\User;
use App\Notifications\InternalNotifications\WarehouseNotifications\UnclosedClaimNotification;

class UnclosedClaimNotificationController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(
        StoreUnclosedClaimNotificationRequest $request,
    ) {
        $centre = (object)$request->validated('centre');
        Centre::find($centre->id)->notify(new UnclosedClaimNotification($request->validated('claim_code')));
        return redirect(route('warehouse.receiving.create'))
            ->with('status-success', __('receiving.centreWasContacted'))
            ->withInput(['claim_code' => $request->validated('claim_code')]);
    }
}