<?php

namespace App\Http\Controllers\Warehouse\Reports;

use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\AddClaimToWarehouseReportRequest;
use App\Models\Claim;
use App\Models\Report;
use App\Models\Status;

class AddClaimToReportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(AddClaimToWarehouseReportRequest $request, Report $report)
    {
        $this->authorize('editReportedClaims', $report);
        $claim = Claim::where('claim_code', $request->claim_code)->first();
        $claim->fill([
            'report_id' => $report->id,
            'warehouse_status_id' => Status::WAREHOUSE_STATUS_REPORTED,
        ])
            ->save();

        $report->update(['status_id' => Status::REPORT_GENERATE_REQUIRED]);

        $report->logs()->create([
            'status_id' => $report->status_id,
            'action_user_id' => auth()->id(),
            'action_type' => 'updated',
            'action' => 'claimAdded',
            'note' => $claim->claim_code
        ]);
        $claim->createHistoryLog('updated', 'claimWasManuallyAddedToReport');

        return back()->with('status-success', __('reports.claimWasAddedIntoReport'));
    }
}
