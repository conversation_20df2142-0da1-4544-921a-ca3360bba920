<?php

namespace App\Http\Controllers\Warehouse\Reports;

use App\Http\Controllers\Controller;
use App\Models\Report;
use App\Services\FileService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class DownloadPublicReportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, FileService $fileService, Report $report)
    {
        $request->validate(['password' => 'required']);

        if ($request->input('password') != $report->decryptedPassword) {
            return redirect()->back()->withErrors(__('general.wrongPassword'));
        } else {
            return Storage::disk($fileService->getFilesDisk())->download($report->zipFile->path);
        }
    }
}
