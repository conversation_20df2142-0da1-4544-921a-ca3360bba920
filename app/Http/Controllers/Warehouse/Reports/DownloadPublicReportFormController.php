<?php

namespace App\Http\Controllers\Warehouse\Reports;

use App\Http\Controllers\Controller;
use App\Models\Report;
use Illuminate\Http\Request;

class DownloadPublicReportFormController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Report $report)
    {
        return view('warehouse.reports.downloadPublicReportForm', [
            'report' => $report,
        ]);
    }
}
