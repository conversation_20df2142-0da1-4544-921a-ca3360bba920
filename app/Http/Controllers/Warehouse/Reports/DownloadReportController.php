<?php

namespace App\Http\Controllers\Warehouse\Reports;

use App\Http\Controllers\Controller;
use App\Models\Report;
use App\Services\FileService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class DownloadReportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, FileService $fileService, Report $report)
    {
        $this->authorize('view reports', auth()->user());
      
        return Storage::disk($fileService->getFilesDisk())->download($report->zipFile->path);
    }
}
