<?php

namespace App\Http\Controllers\Warehouse\Reports;


use App\Exports\ReportedClaimsExport;
use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\Report;
use App\Services\Claims\ClaimsService;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class ExportFilteredReportedClaimsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(
        Request       $request,
        ClaimsService $claimsService,
        Report        $report
    )
    {
        $user = auth()->user();
        $this->authorize('view warehouse', $user);
        $date = date("Y.m.d");
        $filter = session()->get($report->id . '-reportFilter');
        $query = Claim::query();

        if (isset($filter)) {
            $query = filterRequest($query, $filter);
        }

        $claims = $query
            ->where('report_id', $report->id)
            ->orderByRaw(
                "CASE WHEN is_active = 1 THEN 0 END desc,
                            CASE WHEN is_active = 0 THEN warehouse_received_at ELSE warehouse_received_at END desc"
            )
            ->get();


        return Excel::download(new ReportedClaimsExport($claims), "filtered-reported-claims-$date.xlsx");
    }
}
