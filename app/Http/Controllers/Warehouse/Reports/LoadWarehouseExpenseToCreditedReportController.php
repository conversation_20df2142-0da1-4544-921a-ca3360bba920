<?php

namespace App\Http\Controllers\Warehouse\Reports;

use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\LoadWarehouseExpenseToCreditedReportRequest;
use App\Jobs\Warehouse\Reports\ClaimsCreditedJob;
use App\Jobs\Warehouse\Reports\LoadWarehouseExpenseToCreditedReportJob;
use App\Models\ClaimHistoryLog;
use App\Models\Report;
use App\Models\Status;
use App\Services\EsoApi\WarehouseDocumentsService;
use Illuminate\Http\Request;

class LoadWarehouseExpenseToCreditedReportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(LoadWarehouseExpenseToCreditedReportRequest $request, Report $report)
    {
        $this->authorize('loadWarehouseExpenseToCreditedReport', $report);

        $warehouseDocumentsService = new WarehouseDocumentsService($report);
        $warehouseExpenses = collect();
        $creditNotes = collect();

        foreach ($request->warehouse_expense_numbers as $warehouseExpenseNumber) {

            try {
                $warehouseExpense = $warehouseDocumentsService->getWarehouseExpense($warehouseExpenseNumber);


            } catch (\Throwable $e) {
                return redirect()->back()->withErrors(json_decode($e->getMessage()));
            }

            //when destination warehouse missing, loaded dispatch is considered as credit note
            if (!isset($warehouseExpense->kod_cieloveho_skladu)) {
                $creditNotes->push($warehouseExpense);
                continue;
            }

            if ($warehouseExpense->kod_skladu !== '3R00') {
                return redirect()->back()->withErrors(__('reports.warehouseCodeMustBe3R00'));
            }

            if (!in_array($warehouseExpense->kod_cieloveho_skladu, ['3S11', '3R01','3P99','2P99'])) {
                return redirect()->back()->withErrors(__('reports.destinationWarehouseMustBe', ['warehouses' => '3S11, 3R01','3P99','2P99']));
            }

            $warehouseExpenses->push($warehouseExpense);
        }

        $report->fill([
            'status_id' => Status::REPORT_BEING_PROCESSED,
        ])->save();



        if ($creditNotes->count() > 0) {
            $updateReportStatus = $warehouseExpenses->count() === 0;
            ClaimsCreditedJob::dispatch(report: $report, warehouseExpenses: $creditNotes, userId: auth()->id(), updateStatus: $updateReportStatus, sendNotification: false);
        }

        if ($warehouseExpenses->count() > 0){
            LoadWarehouseExpenseToCreditedReportJob::dispatch($report, $warehouseExpenses, auth()->id());
        }

        return back()->with('status-success', __('reports.warehouseExpenseWasUploaded'));
    }
}

