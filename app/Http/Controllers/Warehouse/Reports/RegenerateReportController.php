<?php

namespace App\Http\Controllers\Warehouse\Reports;

use App\Http\Controllers\Controller;
use App\Jobs\Warehouse\Reports\CreateReportJob;
use App\Models\Report;
use App\Models\Status;
use Illuminate\Http\Request;

class RegenerateReportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Report $report)
    {
        $this->authorize('regenerateReport', $report);
        $report->fill(['status_id' => Status::REPORT_BEING_PROCESSED])->save();
        $lastLogStatusId = $report->logs()->where('status_id', '!=', Status::REPORT_GENERATE_REQUIRED)
            ->latest('id')
            ->first()
            ->status_id;

        $report->load('reportFiles');
        CreateReportJob::dispatch($report, auth()->user(), $lastLogStatusId);
        return back()->with('status-success', __('reports.reportCreatingPleaseWait'));
    }
}
