<?php

namespace App\Http\Controllers\Warehouse\Reports;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\Report;
use App\Models\Status;
use Illuminate\Http\Request;

class RemoveClaimFromReportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Report $report, Claim $claim)
    {
        $this->authorize('editReportedClaims', $report);
        $claim->update([
            'report_id' => null,
            'warehouse_status_id' => Status::WAREHOUSE_STATUS_STORED_IN_BATCH,
        ]);

        $report->update(['status_id' => Status::REPORT_GENERATE_REQUIRED]);

        $report->logs()->create([
            'status_id' => $report->status_id,
            'action_user_id' => auth()->id(),
            'action_type' => 'updated',
            'action' => 'claimRemoved',
            'note' => $claim->claim_code
        ]);

        $claim->createHistoryLog('updated', 'claimWasRemovedFromReport');

        return back()->with('status-success', __('reports.claimWasRemovedFromReport'));
    }
}
