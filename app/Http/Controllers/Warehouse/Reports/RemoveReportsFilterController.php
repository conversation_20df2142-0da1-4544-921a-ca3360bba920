<?php

namespace App\Http\Controllers\Warehouse\Reports;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class RemoveReportsFilterController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        session()->remove('warehouseCreateReportsFilter');

        return redirect(route('warehouse.reports.create'));
    }
}
