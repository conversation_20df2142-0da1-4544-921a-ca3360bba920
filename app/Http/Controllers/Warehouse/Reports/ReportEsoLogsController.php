<?php

namespace App\Http\Controllers\Warehouse\Reports;

use App\Http\Controllers\Controller;
use App\Models\Report;
use Illuminate\Http\Request;

class ReportEsoLogsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Report $report)
    {
        $this->authorize('view logs');
        $logs = $report->esoHistoryLogs()
            ->with('actionUser:id,name')
            ->oldest()
            ->paginate(20);

        return view('warehouse.reports.esoLogs', [
            'report' => $report,
            'logs' => $logs,
        ]);
    }
}
