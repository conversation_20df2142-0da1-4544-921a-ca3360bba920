<?php

namespace App\Http\Controllers\Warehouse\Reports;

use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\WarehouseClaimRestockRequest;
use App\Jobs\Eso\ClaimRestockProcessJob;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\Report;
use App\Services\Claims\ClaimsService;
use App\Services\EsoApi\WarehouseDocumentsService;
use Illuminate\Support\Facades\Bus;

class RestockReportClaimController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(WarehouseClaimRestockRequest $request, Report $report, Claim $claim)
    {
        $this->authorize('restockWarehouseReportClaim', [$report, $claim]);
        $claim->load(['claimArticle:id,article_id,quantity', 'claimArticle.article:id,goodsid', 'warehouseCentre']);
        $selectedWarehouse = Centre::find($request->warehouse_id);
        $warehouseDocumentsService = new WarehouseDocumentsService($claim);
        $claimsService = new ClaimsService();
        $articles = $warehouseDocumentsService->formatArticleForDispatch(
            $claim->claimArticle->article->goodsid,
            $claim->claimArticle->quantity
        );

        $user = auth()->user();

        try {
            $expense = $warehouseDocumentsService->createExpense(
                $articles,
                $claim->warehouseCentre,
                $selectedWarehouse,
                $user,
                $claim->claim_code,
            );

            $warehouseDocumentsService->increaseHeaderState($expense);
            $warehouseDocumentsService->createWarehouseReceipt($expense, $selectedWarehouse, $user);
        } catch (\Throwable $e) {
            $error = is_json_string($e->getMessage())
                ? createStringFromIterable(json_decode($e->getMessage()))
                : $e->getMessage();

            //\Log::error($claim->claim_code . ': ' . $error);

            if ($e->getCode() == 408) {
                Bus::chain([
                    new ClaimRestockProcessJob(
                        claim: $claim,
                        additionalDataForEso: $claim->claim_code,
                        actionUser: $user,
                        destinationWarehouse: $selectedWarehouse,

                    ),
                    function () use ($claimsService, $claim, $selectedWarehouse, $user) {
                        $claimsService->restockClaim(
                            claim: $claim,
                            selectedWarehouse: $selectedWarehouse,
                            actionUserId: $user->id,
                            closeClaim: true
                        );
                    }
                ])->dispatch();
                return redirect()->back()
                    ->with('status-info', __('eso.esoTransactionMayTakeSeveralMinutesPleaseBePatient'));
            } else {
                return redirect()->back()->withErrors(json_decode($e->getMessage()));
            }
        }

        $claimsService->restockClaim(
            claim: $claim,
            selectedWarehouse: $selectedWarehouse,
            actionUserId: $user->id,
            closeClaim: true
        );

        return redirect(route('warehouse.reports.show', $report->id) . '#table')
            ->with('status-success', __("receiving.sentTo$selectedWarehouse->code"));
    }
}
