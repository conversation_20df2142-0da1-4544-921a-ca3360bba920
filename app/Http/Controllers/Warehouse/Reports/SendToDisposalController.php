<?php

namespace App\Http\Controllers\Warehouse\Reports;

use App\Http\Controllers\Controller;
use App\Models\Claim;
use App\Models\Report;
use App\Models\Status;
use Illuminate\Http\Request;

class SendToDisposalController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Report $report, Claim $claim)
    {
        $this->authorize('restockWarehouseReportClaim', [$report, $claim]);
        $claim->update(['warehouse_status_id' => Status::WAREHOUSE_STATUS_SENT_TO_DISPOSAL, 'is_active' => 0]);
        return redirect(route('warehouse.reports.show', $report->id) . '#table')
            ->with('status-success', __('reports.claimWasSentToDisposal'));
    }
}
