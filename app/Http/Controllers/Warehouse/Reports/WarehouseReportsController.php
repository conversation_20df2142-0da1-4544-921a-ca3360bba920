<?php

namespace App\Http\Controllers\Warehouse\Reports;

use App\Enums\ReportDecisionsEnums;
use App\Http\Controllers\Controller;
use App\Http\Requests\Warehouse\UpdateReportRequest;
use App\Jobs\Warehouse\Reports\ClaimsCreditedJob;
use App\Jobs\Warehouse\Reports\CreateReportJob;
use App\Mail\Reports\NewReportForCheckMail;
use App\Mail\Reports\ReportCheckedMail;
use App\Models\Article;
use App\Models\Batch;
use App\Models\Centre;
use App\Models\Claim;
use App\Models\ClaimHistoryLog;
use App\Models\Report;
use App\Models\ReportLog;
use App\Models\Status;
use App\Models\Trademark;
use App\Services\Claims\ArticlesService;
use App\Services\EsoApi\WarehouseDocumentsService;
use App\Services\FileService;
use App\Services\NotificationsService;
use App\Services\StatusesService;
use App\Services\Warehouse\ReportsService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class WarehouseReportsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('viewReport', Report::class);
        $filter = ($request->filter) ?: ((session()->get('warehouseReportsIndexPageFilter'))
            ? session()->get('warehouseReportsIndexPageFilter')
            : []);

        $query = Report::query();

        if ($filter) {
            session()->put('warehouseReportsIndexPageFilter', $filter);
            $query = filterRequest($query, $filter, function (Builder $q) use ($filter) {
                return $q->when(isset($filter['closure.trademark']), function ($q) use ($filter) {

                    //Starsie reporty obsahuju iba jeden trademark
                    return $q->whereJsonContains(
                        'filter->trademark,=,claimArticle.article',
                        $filter['closure.trademark']
                    )->orWhereJsonContains(
                        'filter->trademark,in,claimArticle.article',
                        json_encode([$filter['closure.trademark']])
                    );
                });
            });
        }

        $reports = $query->withCount('claims')
            ->with('status', 'creator')
            ->latest()
            ->paginate(25);

        return view('warehouse.reports.index', [
            'reports' => $reports,
            'filter' => $filter,
            'trademarks' => Trademark::all(),
            'creators' => $reports->pluck('creator'),
            'statuses' => Status::where('type', 'reports')->get(),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(
        Request $request,
        ArticlesService $articlesService,
        StatusesService $statusesService,
    ) {
        $this->authorize('createReport', Report::class);
        $trademarks = Trademark::all();
        $suppliers = $articlesService->getAllArticlesSuppliers();
        $statuses = $statusesService->allWarehouseStatuses();


        if ($request->filter) {
            $query = Claim::query();
            $query
                ->where('warehouse_status_id', Status::WAREHOUSE_STATUS_STORED_IN_BATCH)
                ->where('warehouse_centre_id', Centre::Warehouse_3R00)
                ->where('is_active', 1)
                ->whereDoesntHave('report');
            $query = filterRequest($query, $request->filter);

            $claims = $query->with([
                'user:id,name',
                'claimArticle:id,article_id,damage_description,quantity',
                'claimArticle.article:id,goodsid,name,supplier_name,trademark',
                'warehouseStatus:id,name',
                'warehouseCentre:id,name,code',
                'type:id,name',
                'lastWarehouseReceipt',
            ])->orderByRaw("CASE WHEN claim_type_id = 5 THEN created_at ELSE warehouse_received_at END desc")
                ->paginate(25)
                ->withQueryString();
        }


        return view('warehouse.reports.create', [
            'filter' => $request->filter,
            'trademarks' => $trademarks,
            'statuses' => $statuses,
            'suppliers' => $suppliers,
            'claims' => $claims ?? null
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('createReport', Report::class);
        $this->authorize('create reports', auth()->user());
        $validated = $request->validate(['filter' => 'required|json']);

        $report = Report::create([
            'status_id' => Status::REPORT_BEING_PROCESSED,
            'creator_id' => auth()->id(),
            'password' => Crypt::encryptString(Str::random(15)),
            'filter' => $validated['filter'],
        ]);

        $query = Claim::query();
        filterRequest($query, json_decode($request['filter']))
            ->where('warehouse_status_id', Status::WAREHOUSE_STATUS_STORED_IN_BATCH)
            ->where('warehouse_centre_id', Centre::Warehouse_3R00)
            ->where('is_active', 1)
            ->whereDoesntHave('report')
            ->update([
                'report_id' => $report->id,
                'warehouse_status_id' => Status::WAREHOUSE_STATUS_REPORTED,
            ]);

        CreateReportJob::dispatch($report, auth()->user());

        return redirect(route('warehouse.reports.index'))->with(
            'status-success',
            __('reports.reportBeingProcessedPleaseWait')
        );
    }

    /**
     * Display the specified resource.
     */
    public function show(
        Request $request,
        StatusesService $statusesService,
        ArticlesService $articlesService,
        ReportsService $reportsService,
        Report $report
    ) {
        $this->authorize('viewReport', Report::class);

        $filter = ($request->filter) ?: ((session()->get($report->id . '-reportFilter'))
            ? session()->get($report->id . '-reportFilter')
            : []);

        $query = $report->claims();

        if ($filter) {
            session()->put($report->id . '-reportFilter', $filter);
            $query = filterRequest($query, $filter);
        }

        $claims = $query
            ->with([
                'warehouseStatus',
                'user:id,name',
                'claimArticle:id,article_id,damage_description,quantity',
                'claimArticle.article:id,goodsid,name,supplier_name,trademark,season',
                'status:id,name',
                'warehouseCentre:id,name,code',
                'type:id,name',
            ])
            ->when($report->status_id == Status::REPORT_CREDITED, function (Builder $q) {
                $q->with('warehouseDocuments')
                    ->orderByRaw(
                        "CASE WHEN is_active = 1 THEN 0 END desc,
                            CASE WHEN is_active = 0 THEN warehouse_received_at ELSE warehouse_received_at END desc"
                    );
            })
            ->paginate(25)
            ->withQueryString();

        $reportLogs = ReportLog::where('report_id', $report->id)
            ->with(['actionUser', 'status'])
            ->get();

        if ($report->status_id == Status::REPORT_CREDITED) {
            $cdsArticle = Article::where('goodsid', 'CD_S-0-OT-0-0')->first();
        }

        return view('warehouse.reports.show', [
            'report' => $report,
            'filter' => $filter,
            'reportLogs' => $reportLogs,
            'trademarks' => Trademark::all(),
            'batches' => Batch::all(),
            'suppliers' => $articlesService->getAllArticlesSuppliers(),
            'seasons' => $articlesService->getAllArticlesSeasons(),
            'warehouseStatuses' => $statusesService->allWarehouseStatuses(),
            'nextAction' => $reportsService->nextReportAction($report),
            'claims' => $claims,
            'cdsArticle' => $cdsArticle ?? null
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(
        UpdateReportRequest $request,
        NotificationsService $notificationsService,
        Report $report
    ) {
        $this->authorize('increaseReportState', [$report, $request->status_id]);
        $validated = (object)$request->validated();


        switch ((int)$request->status_id) {
            case Status::REPORT_SENT_FOR_CHECK:
                $notificationsService->sendMails(NewReportForCheckMail::class,$report, $validated->note);
//                Mail::to(config('emailAddresses.claims_logistics'))
//                    ->locale('sk')
//                    ->send(new NewReportForCheckMail($report, $validated->note));
                request()->session()->flash('status-success', __('reports.reportWasUpdated'));
                $report->fill(['status_id' => Status::REPORT_SENT_FOR_CHECK])->save();
                break;
            case Status::REPORT_CHECKED :
                $user = [['email'=>$report->creator->email, 'lang'=>'sk']];
                $notificationsService->recipients($user)->sendMails(ReportCheckedMail::class, $report, $validated->note);
//                Mail::to($report->creator->email)
//                    ->locale('sk')
//                    ->send(new ReportCheckedMail($report, $validated->note));
                request()->session()->flash('status-success', __('reports.reportWasUpdated'));
                $report->fill(['status_id' => Status::REPORT_CHECKED])->save();
                break;
            case Status::REPORT_CREDITED :
                $warehouseDocumentsService = new WarehouseDocumentsService($report);
                $RequestWarehouseExpenses = explode(',', $request->warehouse_expense_numbers);
                $warehouseExpenses = collect();
                try {
                    foreach ($RequestWarehouseExpenses as $warehouseExpenseNumber) {
                        $warehouseExpenses->push(
                            $warehouseDocumentsService->getWarehouseExpense($warehouseExpenseNumber)
                        );
                    }
                } catch (\Throwable $e) {
                    return redirect()->back()->withErrors(json_decode($e->getMessage()));
                }

                $report->fill([
                    'status_id' => Status::REPORT_BEING_PROCESSED,
                    'warehouse_expense_numbers' => implode(',', $RequestWarehouseExpenses)
                ])->save();

                ClaimsCreditedJob::dispatch($report, $warehouseExpenses, auth()->id());

                break;
            case Status::REPORT_CLOSED:
                $report->fill(['status_id' => Status::REPORT_CLOSED])->save();
                request()->session()->flash('status-success', __('reports.reportWasClosed'));
                break;
            default:
                $report->fill(['status_id' => $request->status_id])->save();
                request()->session()->flash('status-success', __('reports.reportWasUpdated'));
                break;
        }

        $report->logs()->create([
            'status_id' => $request->status_id,
            'action_user_id' => auth()->id(),
            'action_type' => 'updated',
            'action' => 'reportWasUpdated',
            'note' => $validated->note

        ]);

        return redirect(route('warehouse.reports.show', $report->id));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Report $report, FileService $fileService)
    {
        $this->authorize('deleteReport', $report);
        $logs = [];
        $userId = auth()->id();

        foreach ($report->claims()->get() as $claim) {
            $logs[] = [
                'claim_responsible_person_id' => $claim->claim_responsible_person_id,
                'claim_id' => $claim->id,
                'action_user_id' => $userId,
                'status_id' => $claim->claim_status_id,
                'action_type' => 'updated',
                'action' => 'reportWasDeleted',
                'created_at' => now(),
                'updated_at' => now()
            ];
        }
        ClaimHistoryLog::insert($logs);

        $report->claims()
            ->update([
                'report_id' => null,
                'warehouse_status_id' => Status::WAREHOUSE_STATUS_STORED_IN_BATCH
            ]);

        $report->logs()->delete();
        Storage::disk($fileService->getFilesDisk())->deleteDirectory("reports/report-$report->id");
        $report->reportFiles()->delete();
        $report->delete();
        return redirect(route('warehouse.reports.index'))->with('status-success', __('reports.reportWasDeleted'));
    }
}
