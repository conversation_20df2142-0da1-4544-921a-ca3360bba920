<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class LocateApp
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse) $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $lang = auth()->user()->lang ?? config('app.locale');

        \App::setLocale($lang);

        return $next($request);
    }
}
