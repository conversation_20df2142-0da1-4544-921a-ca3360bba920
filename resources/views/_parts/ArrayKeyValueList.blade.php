{{-- Enhanced Professional Array Key-Value Display Component --}}
@foreach($list as $attr => $value)
    @php
        $isNestedArray = is_array($value) && !isset($value['old']) && !isset($value['new']);
        $hasOldOrNew = is_array($value) && (isset($value['old']) || isset($value['new']));
        $old = $hasOldOrNew ? ($value['old'] ?? null) : null;
        $new = $hasOldOrNew ? ($value['new'] ?? null) : null;
        $hasChanges = $hasOldOrNew && !empty($old);
    @endphp

    <div class="array-item {{ $isNestedArray ? 'nested-section' : 'data-row' }} mb-3">
        
        @if($isNestedArray)
            {{-- Nested Array Section Header --}}
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white py-2">
                    <h6 class="mb-0 d-flex align-items-center">
                        <i class="fas fa-folder-open me-2"></i>
                        {{ $attr }}
                    </h6>
                </div>
                <div class="card-body p-3">
                    <div class="nested-content">
                        @include('_parts.ArrayKeyValueList', ['list' => $value])
                    </div>
                </div>
            </div>

        @else
            {{-- Data Row --}}
            <div class="card border-0 shadow-sm bg-light">
                <div class="card-body py-3 px-4">
                    <div class="row align-items-center">
                        
                        {{-- Attribute Label --}}
                        <div class="col-md-3 col-12 mb-2 mb-md-0">
                            <div class="attribute-label">
                                <span class="badge bg-secondary bg-opacity-10 text-dark fw-semibold px-3 py-2 rounded-pill">
                                    {{ $attr }}
                                </span>
                            </div>
                        </div>

                        {{-- Value Content --}}
                        <div class="col-md-9 col-12">
                            @if($hasOldOrNew)
                                <div class="value-comparison">
                                    @if($hasChanges)
                                        {{-- Show Old vs New Values --}}
                                        <div class="row g-3">
                                            <div class="col-lg-6">
                                                <div class="value-box old-value border-start border-4 border-danger ps-3">
                                                    <div class="value-label mb-1">
                                                        <small class="text-muted fw-semibold">
                                                            <i class="fas fa-minus-circle text-danger me-1"></i>
                                                            Previous Value
                                                        </small>
                                                    </div>
                                                    <div class="value-content">
                                                        @if(is_array($old))
                                                            <pre class="bg-light p-2 rounded small mb-0"><code>{{ json_encode($old, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</code></pre>
                                                        @else
                                                            <span class="text-muted">{{ $old ?: 'Empty' }}</span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <div class="value-box new-value border-start border-4 border-success ps-3">
                                                    <div class="value-label mb-1">
                                                        <small class="text-muted fw-semibold">
                                                            <i class="fas fa-plus-circle text-success me-1"></i>
                                                            Current Value
                                                        </small>
                                                    </div>
                                                    <div class="value-content">
                                                        @if(is_array($new))
                                                            <pre class="bg-light p-2 rounded small mb-0"><code>{{ json_encode($new, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</code></pre>
                                                        @else
                                                            <span class="fw-semibold text-success">{{ $new ?: 'Empty' }}</span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @else
                                        {{-- Show Only New Value --}}
                                        <div class="value-box new-only border-start border-4 border-info ps-3">
                                            <div class="value-label mb-1">
                                                <small class="text-muted fw-semibold">
                                                    <i class="fas fa-info-circle text-info me-1"></i>
                                                    Value
                                                </small>
                                            </div>
                                            <div class="value-content">
                                                @if(is_array($new))
                                                    <pre class="bg-light p-2 rounded small mb-0"><code>{{ json_encode($new, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</code></pre>
                                                @else
                                                    <span class="fw-semibold">{{ $new ?: 'Empty' }}</span>
                                                @endif
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            @else
                                {{-- Simple Value Display --}}
                                <div class="simple-value border-start border-4 border-primary ps-3">
                                    <div class="value-content">
                                        @if(is_array($value))
                                            <pre class="bg-light p-2 rounded small mb-0"><code>{{ json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</code></pre>
                                        @else
                                            <span class="fw-semibold">{{ $value ?: 'Empty' }}</span>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
@endforeach

{{-- Custom Styles --}}
<style>
.array-item {
    transition: all 0.3s ease;
}

.array-item:hover {
    transform: translateY(-2px);
}

.nested-section {
    border-left: 4px solid #007bff;
    padding-left: 1rem;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.value-box {
    background: rgba(248, 249, 250, 0.5);
    border-radius: 0.375rem;
    padding: 0.75rem;
    transition: all 0.2s ease;
}

.value-box:hover {
    background: rgba(248, 249, 250, 0.8);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.attribute-label .badge {
    font-size: 0.875rem;
    letter-spacing: 0.025em;
    min-width: 120px;
    text-align: center;
}

.value-content pre {
    background: #f8f9fa !important;
    border: 1px solid #e9ecef;
    font-size: 0.8rem;
    max-height: 200px;
    overflow-y: auto;
}

.value-content pre::-webkit-scrollbar {
    width: 6px;
}

.value-content pre::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.value-content pre::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.value-content pre::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

@media (max-width: 768px) {
    .nested-section {
        border-left: none;
        border-top: 4px solid #007bff;
        padding-left: 0;
        padding-top: 1rem;
    }
    
    .attribute-label .badge {
        min-width: auto;
        width: 100%;
    }
    
    .value-comparison .row {
        --bs-gutter-x: 0.5rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .array-item .card {
        background-color: #2d3748 !important;
        border-color: #4a5568 !important;
    }
    
    .value-box {
        background: rgba(45, 55, 72, 0.5) !important;
    }
    
    .value-content pre {
        background: #1a202c !important;
        border-color: #4a5568 !important;
        color: #e2e8f0 !important;
    }
}
</style>