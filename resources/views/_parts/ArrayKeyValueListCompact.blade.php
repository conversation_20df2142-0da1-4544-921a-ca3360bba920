{{-- Compact Professional Array Key-Value Display Component --}}
@php
    $maxDepth = $maxDepth ?? 5; // Maximum recursion depth
    $currentDepth = $currentDepth ?? 0; // Current recursion level
@endphp

<div class="array-display-compact">
    @if($currentDepth < $maxDepth)
    @foreach($list as $attr => $value)
        @php
            $isNestedArray = is_array($value) && !isset($value['old']) && !isset($value['new']);
            $hasOldOrNew = is_array($value) && (isset($value['old']) || isset($value['new']));
            $old = $hasOldOrNew ? ($value['old'] ?? null) : null;
            $new = $hasOldOrNew ? ($value['new'] ?? null) : null;
            $hasChanges = $hasOldOrNew && !empty($old);
        @endphp

        @if($isNestedArray)
            {{-- Nested Array Section --}}
            <div class="accordion-item border-0 mb-2">
                <h6 class="accordion-header">
                    <button class="accordion-button collapsed bg-light border rounded py-2 px-3" type="button" 
                            data-bs-toggle="collapse" data-bs-target="#collapse-{{ Str::slug($attr) }}" 
                            aria-expanded="false">
                        <i class="fas fa-folder me-2 text-primary"></i>
                        <span class="fw-semibold">{{ $attr }}</span>
                        <span class="badge bg-primary bg-opacity-10 text-primary ms-auto">
                            {{ count($value) }} items
                        </span>
                    </button>
                </h6>
                <div id="collapse-{{ Str::slug($attr) }}" class="accordion-collapse collapse">
                    <div class="accordion-body bg-light bg-opacity-50 p-3">
                        @include('_parts.ArrayKeyValueListCompact', [
                            'list' => $value, 
                            'maxDepth' => $maxDepth,
                            'currentDepth' => $currentDepth + 1
                        ])
                    </div>
                </div>
            </div>

        @else
            {{-- Data Row --}}
            <div class="data-row border-bottom py-2 px-1">
                <div class="row align-items-center g-2">
                    
                    {{-- Attribute Label --}}
                    <div class="col-sm-4 col-12">
                        <span class="fw-semibold text-secondary small text-uppercase tracking-wide">
                            {{ $attr }}
                        </span>
                    </div>

                    {{-- Value Content --}}
                    <div class="col-sm-8 col-12">
                        @if($hasOldOrNew)
                            @if($hasChanges)
                                {{-- Changed Value --}}
                                <div class="d-flex flex-wrap align-items-center gap-2">
                                    <div class="old-value">
                                        <span class="badge bg-danger bg-opacity-10 text-danger small">
                                            <i class="fas fa-minus fa-xs me-1"></i>
                                            @if(is_array($old))
                                                [{{ count($old) }} items]
                                            @else
                                                {{ Str::limit($old ?: 'Empty', 30) }}
                                            @endif
                                        </span>
                                    </div>
                                    <i class="fas fa-arrow-right text-muted fa-sm"></i>
                                    <div class="new-value">
                                        <span class="badge bg-success bg-opacity-10 text-success small fw-semibold">
                                            <i class="fas fa-plus fa-xs me-1"></i>
                                            @if(is_array($new))
                                                [{{ count($new) }} items]
                                            @else
                                                {{ Str::limit($new ?: 'Empty', 30) }}
                                            @endif
                                        </span>
                                    </div>
                                    @if(is_array($old) || is_array($new))
                                        <button class="btn btn-sm btn-outline-secondary py-0 px-2" type="button" 
                                                data-bs-toggle="modal" data-bs-target="#valueModal-{{ Str::slug($attr) }}">
                                            <i class="fas fa-eye fa-xs"></i>
                                        </button>
                                    @endif
                                </div>
                            @else
                                {{-- New Value Only --}}
                                <div class="new-value-only">
                                    @if(is_array($new))
                                        <span class="badge bg-info bg-opacity-10 text-info fw-semibold">
                                            <i class="fas fa-list fa-xs me-1"></i>
                                            [{{ count($new) }} items]
                                        </span>
                                        <button class="btn btn-sm btn-outline-secondary py-0 px-2 ms-1" type="button" 
                                                data-bs-toggle="modal" data-bs-target="#valueModal-{{ Str::slug($attr) }}">
                                            <i class="fas fa-eye fa-xs"></i>
                                        </button>
                                    @else
                                        <span class="fw-semibold">{{ $new ?: 'Empty' }}</span>
                                    @endif
                                </div>
                            @endif
                        @else
                            {{-- Simple Value --}}
                            <div class="simple-value">
                                @if(is_array($value))
                                    <span class="badge bg-primary bg-opacity-10 text-primary fw-semibold">
                                        <i class="fas fa-list fa-xs me-1"></i>
                                        [{{ count($value) }} items]
                                    </span>
                                    <button class="btn btn-sm btn-outline-secondary py-0 px-2 ms-1" type="button" 
                                            data-bs-toggle="modal" data-bs-target="#valueModal-{{ Str::slug($attr) }}">
                                        <i class="fas fa-eye fa-xs"></i>
                                    </button>
                                @else
                                    <span class="fw-semibold">{{ $value ?: 'Empty' }}</span>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>

                {{-- Modal for Array Values --}}
                @if(is_array($value) || is_array($old) || is_array($new))
                    <div class="modal fade" id="valueModal-{{ Str::slug($attr) }}" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h6 class="modal-title">{{ $attr }} - Detailed View</h6>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    @if($hasChanges)
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <h6 class="text-danger">
                                                    <i class="fas fa-minus-circle me-1"></i>
                                                    Previous Value
                                                </h6>
                                                <pre class="bg-light p-3 rounded small"><code>{{ json_encode($old, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</code></pre>
                                            </div>
                                            <div class="col-md-6">
                                                <h6 class="text-success">
                                                    <i class="fas fa-plus-circle me-1"></i>
                                                    Current Value
                                                </h6>
                                                <pre class="bg-light p-3 rounded small"><code>{{ json_encode($new, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</code></pre>
                                            </div>
                                        </div>
                                    @else
                                        <pre class="bg-light p-3 rounded small mb-0"><code>{{ json_encode($hasOldOrNew ? $new : $value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</code></pre>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        @endif
    @endforeach
    
    @else
        {{-- Max depth reached warning --}}
        <div class="alert alert-warning border-0 py-1 px-2 mb-2">
            <small class="text-muted">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Maximum nesting depth reached.
            </small>
        </div>
    @endif
</div>

{{-- Compact Styles --}}
<style>
.array-display-compact {
    font-size: 0.875rem;
}

.tracking-wide {
    letter-spacing: 0.05em;
}

.data-row {
    transition: background-color 0.2s ease;
    border-bottom: 1px solid rgba(0,0,0,0.08) !important;
}

.data-row:hover {
    background-color: rgba(0,123,255,0.05);
}

.data-row:last-child {
    border-bottom: none !important;
}

.accordion-button {
    font-size: 0.875rem;
    box-shadow: none !important;
    border: 1px solid #dee2e6 !important;
}

.accordion-button:not(.collapsed) {
    background-color: #e7f3ff !important;
    border-color: #007bff !important;
}

.accordion-button:focus {
    border-color: #007bff;
}

.badge {
    font-size: 0.75rem;
}

.modal pre {
    max-height: 400px;
    overflow-y: auto;
    background: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
}

.modal pre::-webkit-scrollbar {
    width: 6px;
}

.modal pre::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.modal pre::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

@media (max-width: 576px) {
    .array-display-compact {
        font-size: 0.8rem;
    }
    
    .data-row .row {
        --bs-gutter-x: 0.5rem;
    }
    
    .old-value, .new-value {
        width: 100%;
    }
    
    .d-flex.flex-wrap {
        flex-direction: column !important;
        align-items: flex-start !important;
    }
}
</style>

